/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service;

import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.dto.ProductInfoDTO;
import com.fulfillmen.shop.domain.req.AggregateSearchReq;
import com.fulfillmen.shop.domain.vo.ProductDetailVO;
import com.fulfillmen.shop.domain.vo.ProductInfoVO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 搜索产品业务
 *
 * <AUTHOR>
 * @date 2025/2/25 18:58
 * @description: 商品搜索服务接口
 * @since 1.0.0
 */
public interface IProductService {

    /**
     * 获取商品详情（前端VO）
     * <pre>
     * 可以是 product_spu id
     * 或者 pdc_product_mapping id
     * 或 offerId
     * </pre>
     *
     * @param productId 商品ID
     * @return 商品详情（包含中英文字段）
     */
    ProductDetailVO getProductDetailVO(Long productId);

    /**
     * 重新同步商品详情（前端VO）
     *
     * @param id 商品ID || 平台产品ID
     * @return 商品详情（包含中英文字段）
     */
    ProductDetailVO reSyncProductDetailVO(Long id);

    /**
     * 上传图片获取图片ID
     *
     * <pre>
     * 1. 图片文件大小限制在 2MB 以内
     * 2. 仅支持图片格式文件
     * </pre>
     *
     * @param file 图片文件
     * @return 图片ID
     */
    String uploadImage(MultipartFile file);

    /**
     * 清理产品详情缓存
     *
     * @param platformProductId 平台产品ID，null表示清理所有
     * @return 清理结果描述
     */
    String clearProductDetailCache(String platformProductId);

    /**
     * 搜索相似商品（前端VO）
     *
     * @param imagesUrl 图片URL
     * @param pageIndex 页码
     * @param pageSize  每页大小
     * @return 搜索结果（分页）
     */
    @Deprecated
    PageDTO<ProductInfoVO> searchSimilarProductsVO(String imagesUrl, Integer pageIndex, Integer pageSize);

    /**
     * 搜索相似商品 并同步进库
     *
     * @param offerId   商品ID
     * @param pageIndex 页码
     * @param pageSize  每页大小
     * @return 搜索结果
     */
    PageDTO<ProductInfoDTO> searchSimilarProductsSyncDb(Long offerId, Integer pageIndex, Integer pageSize);

    /**
     * 统一聚合搜索商品 - 优化版本 🚀
     *
     * <pre>
     * 🎯 这是新的统一搜索入口，替代以下废弃方法：
     * - aggregateSearch() - 聚合搜索
     * - aggregateSearchVO() - 聚合搜索VO版本
     * - aggregateSearchPdcProductMapping() - 聚合搜索DTO版本
     * - searchPdcProductMappingByKeyword() - 关键词搜索
     *
     * 🔥 支持多种搜索方式的统一入口：
     * 1. 关键词搜索 (searchType = 1)
     * 2. 图片搜索 (searchType = 2)
     * 3. 混合搜索 (同时提供关键词和图片)
     *
     * ⚡ 特性：
     * - 智能缓存管理，大幅提升响应速度
     * - 统一的数据同步逻辑
     * - 自动错误处理和降级
     * - 性能监控统计
     *
     * 💡 使用建议：
     * - 推荐 useCache = true，利用缓存提升用户体验
     * - 管理后台可设置 useCache = false，获取最新数据
     * </pre>
     *
     * @param request  聚合搜索请求参数
     * @param useCache 是否使用缓存，true=使用缓存，false=跳过缓存，推荐true
     * @return 商品信息列表（前端VO格式）
     */
    PageDTO<ProductInfoVO> unifiedAggregateSearch(AggregateSearchReq request, Boolean useCache);

}
