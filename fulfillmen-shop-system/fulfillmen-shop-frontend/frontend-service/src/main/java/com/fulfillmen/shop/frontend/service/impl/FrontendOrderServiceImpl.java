/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service.impl;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fulfillmen.shop.common.context.OrderContext;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.common.util.I18nMessageUtils;
import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.dao.mapper.TzProductSkuMapper;
import com.fulfillmen.shop.dao.mapper.TzProductSpuMapper;
import com.fulfillmen.shop.dao.mapper.TzShoppingCartMapper;
import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.dto.order.OrderItemInfo;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.req.OrderReq.CancelOrderRequest;
import com.fulfillmen.shop.domain.req.OrderReq.ConfirmReceiptRequest;
import com.fulfillmen.shop.domain.req.OrderReq.CreateOrderReq;
import com.fulfillmen.shop.domain.req.OrderReq.CreateOrderSubmitReq;
import com.fulfillmen.shop.domain.req.OrderReq.OrderPreviewReq;
import com.fulfillmen.shop.domain.req.OrderReq.RefundApplicationRequest;
import com.fulfillmen.shop.domain.req.OrderReq.RefundApplicationVO;
import com.fulfillmen.shop.domain.vo.OrderPreviewVO;
import com.fulfillmen.shop.domain.vo.OrderSubmitVO;
import com.fulfillmen.shop.domain.vo.UserOrderDetailVO;
import com.fulfillmen.shop.domain.vo.UserOrderDetailVO.TrackingInfo;
import com.fulfillmen.shop.domain.vo.UserPurchaseOrderListVO;
import com.fulfillmen.shop.frontend.cache.PreviewCacheData;
import com.fulfillmen.shop.frontend.convert.FrontendOrderConvert;
import com.fulfillmen.shop.frontend.convert.FrontendOrderPurchaseConvert;
import com.fulfillmen.shop.frontend.service.IFrontendOrderService;
import com.fulfillmen.shop.manager.core.order.OrderEventPublisher;
import com.fulfillmen.shop.manager.core.order.event.CreatePurchaseOrderSyncWmsHandler;
import com.fulfillmen.shop.manager.core.order.helper.OrderContextHelper;
import com.fulfillmen.shop.manager.core.repository.TzOrderPurchaseRepository;
import com.fulfillmen.shop.manager.support.alibaba.IOrderManager;
import com.fulfillmen.starter.cache.redisson.util.RedisUtils;
import com.fulfillmen.starter.core.validation.ValidationUtils;
import com.fulfillmen.support.alibaba.api.request.model.TradeFastAddress;
import com.fulfillmen.support.alibaba.api.request.model.TradeFastCargo;
import com.fulfillmen.support.alibaba.api.request.order.OrderPreviewRequestRecord;
import com.fulfillmen.support.alibaba.api.response.order.OrderPreviewResponse;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单服务实现
 *
 * <AUTHOR>
 * @date 2025/6/23
 * @description 订单服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FrontendOrderServiceImpl implements IFrontendOrderService {

    /**
     * Redis中用于存储订单预览幂等Token的Key前缀
     */
    private static final String ORDER_IDEMPOTENT_TOKEN_KEY_PREFIX = "order:idempotent:";
    /**
     * 订单预览缓存的过期时间（分钟）
     */
    private static final long ORDER_PREVIEW_CACHE_EXPIRATION_MINUTES = 30;

    private final IOrderManager orderManager;

    private final TzProductSpuMapper productSpuMapper;
    private final TzProductSkuMapper productSkuMapper;
    private final TzOrderPurchaseRepository orderPurchaseRepository;
    private final TzOrderItemMapper tzOrderItemMapper;
    private final TzOrderSupplierMapper orderSupplierMapper;

    private final TzShoppingCartMapper shoppingCartMapper;

    private final FrontendOrderConvert frontendOrderConvert;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    // 用于订单事件处理
    private final OrderEventPublisher orderEventPublisher;
    // WMS同步处理器
    private final CreatePurchaseOrderSyncWmsHandler wmsHandler;

    /**
     * 订单预览。
     * <p>
     * 此方法处理订单预览的请求，包括以下步骤： 1. 获取并验证商品信息。 2. 并发调用1688的预览接口，以获取包括运费在内的最新价格。 3.
     * 将预览结果进行汇总。 4. 生成幂等令牌并将预览数据缓存，以备后续提交订单使用。
     *
     * @param orderPreviewReq 订单预览请求
     * @return 订单预览结果
     */
    @Override
    public OrderPreviewVO previewOrder(OrderPreviewReq orderPreviewReq) {
        log.info("开始处理订单预览请求，商品数量: {}", orderPreviewReq.getProductList().size());

        // 1. 获取当前用户信息和请求的商品
        Long userId = UserContextHolder.getUserId();
        Map<Long, Integer> skuQuantityMap = orderPreviewReq.getProductList().stream()
                .collect(Collectors.toMap(CreateOrderReq::getSkuId, CreateOrderReq::getProductQuantity));

        // 2. 从数据库获取商品和SKU信息
        List<TzProductSku> productSkuList = getProductSkus(new ArrayList<>(skuQuantityMap.keySet()));
        if (CollectionUtils.isEmpty(productSkuList)) {
            log.warn("未找到任何SKU信息，skuIds: {}", skuQuantityMap.keySet());
            return buildEmptyOrderPreviewVO();
        }
        Map<Long, TzProductSpu> spuMap = getProductSpus(productSkuList);

        // 3. 验证商品是否满足最小起批量
        OrderContextHelper.checkSpuMinPurchaseQuantity(productSkuList, skuQuantityMap, spuMap);

        // 4. 按供应商分组
        Map<String, List<OrderItemInfo>> supplierGroupMap = OrderContextHelper.groupBySupplier(productSkuList, spuMap,
                skuQuantityMap);
        log.info("按供应商分组完成，供应商数量: {}", supplierGroupMap.size());

        // 5. 并行调用1688订单预览接口，获取包含运费的精确价格
        // 使用CompletableFuture实现并行处理，可以显著提高多供应商场景下的预览性能。
        List<CompletableFuture<FrontendOrderConvert.SupplierPreviewResult>> futures = new ArrayList<>();
        for (Map.Entry<String, List<OrderItemInfo>> entry : supplierGroupMap.entrySet()) {
            String supplierId = entry.getKey();
            List<OrderItemInfo> items = entry.getValue();

            CompletableFuture<FrontendOrderConvert.SupplierPreviewResult> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return callSupplierOrderPreview(userId, supplierId, items);
                } catch (Exception e) {
                    log.error("调用 1688 API 订单预览失败: supplierId=[{}]", supplierId, e);
                    return buildFailedSupplierPreviewResult(supplierId, items, e.getMessage());
                }
            }, threadPoolTaskExecutor);
            futures.add(future);
        }

        // 6. 等待所有异步调用完成
        List<FrontendOrderConvert.SupplierPreviewResult> supplierResults = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());

        // 7. 使用转换器合并结果并构建返回对象
        OrderPreviewVO result = frontendOrderConvert.convertToOrderPreviewVO(supplierResults);

        // 8. 生成幂等令牌并缓存预览数据
        generateAndCachePreviewData(userId, orderPreviewReq, result);

        log.info("订单预览处理完成，总金额: {}元, 令牌: {}", result.getPriceDetails().getTotalAmount(), result.getIdempotentToken());

        return result;
    }

    /**
     * 提交订单。
     * <p>
     * 此方法处理最终的订单提交请求。主要步骤如下： 1. 验证幂等令牌，防止重复提交。 2. 从缓存中获取用户已确认的预览数据。 3.
     * 再次从数据库获取商品信息并进行校验，确保数据在预览到提交期间未发生变更。 4. 使用OrderContextHelper构建完整的订单实体。 5.
     * 将订单数据持久化到数据库。 6.
     * 触发订单创建的后续事件（如通知、调用三方API等）。 7. 清理购物车和幂等令牌。
     *
     * @param orderSubmitReq 订单提交请求
     * @return 订单提交结果
     */
    @Override
    public OrderSubmitVO submitOrder(CreateOrderSubmitReq orderSubmitReq) {
        log.info("开始处理订单提交请求，幂等令牌: {}", orderSubmitReq.getIdempotentToken());
        try {
            // 1. 验证幂等令牌并从缓存中获取预览数据
            PreviewCacheData cacheData = validateIdempotentToken(orderSubmitReq.getIdempotentToken());

            // 2. 获取当前用户信息和请求的商品
            Long userId = UserContextHolder.getUserId();
            Map<Long, Integer> skuQuantityMap = cacheData.getOrderPreviewReq().getProductList().stream()
                    .collect(Collectors.toMap(CreateOrderReq::getSkuId, CreateOrderReq::getProductQuantity));

            // 3. 从数据库获取商品和SKU信息
            List<TzProductSku> productSkuList = getProductSkus(new ArrayList<>(skuQuantityMap.keySet()));
            if (CollectionUtils.isEmpty(productSkuList)) {
                log.warn("提交订单时未找到任何SKU信息，skuIds: {}", skuQuantityMap.keySet());
                throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.RESOURCE_NOT_FOUND);
            }
            Map<Long, TzProductSpu> spuMap = getProductSpus(productSkuList);

            // 4. 再次验证商品是否满足最小起批量，以防止在预览到提交的间隙，商品规则发生变化。
            OrderContextHelper.checkSpuMinPurchaseQuantity(productSkuList, skuQuantityMap, spuMap);

            // 5. 构建订单上下文，包含所有需要创建的订单实体
            List<Long> shoppingCartIds = cacheData.getShoppingCartIds() != null
                    && !cacheData.getShoppingCartIds().isEmpty()
                            ? Arrays.stream(cacheData.getShoppingCartIds().split(","))
                                    .map(Long::parseLong).collect(Collectors.toList())
                            : Collections.emptyList();

            // 从缓存数据中获取预览VO，包含价格比较和计算需要的信息
            OrderPreviewVO previewVO = cacheData.getOrderPreviewVO();
            // 构建订单上下文
            OrderContext orderContext = OrderContextHelper.buildContext(orderSubmitReq, userId, skuQuantityMap,
                    productSkuList, spuMap, shoppingCartIds, previewVO);

            // 6. 保存订单实体到数据库（包含状态管理系统初始化）
            orderPurchaseRepository.createPurchaseOrder(orderContext);
            log.info("订单创建成功，订单号: {}, 初始状态: {}",
                    orderContext.getPurchaseOrder().getPurchaseOrderNo(),
                    orderContext.getPurchaseOrder().getOrderStatus().getDescription());

            // 7. 发布订单创建事件处理
            // MODIFY: 2025/7/9 修改成事件机制发布
            orderEventPublisher.publishOrderCreatedEvent(orderContext);

            // 8. 记录状态管理系统的初始化信息
            logOrderStatusInitialization(orderContext);

            // 9. 构建返回结果
            return OrderContextHelper.buildOrderSubmitResponse(orderContext);
        } finally {
            // 10. 删除幂等令牌
            deleteIdempotentToken(orderSubmitReq.getIdempotentToken());
        }
    }

    /**
     * 根据SKU ID列表批量获取SKU信息。
     *
     * @param skuIds SKU ID列表
     * @return SKU信息列表
     */
    private List<TzProductSku> getProductSkus(List<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        return productSkuMapper.selectByIds(skuIds);
    }

    /**
     * 根据SKU列表批量获取其关联的SPU信息。
     *
     * @param productSkuList SKU列表
     * @return SPU信息的Map，键为SPU ID
     */
    private Map<Long, TzProductSpu> getProductSpus(List<TzProductSku> productSkuList) {
        if (CollectionUtils.isEmpty(productSkuList)) {
            return Collections.emptyMap();
        }
        Set<Long> spuIds = productSkuList.stream().map(TzProductSku::getSpuId).collect(Collectors.toSet());
        return productSpuMapper.selectByIds(spuIds).stream()
                .collect(Collectors.toMap(TzProductSpu::getId, spu -> spu));
    }

    /**
     * 调用单个供应商的订单预览接口。
     *
     * @param userId     用户ID
     * @param supplierId 供应商ID
     * @param items      该供应商下的商品项
     * @return 供应商预览结果
     */
    private FrontendOrderConvert.SupplierPreviewResult callSupplierOrderPreview(Long userId, String supplierId,
            List<OrderItemInfo> items) {
        log.debug("调用供应商{}订单预览，商品数量: {}", supplierId, items.size());

        // 构建收货地址 - 使用默认地址
        TradeFastAddress address = OrderContextHelper.buildDefaultAddress(userId);

        // 构建商品列表
        List<TradeFastCargo> cargoList = OrderContextHelper.buildCargoList(items);

        // 构建预览请求
        OrderPreviewRequestRecord request = OrderPreviewRequestRecord.of(address, cargoList, "general");

        // 调用OrderManager预览接口
        OrderPreviewResponse response = orderManager.previewOrder(request);

        return FrontendOrderConvert.SupplierPreviewResult.builder()
                .supplierId(supplierId)
                .supplierName(items.getFirst().getSpu().getSourcePlatformSellerName())
                .items(items)
                .response(response)
                .success(response != null && response.getSuccess())
                .errorMessage(null)
                .build();
    }

    /**
     * 当供应商预览接口调用失败时，构建一个表示失败的预览结果。
     *
     * @param supplierId   供应商ID
     * @param items        商品项
     * @param errorMessage 错误信息
     * @return 失败的供应商预览结果
     */
    private FrontendOrderConvert.SupplierPreviewResult buildFailedSupplierPreviewResult(String supplierId,
            List<OrderItemInfo> items, String errorMessage) {
        return FrontendOrderConvert.SupplierPreviewResult.builder()
                .supplierId(supplierId)
                .supplierName(items.isEmpty() ? "未知供应商" : items.getFirst().getSpu().getSourcePlatformSellerName())
                .items(items)
                .response(null)
                .success(false)
                .errorMessage(errorMessage)
                .build();
    }

    /**
     * 验证幂等令牌的有效性。 如果令牌不存在或已过期，则抛出异常。
     *
     * @param idempotentToken 幂等令牌
     * @return 缓存的预览数据
     */
    private PreviewCacheData validateIdempotentToken(String idempotentToken) {
        String tokenKey = ORDER_IDEMPOTENT_TOKEN_KEY_PREFIX + idempotentToken;
        PreviewCacheData cacheData = RedisUtils.<PreviewCacheData>get(tokenKey).orElse(null);
        ValidationUtils.throwIf(cacheData == null,
                I18nMessageUtils.getMessage(FulfillmenErrorCodeEnum.ORDER_PREVIEW_TOKEN_EXPIRED.getI18nKey()));
        return cacheData;
    }

    /**
     * 在订单处理完成后，删除幂等令牌。
     *
     * @param idempotentToken 待删除的幂等令牌
     */
    private void deleteIdempotentToken(String idempotentToken) {
        String tokenKey = ORDER_IDEMPOTENT_TOKEN_KEY_PREFIX + idempotentToken;
        RedisUtils.delete(tokenKey);
    }

    /**
     * 生成幂等令牌，并将完整的预览数据缓存到Redis中。
     *
     * @param userId          用户ID
     * @param orderPreviewReq 原始的预览请求
     * @param result          生成的预览结果VO
     */
    private void generateAndCachePreviewData(Long userId, OrderPreviewReq orderPreviewReq,
            OrderPreviewVO result) {
        // 生成幂等令牌
        String idempotentToken = UUID.randomUUID().toString().replace("-", "");
        LocalDateTime now = LocalDateTime.now();
        String tokenKey = ORDER_IDEMPOTENT_TOKEN_KEY_PREFIX + idempotentToken;

        // 填充VO中的缓存相关信息
        result.setIdempotentToken(idempotentToken);
        result.setCreateTime(now);
        result.setTokenExpiryTime(now.plusMinutes(ORDER_PREVIEW_CACHE_EXPIRATION_MINUTES));
        result.setIsShoppingCart(orderPreviewReq.getIsShoppingCart());
        result.setShoppingCartIds(orderPreviewReq.getShoppingCartIds());

        // 构建缓存数据
        PreviewCacheData cacheData = PreviewCacheData.builder()
                .userId(userId)
                .orderPreviewReq(orderPreviewReq)
                .orderPreviewVO(result)
                .createTime(now)
                .isShoppingCart(orderPreviewReq.getIsShoppingCart())
                .shoppingCartIds(orderPreviewReq.getShoppingCartIds())
                .build();

        // 设置缓存，并指定过期时间
        RedisUtils.set(tokenKey, cacheData, Duration.ofMinutes(ORDER_PREVIEW_CACHE_EXPIRATION_MINUTES));
        log.debug("生成订单预览令牌: {}, 用户: {}", idempotentToken, userId);
    }

    /**
     * 当无法找到任何商品时，构建一个表示错误的空订单预览VO。
     *
     * @return 空的订单预览VO
     */
    private OrderPreviewVO buildEmptyOrderPreviewVO() {
        return OrderPreviewVO.builder()
                .orderPreviewSummary(OrderPreviewVO.OrderPreviewSummary.builder()
                        .totalQuantity(0)
                        .productTypeCount(0)
                        .success(false)
                        .hasErrors(true)
                        .build())
                .productItems(Collections.emptyList())
                .priceDetails(OrderPreviewVO.PriceDetails.builder()
                        .merchandiseAmount(BigDecimal.ZERO)
                        .merchandiseAmountUsd(BigDecimal.ZERO)
                        .shippingAmount(BigDecimal.ZERO)
                        .shippingAmountUsd(BigDecimal.ZERO)
                        .serviceFee(BigDecimal.ZERO)
                        .serviceFeeUsd(BigDecimal.ZERO)
                        .serviceFeeRate(BigDecimal.ZERO)
                        .totalAmount(BigDecimal.ZERO)
                        .totalAmountUsd(BigDecimal.ZERO)
                        .build())
                .errors(Collections.singletonList(OrderPreviewVO.OrderPreviewError.builder()
                        .errorCode("PRODUCT_NOT_FOUND")
                        .errorMessage("未找到商品信息")
                        .errorType("VALIDATION")
                        .build()))
                .isShoppingCart(0)
                .shoppingCartIds(null)
                .build();
    }

    /**
     * 根据令牌查询预览信息
     *
     * @param idempotentToken 幂等令牌
     * @return 订单预览结果
     */
    @Override
    @SuppressWarnings("unchecked")
    public OrderPreviewVO getPreviewByToken(String idempotentToken) {
        String tokenKey = ORDER_IDEMPOTENT_TOKEN_KEY_PREFIX + idempotentToken;
        PreviewCacheData cacheData = RedisUtils.<PreviewCacheData>get(tokenKey).orElse(null);

        if (cacheData == null) {
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.ORDER_PREVIEW_TOKEN_NOT_FOUND);
        }

        OrderPreviewVO result = cacheData.getOrderPreviewVO();
        if (result == null) {
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.ORDER_PREVIEW_TOKEN_DATA_ERROR);
        }

        log.debug("查询订单预览令牌: {}", idempotentToken);
        return result;
    }

    @Override
    public PageDTO<UserPurchaseOrderListVO> getOrderList(int page, int size, Integer status, String keyword,
            LocalDateTime startTime, LocalDateTime endTime) {
        // 以采购单为准获取订单列表
        Page<TzOrderPurchase> purchasePage = new Page<>(page, size);
        LambdaQueryWrapper<TzOrderPurchase> queryWrapper = new LambdaQueryWrapper<TzOrderPurchase>()
                .eq(TzOrderPurchase::getBuyerId, UserContextHolder.getUserId())
                .eq(Objects.nonNull(status), TzOrderPurchase::getOrderStatus,
                        TzOrderPurchaseStatusEnum.getByValue(status))
                .like(StringUtils.hasText(keyword), TzOrderPurchase::getPurchaseOrderNo, keyword)
                // 添加时间范围筛选
                .between(startTime != null && endTime != null, TzOrderPurchase::getOrderDate, startTime, endTime)
                // 按下单时间倒序
                .orderByDesc(TzOrderPurchase::getOrderDate);

        log.info("查询条件：{}", queryWrapper.getTargetSql());
        // 分页获取
        orderPurchaseRepository.page(purchasePage, queryWrapper);
        log.info("查询结果：总数：{}， 当前页记录数：{}", purchasePage.getTotal(), purchasePage.getRecords().size());
        // 获取商品清单
        Map<Long, List<TzOrderItem>> orderItemsMapByOrderPurchaseId = tzOrderItemMapper
                .selectList(new LambdaQueryWrapper<TzOrderItem>()
                        .in(TzOrderItem::getPurchaseOrderId, purchasePage.getRecords().stream()
                                .map(TzOrderPurchase::getId).collect(Collectors.toList())))
                .stream()
                .collect(Collectors.groupingBy(TzOrderItem::getPurchaseOrderId, Collectors.toList()));

        // 组装
        PageDTO<UserPurchaseOrderListVO> userPurchaseOrderListPage = FrontendOrderPurchaseConvert.INSTANCE
                .convertToUserPurchaseOrderListVO(purchasePage);

        if (CollectionUtils.isEmpty(userPurchaseOrderListPage.getRecords())) {
            log.info("没有找到符合条件的结果，直接返回空结果");
            return userPurchaseOrderListPage;
        }

        // 创建采购订单ID到汇率的映射，用于订单项美元价格计算
        Map<Long, BigDecimal> exchangeRateMap = purchasePage.getRecords().stream()
                .collect(Collectors.toMap(TzOrderPurchase::getId,
                        tzOrderPurchase -> tzOrderPurchase.getExchangeRateSnapshot() == null ? BigDecimal.ONE
                                : tzOrderPurchase.getExchangeRateSnapshot(),
                        (existing, replacement) -> existing));

        userPurchaseOrderListPage.getRecords().forEach(purchaseOrder -> {
            List<TzOrderItem> orderItems = orderItemsMapByOrderPurchaseId.get(purchaseOrder.getId());
            log.info("获取汇率：{}", exchangeRateMap.get(purchaseOrder.getId()));
            if (!CollectionUtils.isEmpty(orderItems)) {
                BigDecimal exchangeRate = exchangeRateMap.get(purchaseOrder.getId());
                List<UserPurchaseOrderListVO.OrderItemInfo> orderItemInfoList = orderItems.stream()
                        .map(item -> FrontendOrderPurchaseConvert.INSTANCE.convertToOrderItemInfoWithExchangeRate(item,
                                exchangeRate))
                        .collect(Collectors.toList());
                purchaseOrder.setOrderItems(orderItemInfoList);
            }
        });
        return userPurchaseOrderListPage;
    }

    @Override
    public UserOrderDetailVO getOrderDetail(String orderNo) {
        // 根据采购单号获取订单详情信息
        TzOrderPurchase purchaseOrder = orderPurchaseRepository.getOne(new LambdaQueryWrapper<TzOrderPurchase>()
                .eq(TzOrderPurchase::getPurchaseOrderNo, orderNo)
                .eq(TzOrderPurchase::getBuyerId, UserContextHolder.getUserId()));

        // 判断订单是否存在
        if (purchaseOrder == null) {
            log.warn("订单不存在，订单号: {}", orderNo);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.RESOURCE_NOT_FOUND, "订单");
        }

        // 获取汇率
        Map<Long, BigDecimal> exchangeRateMap = Collections.singletonMap(purchaseOrder.getId(),
                purchaseOrder.getExchangeRateSnapshot());
        BigDecimal exchangeRate = exchangeRateMap.get(purchaseOrder.getId());

        // 转换商品列表
        List<TzOrderItem> orderItems = tzOrderItemMapper.selectList(new LambdaQueryWrapper<TzOrderItem>()
                .eq(TzOrderItem::getPurchaseOrderId, purchaseOrder.getId()));
        List<UserOrderDetailVO.OrderItemDetailInfo> orderItemDetailInfoList = orderItems.stream()
                .map(item -> FrontendOrderPurchaseConvert.INSTANCE.convertToOrderItemDetailInfoWithExchangeRate(item,
                        exchangeRate))
                .collect(Collectors.toList());

        log.info("订单详情获取成功，订单号: {}，商品数量: {}", orderNo, orderItemDetailInfoList.size());

        // 组装
        return FrontendOrderPurchaseConvert.INSTANCE.convertToUserOrderDetailVO(purchaseOrder, orderItemDetailInfoList);
    }

    @Override
    public TrackingInfo getOrderTracking(String orderNo) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getOrderTracking'");
    }

    @Override
    public void confirmReceipt(String orderNo, ConfirmReceiptRequest request) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'confirmReceipt'");
    }

    @Override
    public void cancelOrder(String orderNo, CancelOrderRequest request) {
        log.info("用户取消订单功能暂未实现，订单号: {}", orderNo);
        throw new UnsupportedOperationException("用户取消订单功能暂未实现");
    }

    @Override
    public RefundApplicationVO applyRefund(String orderNo, RefundApplicationRequest request) {
        log.info("用户申请退款功能暂未实现，订单号: {}", orderNo);
        throw new UnsupportedOperationException("用户申请退款功能暂未实现");
    }

    @Override
    public void syncOrderToWms(String orderNo) {
        log.info("开始同步采购订单到WMS，订单号: {}", orderNo);

        // 根据采购订单号查询订单信息
        OrderContext orderContext = buildOrderContextByOrderNo(orderNo);

        if (orderContext == null) {
            log.error("无法构建订单上下文，采购订单可能不存在: {}", orderNo);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.RESOURCE_NOT_FOUND, "采购订单");
        }

        // 调用WMS同步处理器进行重试同步
        wmsHandler.retrySyncToWms(orderContext);

        log.info("采购订单同步WMS完成，订单号: {}", orderNo);
    }

    /**
     * 根据采购订单号构建订单上下文
     *
     * @param orderNo 采购订单号
     * @return 订单上下文
     */
    private OrderContext buildOrderContextByOrderNo(String orderNo) {
        // 查询采购订单
        TzOrderPurchase purchaseOrder = orderPurchaseRepository.getOne(
                new LambdaQueryWrapper<TzOrderPurchase>()
                        .eq(TzOrderPurchase::getPurchaseOrderNo, orderNo)
                        .eq(TzOrderPurchase::getBuyerId, UserContextHolder.getUserId()));

        if (purchaseOrder == null) {
            log.warn("采购订单不存在，订单号: {}", orderNo);
            return null;
        }

        // 查询供应商订单
        List<TzOrderSupplier> supplierOrders = orderSupplierMapper.selectList(
                new LambdaQueryWrapper<TzOrderSupplier>()
                        .eq(TzOrderSupplier::getPurchaseOrderId, purchaseOrder.getId()));

        // 查询订单项
        List<TzOrderItem> orderItems = tzOrderItemMapper.selectList(
                new LambdaQueryWrapper<TzOrderItem>()
                        .eq(TzOrderItem::getPurchaseOrderId, purchaseOrder.getId()));

        // 构建订单上下文
        return OrderContext.builder()
                .purchaseOrder(purchaseOrder)
                .supplierOrders(supplierOrders)
                .orderItems(orderItems)
                .shoppingCartIds(Collections.emptyList()) // 重试同步时不需要购物车信息
                .build();
    }

    // ==================== 状态管理相关辅助方法 ====================

    /**
     * 记录订单状态管理系统的初始化信息
     */
    private void logOrderStatusInitialization(OrderContext orderContext) {
        try {
            log.info("订单状态管理系统初始化完成:");
            log.info("- 采购订单: ID={}, 状态={}, 供应商数量={}",
                    orderContext.getPurchaseOrder().getId(),
                    orderContext.getPurchaseOrder().getOrderStatus().getDescription(),
                    orderContext.getPurchaseOrder().getSupplierCount());

            log.info("- 供应商订单数量: {}", orderContext.getSupplierOrders().size());
            orderContext.getSupplierOrders().forEach(supplier -> log.debug("  供应商订单: ID={}, 供应商={}, 状态={}, 订单项数量={}",
                    supplier.getId(),
                    supplier.getSupplierId(),
                    supplier.getStatus().getDescription(),
                    supplier.getLineItemCount()));

            log.info("- 订单项数量: {}", orderContext.getOrderItems().size());
            orderContext.getOrderItems().forEach(item -> log.debug("  订单项: ID={}, SKU={}, 状态={}",
                    item.getId(),
                    item.getProductSkuId(),
                    item.getStatus().getDesc()));

        } catch (Exception e) {
            log.warn("记录订单状态初始化信息失败: {}", e.getMessage());
        }
    }

}
