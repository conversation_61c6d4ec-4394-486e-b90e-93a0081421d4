/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.convert;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.dto.ProductInfoDTO;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.TzProductSellerDataInfoDTO;
import com.fulfillmen.shop.domain.dto.TzProductSkuDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductAttributeCPVDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductSaleInfoDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductSellerDataDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductShippingInfoDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductSkuDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductSkuSpecDTO;
import com.fulfillmen.shop.domain.entity.json.AttrJson;
import com.fulfillmen.shop.domain.vo.ProductDetailVO;
import com.fulfillmen.shop.domain.vo.ProductInfoVO;
import com.fulfillmen.shop.domain.vo.SellerDataInfoVO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 前端产品信息转换器
 *
 * <AUTHOR>
 * @date 2025/6/10 10:00
 * @description: 前端产品信息转换器，简化转换逻辑，直接映射字段
 * @since 1.0.0
 */
@Mapper
public interface FrontendProductConvert {

    FrontendProductConvert INSTANCE = Mappers.getMapper(FrontendProductConvert.class);

    /**
     * 批量转换ProductInfoDTO列表为ProductInfoVO列表
     *
     * @param productInfoDTOList ProductInfoDTO列表
     * @return ProductInfoVO列表
     */
    default List<ProductInfoVO> toProductInfoVOList(List<ProductInfoDTO> productInfoDTOList) {
        if (CollectionUtil.isEmpty(productInfoDTOList)) {
            return Collections.emptyList();
        }

        return productInfoDTOList.stream().map(this::toProductInfoVO).collect(Collectors.toList());
    }

    /**
     * 转换分页对象
     *
     * @param productInfoDTOPage ProductInfoDTO分页对象
     * @return ProductInfoVO分页对象
     */
    default PageDTO<ProductInfoVO> toProductInfoVOPage(PageDTO<ProductInfoDTO> productInfoDTOPage) {
        if (productInfoDTOPage == null) {
            return null;
        }

        List<ProductInfoVO> voList = toProductInfoVOList(productInfoDTOPage.getRecords());

        return PageDTO.<ProductInfoVO>builder()
            .records(voList)
            .total(productInfoDTOPage.getTotal())
            .pageIndex(productInfoDTOPage.getPageIndex())
            .pageSize(productInfoDTOPage.getPageSize())
            .build();
    }

    /**
     * 将ProductInfoDTO转换为ProductInfoVO 直接映射字段，不处理多语言转换
     *
     * @param productInfoDTO ProductInfoDTO
     * @return ProductInfoVO
     */
    @Mapping(source = "sellerDataInfo", target = "sellerInfo")
    @Mapping(source = "categoryName", target = "categoryName")
    @Mapping(source = "nameTrans", target = "nameTrans")
    @Mapping(source = "categoryNameTrans", target = "categoryNameEn")
    ProductInfoVO toProductInfoVO(ProductInfoDTO productInfoDTO);

    /**
     * 将AlibabaProductDetailDTO转换为ProductDetailVO 包含中英文字段映射
     *
     * @param alibabaProductDetail AlibabaProductDetailDTO
     * @return ProductDetailVO
     */
    @Mapping(source = "id", target = "id")
    @Mapping(source = "title", target = "title")
    @Mapping(source = "titleTrans", target = "titleEn")
    @Mapping(source = "categoryName", target = "categoryName")
    @Mapping(source = "categoryNameTrans", target = "categoryNameEn")
    @Mapping(source = "description", target = "description")
    @Mapping(source = "descriptionTrans", target = "descriptionEn")
    @Mapping(source = "unit", target = "unit")
    @Mapping(source = "unitTrans", target = "unitEn")
    @Mapping(source = "minOrderQuantity", target = "minOrderQuantity")
    @Mapping(target = "sellerDataInfo", expression = "java(convertAlibabaSellerDataToVO(alibabaProductDetail.getSellerDataInfo()))")
    @Mapping(target = "productSkuInfos", expression = "java(convertProductSkuInfos(alibabaProductDetail.getProductSkuList()))")
    @Mapping(target = "productAttributes", expression = "java(convertProductAttributes(alibabaProductDetail.getProductAttributeList()))")
    @Mapping(target = "productSaleInfo", expression = "java(convertProductSaleInfo(alibabaProductDetail.getProductSaleInfo()))")
    @Mapping(target = "shippingInfo", expression = "java(convertShippingInfo(alibabaProductDetail.getShippingInfo()))")
    ProductDetailVO toProductDetailVO(AlibabaProductDetailDTO alibabaProductDetail);

    /**
     * 将TzProductDTO转换为ProductDetailVO 支持统一的产品数据结构转换
     *
     * @param tzProductDTO TzProductDTO
     * @return ProductDetailVO
     */
    default ProductDetailVO toProductDetailVOFromTzProductDTO(TzProductDTO tzProductDTO) {
        if (tzProductDTO == null) {
            return null;
        }
        ProductDetailVO vo = new ProductDetailVO();
        vo.setId(tzProductDTO.getId());
        vo.setTitle(tzProductDTO.getTitle());
        vo.setTitleEn(tzProductDTO.getTitleTrans());
        vo.setCategoryId(tzProductDTO.getCategoryId());
        vo.setCategoryName(tzProductDTO.getCategoryName());
        vo.setCategoryNameEn(tzProductDTO.getCategoryNameTrans());
        vo.setDescription(tzProductDTO.getDescription());
        vo.setDescriptionEn(tzProductDTO.getDescriptionTrans());
        vo.setMinOrderQuantity(tzProductDTO.getMinOrderQuantity());
        vo.setUnit(tzProductDTO.getUnit());
        vo.setUnitEn(tzProductDTO.getUnitTrans());
        vo.setMainVideo(tzProductDTO.getMainVideo());
        vo.setDetailVideo(tzProductDTO.getDetailVideo());
        vo.setImages(convertImagesFromTzProductDTO(tzProductDTO));
        vo.setWhiteImage(tzProductDTO.getWhiteImage());
        vo.setProductSkuInfos(convertTzProductSkuInfos(tzProductDTO.getSkuList()));
        vo.setProductAttributes(convertTzProductAttributes(tzProductDTO.getAttributeCpvs()));
        vo.setProductSaleInfo(convertTzProductSaleInfo(tzProductDTO));
        vo.setShippingInfo(convertTzProductShippingInfo(tzProductDTO.getShippingInfo()));
        vo.setSellerDataInfo(convertTzProductSellerDataInfo(tzProductDTO.getSellerDataInfo()));
        vo.setPrice(extractPriceFromTzProduct(tzProductDTO));
        vo.setUsdPrice(calculateUsdPriceFromTzProduct(tzProductDTO));
        return vo;
    }

    // ===================== 保持向后兼容的简单转换方法 =====================

    /**
     * 将ProductInfoDTO分页对象转换为ProductInfoVO分页对象
     *
     * @param productInfoDTOByPage ProductInfoDTO分页对象
     * @return ProductInfoVO分页对象
     */
    default PageDTO<ProductInfoVO> convert(PageDTO<ProductInfoDTO> productInfoDTOByPage) {
        return toProductInfoVOPage(productInfoDTOByPage);
    }

    /**
     * 将ProductInfoDTO列表转换为ProductInfoVO列表
     *
     * @param productInfoDTOList ProductInfoDTO列表
     * @return ProductInfoVO列表
     */
    default List<ProductInfoVO> convert(List<ProductInfoDTO> productInfoDTOList) {
        return toProductInfoVOList(productInfoDTOList);
    }

    /**
     * 将ProductInfoDTO转换为ProductInfoVO
     *
     * @param productInfoDTO ProductInfoDTO
     * @return ProductInfoVO
     */
    default ProductInfoVO convert(ProductInfoDTO productInfoDTO) {
        return toProductInfoVO(productInfoDTO);
    }

    // ===================== 复杂对象转换方法 =====================

    /**
     * 转换 AlibabaProductSellerDataDTO 为 SellerDataInfoVO
     *
     * @param sellerData AlibabaProductSellerDataDTO
     * @return SellerDataInfoVO
     */
    default SellerDataInfoVO convertAlibabaSellerDataToVO(AlibabaProductSellerDataDTO sellerData) {
        if (sellerData == null) {
            return null;
        }

        SellerDataInfoVO vo = new SellerDataInfoVO();

        // 交易勋章等级转换（Integer -> Integer）
        vo.setTradeMedalLevel(sellerData.getTradeMedalLevel());

        vo.setCompositeServiceScore(sellerData.getCompositeServiceScore());
        vo.setLogisticsExperienceScore(sellerData.getLogisticsExperienceScore());
        vo.setDisputeComplaintScore(sellerData.getDisputeComplaintScore());
        vo.setOfferExperienceScore(sellerData.getOfferExperienceScore());
        vo.setAfterSalesExperienceScore(sellerData.getAfterSalesExperienceScore());
        vo.setConsultingExperienceScore(sellerData.getConsultingExperienceScore());
        vo.setRepeatPurchasePercent(sellerData.getRepeatPurchasePercent());

        return vo;
    }

    /**
     * 转换商品SKU信息列表
     *
     * @param skuList AlibabaProductSkuDTO列表
     * @return ProductDetailVO.ProductSkuInfoVO列表
     */
    default List<ProductDetailVO.ProductSkuInfoVO> convertProductSkuInfos(List<AlibabaProductSkuDTO> skuList) {
        if (CollectionUtil.isEmpty(skuList)) {
            return Collections.emptyList();
        }

        return skuList.stream().map(this::convertSingleProductSkuInfo).collect(Collectors.toList());
    }

    /**
     * 转换单个商品SKU信息
     *
     * @param sku AlibabaProductSkuDTO
     * @return ProductDetailVO.ProductSkuInfoVO
     */
    default ProductDetailVO.ProductSkuInfoVO convertSingleProductSkuInfo(AlibabaProductSkuDTO sku) {
        if (sku == null) {
            return null;
        }

        ProductDetailVO.ProductSkuInfoVO vo = new ProductDetailVO.ProductSkuInfoVO();
        vo.setSkuId(sku.getSkuId());
        vo.setSpecId(sku.getSpecId());
        vo.setAmountOnSale(sku.getAmountOnSale());
        vo.setPrice(sku.getPrice());
        vo.setUsdPrice(sku.getUsdPrice());
        vo.setSpecs(convertProductSkuSpecs(sku.getSpecs()));
        return vo;
    }

    /**
     * 转换SKU规格属性列表
     *
     * @param specList AlibabaProductSkuSpecDTO列表
     * @return ProductDetailVO.ProductSkuSpecVO列表
     */
    default List<ProductDetailVO.ProductSkuSpecVO> convertProductSkuSpecs(List<AlibabaProductSkuSpecDTO> specList) {
        if (CollectionUtil.isEmpty(specList)) {
            return Collections.emptyList();
        }

        return specList.stream().map(this::convertSingleProductSkuSpec).collect(Collectors.toList());
    }

    /**
     * 转换单个SKU规格属性
     *
     * @param spec AlibabaProductSkuSpecDTO
     * @return ProductDetailVO.ProductSkuSpecVO
     */
    default ProductDetailVO.ProductSkuSpecVO convertSingleProductSkuSpec(AlibabaProductSkuSpecDTO spec) {
        if (spec == null) {
            return null;
        }

        ProductDetailVO.ProductSkuSpecVO vo = new ProductDetailVO.ProductSkuSpecVO();
        vo.setAttributeId(spec.getAttributeId());
        vo.setSpecName(spec.getAttributeName());
        vo.setSpecNameEn(spec.getAttributeNameTrans());
        vo.setSpecValue(spec.getValue());
        vo.setSpecValueEn(spec.getValueTrans());

        return vo;
    }

    /**
     * 转换商品属性列表
     *
     * @param attributeList AlibabaProductAttributeCPVDTO列表
     * @return ProductDetailVO.ProductAttributeVO列表
     */
    default List<ProductDetailVO.ProductAttributeVO> convertProductAttributes(List<AlibabaProductAttributeCPVDTO> attributeList) {
        if (CollectionUtil.isEmpty(attributeList)) {
            return Collections.emptyList();
        }

        return attributeList.stream().map(this::convertSingleProductAttribute).collect(Collectors.toList());
    }

    /**
     * 转换单个商品属性
     *
     * @param attribute AlibabaProductAttributeCPVDTO
     * @return ProductDetailVO.ProductAttributeVO
     */
    default ProductDetailVO.ProductAttributeVO convertSingleProductAttribute(AlibabaProductAttributeCPVDTO attribute) {
        if (attribute == null) {
            return null;
        }

        ProductDetailVO.ProductAttributeVO vo = new ProductDetailVO.ProductAttributeVO();
        vo.setAttributeId(attribute.getAttributeId());
        vo.setAttributeName(attribute.getAttributeName());
        vo.setAttributeNameEn(attribute.getAttributeNameTrans());
        vo.setValue(attribute.getValue());
        vo.setValueEn(attribute.getValueTrans());

        return vo;
    }

    /**
     * 转换商品销售信息
     *
     * @param saleInfo AlibabaProductSaleInfoDTO
     * @return ProductDetailVO.ProductSaleInfoVO
     */
    default ProductDetailVO.ProductSaleInfoVO convertProductSaleInfo(AlibabaProductSaleInfoDTO saleInfo) {
        if (saleInfo == null) {
            return null;
        }

        ProductDetailVO.ProductSaleInfoVO vo = new ProductDetailVO.ProductSaleInfoVO();
        vo.setAmountOnSale(saleInfo.getAmountOnSale());
        vo.setQuoteType(saleInfo.getQuoteType());
        vo.setMinOrderQuantity(saleInfo.getMinOrderQuantity());

        // 设置单位信息
        if (saleInfo.getUnitInfo() != null) {
            vo.setUnit(saleInfo.getUnitInfo().getUnit());
            vo.setUnitEn(saleInfo.getUnitInfo().getUnitTrans());
        }

        // 设置价格信息 - 从价格区间中获取第一个价格
        if (CollectionUtil.isNotEmpty(saleInfo.getPriceRangeList())) {
            AlibabaProductSaleInfoDTO.PriceRange firstRange = saleInfo.getPriceRangeList().get(0);
            vo.setPrice(firstRange.getPrice());
            vo.setUsdPrice(firstRange.getUsdPrice());
        }

        // 设置分销信息
        if (saleInfo.getFenxiaoSaleInfo() != null) {
            // 可以添加更多分销相关的字段映射
            var fenxiaoSaleInfo = saleInfo.getFenxiaoSaleInfo();
            vo.setPrice(fenxiaoSaleInfo.getOnePiecePrice());
            vo.setUsdPrice(fenxiaoSaleInfo.getUsdOnePiecePrice());
        }

        return vo;
    }

    /**
     * 转换商品物流包裹信息
     *
     * @param shippingInfo AlibabaProductShippingInfoDTO
     * @return ProductDetailVO.ProductShippingInfoVO
     */
    default ProductDetailVO.ProductShippingInfoVO convertShippingInfo(AlibabaProductShippingInfoDTO shippingInfo) {
        if (shippingInfo == null) {
            return null;
        }

        ProductDetailVO.ProductShippingInfoVO vo = new ProductDetailVO.ProductShippingInfoVO();

        // 从 Double 转换为 BigDecimal，并设置包装尺寸
        if (shippingInfo.getLength() != null) {
            vo.setPackageLength(BigDecimal.valueOf(shippingInfo.getLength()));
        }
        if (shippingInfo.getWidth() != null) {
            vo.setPackageWidth(BigDecimal.valueOf(shippingInfo.getWidth()));
        }
        if (shippingInfo.getHeight() != null) {
            vo.setPackageHeight(BigDecimal.valueOf(shippingInfo.getHeight()));
        }
        if (shippingInfo.getWeight() != null) {
            vo.setPackageWeight(BigDecimal.valueOf(shippingInfo.getWeight()));
        }

        // 设置发货地址作为运输方式（暂时）
        vo.setShippingMethod(shippingInfo.getSendGoodsAddressText());
        vo.setShippingMethodEn(shippingInfo.getSendGoodsAddressText()); // 暂时使用相同值

        // 暂时设置默认发货天数，实际项目中可能需要从其他字段获取
        vo.setEstimatedDeliveryDays(7); // 默认7天

        return vo;
    }

    // ===================== TzProductDTO 相关转换方法 =====================

    /**
     * 将主图转换为图片列表
     *
     * @param mainImage 主图URL
     * @return 图片列表
     */
    default List<String> convertMainImageToList(String mainImage) {
        if (StrUtil.isBlank(mainImage)) {
            return Collections.emptyList();
        }
        return List.of(mainImage);
    }

    /**
     * 从TzProductDTO中提取完整的图片列表
     *
     * @param tzProductDTO TzProductDTO
     * @return 图片列表
     */
    default List<String> convertImagesFromTzProductDTO(TzProductDTO tzProductDTO) {
        List<String> imageList = new ArrayList<>();

        // 添加主图
        if (StrUtil.isNotBlank(tzProductDTO.getMainImage())) {
            imageList.add(tzProductDTO.getMainImage());
        }

        // 从JSON字符串中解析更多图片
        if (StrUtil.isNotBlank(tzProductDTO.getImages())) {
            try {
                List<String> additionalImages = cn.hutool.json.JSONUtil.toList(tzProductDTO.getImages(), String.class);
                if (CollectionUtil.isNotEmpty(additionalImages)) {
                    imageList.addAll(additionalImages);
                }
            } catch (Exception e) {
                // 如果JSON解析失败，忽略错误
            }
        }

        return imageList.isEmpty() ? Collections.emptyList() : imageList;
    }

    /**
     * 转换TzProductDTO的商品属性
     *
     * @param attributeCpvsJson 属性JSON字符串
     * @return ProductDetailVO.ProductAttributeVO列表
     */
    default List<ProductDetailVO.ProductAttributeVO> convertTzProductAttributes(String attributeCpvsJson) {
        if (StrUtil.isBlank(attributeCpvsJson)) {
            return Collections.emptyList();
        }

        try {
            List<AlibabaProductAttributeCPVDTO> attributeList = JSONUtil
                .toList(attributeCpvsJson, AlibabaProductAttributeCPVDTO.class);

            if (CollectionUtil.isEmpty(attributeList)) {
                return Collections.emptyList();
            }

            return convertProductAttributes(attributeList);
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    /**
     * 转换TzProductDTO的销售信息
     *
     * @param tzProductDTO TzProductDTO
     * @return ProductDetailVO.ProductSaleInfoVO
     */
    default ProductDetailVO.ProductSaleInfoVO convertTzProductSaleInfo(TzProductDTO tzProductDTO) {
        if (tzProductDTO == null) {
            return null;
        }

        // 只为单品创建销售信息
        boolean isSingleItem = tzProductDTO.getIsSingleItem() != null && tzProductDTO.getIsSingleItem()
            .equals(com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum.YES);

        if (!isSingleItem) {
            return null;
        }

        ProductDetailVO.ProductSaleInfoVO vo = new ProductDetailVO.ProductSaleInfoVO();

        // 从单品SKU中获取信息
        if (CollectionUtil.isNotEmpty(tzProductDTO.getSkuList())) {
            TzProductSkuDTO firstSku = tzProductDTO.getSkuList().get(0);
            vo.setAmountOnSale(firstSku.getQuantity());
            vo.setPrice(firstSku.getDropShippingPrice() != null
                ? firstSku.getDropShippingPrice()
                : firstSku.getPrice());
            vo.setUsdPrice(firstSku.getUsdDropShippingPrice() != null
                ? firstSku.getUsdDropShippingPrice()
                : firstSku.getUsdPrice());

            // 从SKU单位信息中获取单位
            if (firstSku.getUnitInfo() != null) {
                vo.setUnit(firstSku.getUnitInfo().getUnit());
                vo.setUnitEn(firstSku.getUnitInfo().getUnitTrans());
            }
        }

        // 设置基础信息
        vo.setUnit(tzProductDTO.getUnit());
        vo.setUnitEn(tzProductDTO.getUnitTrans());
        vo.setMinOrderQuantity(tzProductDTO.getMinOrderQuantity() != null ? tzProductDTO.getMinOrderQuantity() : 1);
        vo.setQuoteType(0); // 单品按商品数量报价

        return vo;
    }

    /**
     * 转换TzProductDTO的物流信息
     *
     * @param shippingInfoJson 物流信息JSON字符串
     * @return ProductDetailVO.ProductShippingInfoVO
     */
    default ProductDetailVO.ProductShippingInfoVO convertTzProductShippingInfo(String shippingInfoJson) {
        if (StrUtil.isBlank(shippingInfoJson)) {
            return null;
        }

        try {
            AlibabaProductShippingInfoDTO shippingInfo = JSONUtil
                .toBean(shippingInfoJson, AlibabaProductShippingInfoDTO.class);

            return convertShippingInfo(shippingInfo);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 转换TzProductDTO中的卖家数据信息
     *
     * <p>
     * 现在TzProductDTO包含完整的TzProductSellerDataInfoDTO， 可以直接转换为前端需要的SellerDataInfoVO。
     * </p>
     *
     * @param sellerDataInfo TzProductSellerDataInfoDTO
     * @return SellerDataInfoVO
     */
    default SellerDataInfoVO convertTzProductSellerDataInfo(TzProductSellerDataInfoDTO sellerDataInfo) {
        if (sellerDataInfo == null) {
            return null;
        }

        SellerDataInfoVO vo = new SellerDataInfoVO();

        // 交易勋章等级转换（String -> Integer）
        if (StrUtil.isNotBlank(sellerDataInfo.getTradeMedalLevel())) {
            try {
                vo.setTradeMedalLevel(Integer.valueOf(sellerDataInfo.getTradeMedalLevel()));
            } catch (NumberFormatException e) {
                vo.setTradeMedalLevel(null);
            }
        }

        vo.setCompositeServiceScore(sellerDataInfo.getCompositeServiceScore());
        vo.setLogisticsExperienceScore(sellerDataInfo.getLogisticsExperienceScore());
        vo.setDisputeComplaintScore(sellerDataInfo.getDisputeComplaintScore());
        vo.setOfferExperienceScore(sellerDataInfo.getOfferExperienceScore());
        vo.setAfterSalesExperienceScore(sellerDataInfo.getAfterSalesExperienceScore());
        vo.setConsultingExperienceScore(sellerDataInfo.getConsultingExperienceScore());
        vo.setRepeatPurchasePercent(sellerDataInfo.getRepeatPurchasePercent());

        return vo;
    }

    /**
     * 从TzProductDTO中提取价格
     *
     * @param tzProductDTO TzProductDTO
     * @return 价格
     */
    default BigDecimal extractPriceFromTzProduct(TzProductDTO tzProductDTO) {
        if (tzProductDTO == null || CollectionUtil.isEmpty(tzProductDTO.getSkuList())) {
            return null;
        }

        // 获取第一个SKU的价格
        TzProductSkuDTO firstSku = tzProductDTO.getSkuList().get(0);
        return firstSku.getDropShippingPrice() != null ? firstSku.getDropShippingPrice() : firstSku.getPrice();
    }

    /**
     * 计算USD价格
     *
     * @param tzProductDTO TzProductDTO
     * @return USD价格
     */
    default BigDecimal calculateUsdPriceFromTzProduct(TzProductDTO tzProductDTO) {
        if (tzProductDTO == null || CollectionUtil.isEmpty(tzProductDTO.getSkuList())) {
            return null;
        }

        // 获取第一个SKU的USD价格
        TzProductSkuDTO firstSku = tzProductDTO.getSkuList().get(0);
        BigDecimal usdPrice = firstSku.getUsdDropShippingPrice() != null
            ? firstSku.getUsdDropShippingPrice()
            : firstSku.getUsdPrice();

        // 如果没有USD价格，可以基于人民币价格计算（需要汇率）
        if (usdPrice == null) {
            BigDecimal cnyPrice = firstSku.getDropShippingPrice() != null
                ? firstSku.getDropShippingPrice()
                : firstSku.getPrice();
            if (cnyPrice != null) {
                // 使用固定汇率7.2进行转换，实际项目中应该使用实时汇率
                return cnyPrice.divide(BigDecimal.valueOf(7.2), 2, RoundingMode.HALF_UP);
            }
        }

        return usdPrice;
    }

    /**
     * 转换TzProductDTO的SKU信息列表
     *
     * @param skuList TzProductDTO.TzProductSkuDTO列表
     * @return ProductDetailVO.ProductSkuInfoVO列表
     */
    default List<ProductDetailVO.ProductSkuInfoVO> convertTzProductSkuInfos(List<TzProductSkuDTO> skuList) {
        if (CollectionUtil.isEmpty(skuList)) {
            return Collections.emptyList();
        }

        return skuList.stream().map(this::convertSingleTzProductSkuInfo).collect(Collectors.toList());
    }

    /**
     * 转换单个TzProductDTO的SKU信息
     *
     * @param sku TzProductDTO.TzProductSkuDTO
     * @return ProductDetailVO.ProductSkuInfoVO
     */
    default ProductDetailVO.ProductSkuInfoVO convertSingleTzProductSkuInfo(TzProductSkuDTO sku) {
        if (sku == null) {
            return null;
        }

        ProductDetailVO.ProductSkuInfoVO vo = new ProductDetailVO.ProductSkuInfoVO();
        vo.setSkuId(sku.getId());
        vo.setSpecId(sku.getPlatformSpecId());
        vo.setAmountOnSale(sku.getQuantity());
        vo.setPrice(sku.getDropShippingPrice() != null ? sku.getDropShippingPrice() : sku.getPrice());
        vo.setImage(sku.getImage());
        // 计算USD价格
        vo.setUsdPrice(calculateUsdPriceFromSkuDTO(sku));

        // 转换规格属性
        vo.setSpecs(convertTzProductSkuSpecs(sku.getSpecs()));

        return vo;
    }

    /**
     * 计算SKU的USD价格
     *
     * @param sku TzProductDTO.TzProductSkuDTO
     * @return USD价格
     */
    default BigDecimal calculateUsdPriceFromSkuDTO(TzProductSkuDTO sku) {
        if (sku == null) {
            return null;
        }

        // 优先使用USD代发价格
        BigDecimal usdPrice = sku.getUsdDropShippingPrice();
        if (usdPrice != null) {
            return usdPrice;
        }

        // 其次使用USD价格
        usdPrice = sku.getUsdPrice();
        if (usdPrice != null) {
            return usdPrice;
        }

        // 最后基于人民币价格计算
        BigDecimal cnyPrice = sku.getDropShippingPrice() != null ? sku.getDropShippingPrice() : sku.getPrice();
        if (cnyPrice != null) {
            // 使用固定汇率7.2进行转换，实际项目中应该使用实时汇率
            return cnyPrice.divide(BigDecimal.valueOf(7.2), 2, java.math.RoundingMode.HALF_UP);
        }

        return null;
    }

    /**
     * 转换TzProductDTO的SKU规格属性
     *
     * <p>新的AttrJson结构包含中英文字段在同一个对象中</p>
     *
     * @param specsList AttrJson规格属性列表
     * @return ProductDetailVO.ProductSkuSpecVO列表
     */
    default List<ProductDetailVO.ProductSkuSpecVO> convertTzProductSkuSpecs(List<AttrJson> specsList) {
        if (CollectionUtil.isEmpty(specsList)) {
            return Collections.emptyList();
        }

        List<ProductDetailVO.ProductSkuSpecVO> specVOs = new ArrayList<>();

        // 直接转换每个AttrJson对象
        for (AttrJson attr : specsList) {
            ProductDetailVO.ProductSkuSpecVO specVO = new ProductDetailVO.ProductSkuSpecVO();

            // 设置属性ID
            specVO.setAttributeId(attr.getAttrId());

            // 设置中文属性
            specVO.setSpecName(attr.getAttrKey());
            specVO.setSpecValue(attr.getAttrValue());

            // 设置英文属性
            specVO.setSpecNameEn(attr.getAttrKeyTrans());
            specVO.setSpecValueEn(attr.getAttrValueTrans());

            specVOs.add(specVO);
        }

        return specVOs;
    }

}
