/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.feiniaojin.gracefulresponse.GracefulResponseException;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.enums.FulfillmenValidationCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.common.properties.CommonConstants;
import com.fulfillmen.shop.dao.mapper.TzProductSpuMapper;
import com.fulfillmen.shop.dao.mapper.TzShoppingCartMapper;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.TzShoppingCart;
import com.fulfillmen.shop.domain.entity.enums.ShoppingCartProductType;
import com.fulfillmen.shop.domain.req.ShoppingCartReq.AddProductToCartRecordDTO;
import com.fulfillmen.shop.domain.req.ShoppingCartReq.AddSkuInfoToCartRecordDTO;
import com.fulfillmen.shop.domain.req.ShoppingCartReq.SearchShoppingCartRecordDTO;
import com.fulfillmen.shop.frontend.convert.FrontendShoppingCartConvert;
import com.fulfillmen.shop.frontend.service.IShoppingCartService;
import com.fulfillmen.shop.domain.vo.ShoppingCartVO;
import com.fulfillmen.shop.manager.service.IProductSyncService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import me.ahoo.cosid.IdGenerator;
import me.ahoo.cosid.provider.DefaultIdGeneratorProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 购物车服务
 *
 * <AUTHOR>
 * @date 2025/4/28 09:41
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Service
public class ShoppingCartServiceImpl implements IShoppingCartService {

    private final TzShoppingCartMapper tzShoppingCartMapper;
    private final IProductSyncService productSyncService;
    private final TzProductSpuMapper tzProductSpuMapper;

    public ShoppingCartServiceImpl(TzShoppingCartMapper tzShoppingCartMapper,
        @Qualifier("productSyncServiceImpl") IProductSyncService productSyncService,
        TzProductSpuMapper tzProductSpuMapper) {
        this.tzShoppingCartMapper = tzShoppingCartMapper;
        this.productSyncService = productSyncService;
        this.tzProductSpuMapper = tzProductSpuMapper;
    }

    @Override
    @CacheInvalidate(name = "cart:", key = "T(com.fulfillmen.shop.common.context.UserContextHolder).getUserId()")
    public void removeItemsToCart(String productId, List<String> skuIds) {
        Long userId = UserContextHolder.getUserId();

        // 1. 参数验证
        if (!StringUtils.hasText(productId)) {
            log.warn("删除购物车商品失败，产品ID为空，用户ID: {}", userId);
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PARAMETER_INVALID, "产品ID不能为空");
        }

        try {
            Long.valueOf(productId);
        } catch (NumberFormatException e) {
            log.warn("删除购物车商品失败，产品ID格式无效，用户ID: {}, 产品ID: {}", userId, productId);
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PARAMETER_INVALID, "产品ID格式无效");
        }

        // 2. 构建删除条件
        LambdaQueryWrapper<TzShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzShoppingCart::getUserId, userId).eq(TzShoppingCart::getProductId, Long.valueOf(productId));

        // 如果指定了SKU ID，则只删除指定的SKU
        if (Objects.nonNull(skuIds) && !skuIds.isEmpty()) {
            // 验证SKU ID格式
            List<Long> validSkuIds = new ArrayList<>();
            for (String skuId : skuIds) {
                try {
                    validSkuIds.add(Long.valueOf(skuId));
                } catch (NumberFormatException e) {
                    log.warn("删除购物车商品失败，SKU ID格式无效，用户ID: {}, 产品ID: {}, SKU ID: {}", userId, productId, skuId);
                    throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PARAMETER_INVALID, "SKU ID格式无效: " + skuId);
                }
            }
            queryWrapper.in(TzShoppingCart::getSkuId, validSkuIds);
        }

        // 3. 先查询要删除的记录数量，用于验证和日志
        long countToDelete = this.tzShoppingCartMapper.selectCount(queryWrapper);
        if (countToDelete == 0) {
            log.warn("删除购物车商品失败，未找到匹配的记录，用户ID: {}, 产品ID: {}, SKU IDs: {}", userId, productId, skuIds);
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.SHOPPING_CART_ITEM_NOT_FOUND, productId);
        }

        // 4. 执行删除操作
        try {
            int deletedCount = this.tzShoppingCartMapper.delete(queryWrapper);
            if (deletedCount > 0) {
                log.info("删除购物车记录成功，用户ID: {}, 产品ID: {}, SKU IDs: {}, 删除数量: {}", userId, productId, skuIds, deletedCount);
            } else {
                log.warn("删除购物车记录失败，没有记录被删除，用户ID: {}, 产品ID: {}, SKU IDs: {}", userId, productId, skuIds);
                throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.SHOPPING_CART_DELETE_FAILED, productId);
            }
        } catch (Exception e) {
            log.error("删除购物车记录时发生数据库异常，用户ID: {}, 产品ID: {}, SKU IDs: {}", userId, productId, skuIds, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.DATABASE_OPERATION_FAILED);
        }
    }

    @Override
    @Cached(name = "cart:", expire = 7300, cacheType = CacheType.REMOTE, key = "T(com.fulfillmen.shop.common.context.UserContextHolder).getUserId()")
    public List<ShoppingCartVO> getCartItems(SearchShoppingCartRecordDTO searchShoppingCartRecordDTO) {
        LambdaQueryWrapper<TzShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzShoppingCart::getUserId, UserContextHolder.getUserId())
            .like(StringUtils.hasText(searchShoppingCartRecordDTO
                .getKeyword()), TzShoppingCart::getTitle, searchShoppingCartRecordDTO.getKeyword())
            .like(StringUtils.hasText(searchShoppingCartRecordDTO
                .getKeyword()), TzShoppingCart::getTitleTrans, searchShoppingCartRecordDTO.getKeyword())
            .orderByDesc(TzShoppingCart::getGmtModified);
        List<TzShoppingCart> shoppingCarts = this.tzShoppingCartMapper.selectList(queryWrapper);
        // 返回购物车列表
        return FrontendShoppingCartConvert.INSTANCE.toVoList(shoppingCarts);
    }

    @Override
    @CacheInvalidate(name = "cart:", key = "T(com.fulfillmen.shop.common.context.UserContextHolder).getUserId()")
    public void addOrUpdateToCart(AddProductToCartRecordDTO addProductToCartRecordDTO) {
        // 1. 获取登录用户信息
        Long userId = UserContextHolder.getUserId();
        // 这个 id 是 tz_product_spu.id
        Long spuId = addProductToCartRecordDTO.getProductId();
        List<AddSkuInfoToCartRecordDTO> purchaseSkuInfos = addProductToCartRecordDTO.getPurchaseSkuInfos();

        // 2. 检查购物车数量限制
        checkCartCount(userId);
        // 获取平台产品ID
        TzProductSpu tzProductSpu = this.tzProductSpuMapper.selectById(spuId);
        if (Objects.isNull(tzProductSpu)) {
            log.warn("产品不存在 : [{}] ", spuId);
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PRODUCT_NOT_FOUND, spuId);
        }
        // FIXBUG: 2025年06月26日10:10:45 判断 sku 是否满足产品的最小起订量
        if (tzProductSpu.getMinOrderQuantity() != null && tzProductSpu.getMinOrderQuantity() > 0) {
            purchaseSkuInfos.forEach(skuInfoToCartRecordDTO -> {
                if (skuInfoToCartRecordDTO.getQty() < tzProductSpu.getMinOrderQuantity()) {
                    log.warn("产品最小起订量不足 : [{} - {}] : qty: {} ", spuId, skuInfoToCartRecordDTO.getSkuId(), skuInfoToCartRecordDTO.getQty());
                    throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PRODUCT_MIN_ORDER_QUANTITY_NOT_REACHED, tzProductSpu.getMinOrderQuantity());
                }
            });
            log.debug("产品最小起订量验证通过 : [{}] ", tzProductSpu.getMinOrderQuantity());
        }

        List<TzProductSku> skuList = productSyncService.getSkuListBySpuId(tzProductSpu.getId());

        // 5. 验证产品和SKU状态
        validateProductAndSkuStatus(tzProductSpu.getPdcPlatformProductId(), tzProductSpu, skuList);

        // 6. 处理购物车添加/更新逻辑
        List<TzShoppingCart> addShoppingCarts = new ArrayList<>();
        handleSkuItemCart(userId, tzProductSpu, skuList, purchaseSkuInfos, addShoppingCarts);

        // 7. 批量保存新的购物车项
        if (!addShoppingCarts.isEmpty()) {
            tzShoppingCartMapper.insertBatch(addShoppingCarts);
            log.info("成功添加 {} 个新商品到购物车，用户ID: {}", addShoppingCarts.size(), userId);
        }
    }

    /**
     * 验证产品和SKU状态
     *
     * @param platformProductId 平台产品ID
     * @param productSpu        产品SPU信息
     * @param skuList           SKU列表
     */
    private void validateProductAndSkuStatus(String platformProductId,
        TzProductSpu productSpu,
        List<TzProductSku> skuList) {

        if (CollectionUtil.isEmpty(skuList)) {
            log.error("商品数据异常：SKU列表为空. spuId: {}, platformProductId: {}", productSpu.getId(), platformProductId);
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PRODUCT_SKU_NOT_FOUND, platformProductId);
        }

        // 检查是否有任何一个SKU具有有效价格
        boolean hasValidPriceSku = skuList.stream().anyMatch(sku -> {
            BigDecimal price = sku.getDropShippingPrice() != null ? sku.getDropShippingPrice() : sku.getPrice();
            return price != null && price.compareTo(BigDecimal.ZERO) > 0;
        });

        if (!hasValidPriceSku) {
            log.error("商品无任何有效价格的SKU. spuId: {}, platformProductId: {}", productSpu.getId(), platformProductId);
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PRODUCT_PRICE_NOT_AVAILABLE, productSpu.getId());
        }

        log.debug("产品和SKU状态验证通过. spuId: {}, platformProductId: {}", productSpu.getId(), platformProductId);
    }

    /**
     * 检查购物车数量
     *
     * @param userId 用户 ID
     */
    private void checkCartCount(Long userId) {
        // TODO: 2025/5/9 如果存在 sku 信息内
        LambdaQueryWrapper<TzShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzShoppingCart::getUserId, userId);
        long count = this.tzShoppingCartMapper.selectCount(queryWrapper);
        if (count >= CommonConstants.SHOPPING_CART_MAX_LIMIT) {
            log.warn("购物车数量已达到上限，当前数量: {} , 最大数量: {} ", count, CommonConstants.SHOPPING_CART_MAX_LIMIT);
            throw new GracefulResponseException("The number of items in the shopping cart has reached the limit.");
        }
    }

    @Override
    @CacheInvalidate(name = "cart:", key = "T(com.fulfillmen.shop.common.context.UserContextHolder).getUserId()")
    public void clearItemsToCart() {
        Long userId = UserContextHolder.getUserId();

        // 1. 先查询购物车中的商品数量
        LambdaQueryWrapper<TzShoppingCart> countWrapper = new LambdaQueryWrapper<>();
        countWrapper.eq(TzShoppingCart::getUserId, userId);
        long itemCount = this.tzShoppingCartMapper.selectCount(countWrapper);

        if (itemCount == 0) {
            log.info("清空购物车操作，购物车已为空，用户ID: {}", userId);
            return; // 购物车已为空，直接返回
        }

        // 2. 执行清空操作
        try {
            LambdaQueryWrapper<TzShoppingCart> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(TzShoppingCart::getUserId, userId);
            int deletedCount = this.tzShoppingCartMapper.delete(deleteWrapper);

            if (deletedCount > 0) {
                log.info("清空购物车成功，用户ID: {}, 清空数量: {}", userId, deletedCount);
            } else {
                log.warn("清空购物车失败，没有记录被删除，用户ID: {}", userId);
                throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.SHOPPING_CART_CLEAR_FAILED);
            }
        } catch (Exception e) {
            log.error("清空购物车时发生数据库异常，用户ID: {}", userId, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.DATABASE_OPERATION_FAILED);
        }
    }

    @Override
    public List<ShoppingCartVO> getCartItems(String productId) {
        Long userId = UserContextHolder.getUserId();
        LambdaQueryWrapper<TzShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzShoppingCart::getUserId, userId).eq(TzShoppingCart::getProductId, Long.valueOf(productId));
        List<TzShoppingCart> shoppingCarts = this.tzShoppingCartMapper.selectList(queryWrapper);
        if (!shoppingCarts.isEmpty()) {
            return FrontendShoppingCartConvert.INSTANCE.toVoList(shoppingCarts);
        }
        return List.of();
    }

    @Override
    @CacheInvalidate(name = "cart:", key = "T(com.fulfillmen.shop.common.context.UserContextHolder).getUserId()")
    public void updateProductQuantity(String productId, String skuId, Integer quantity) {
        Long userId = UserContextHolder.getUserId();

        // 1. 参数验证
        if (!StringUtils.hasText(productId)) {
            log.warn("更新购物车数量失败，产品ID为空，用户ID: {}", userId);
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PARAMETER_INVALID, "产品ID不能为空");
        }

        if (!StringUtils.hasText(skuId)) {
            log.warn("更新购物车数量失败，SKU ID为空，用户ID: {}, 产品ID: {}", userId, productId);
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PARAMETER_INVALID, "SKU ID不能为空");
        }

        if (quantity == null || quantity <= 0) {
            log.warn("更新购物车数量失败，数量无效，用户ID: {}, 产品ID: {}, SKU ID: {}, 数量: {}", userId, productId, skuId, quantity);
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PARAMETER_INVALID, "商品数量必须大于0");
        }

        if (quantity > CommonConstants.SHOPPING_CART_SINGLE_ITEM_MAX_QUANTITY) {
            log.warn("更新购物车数量失败，数量超过限制，用户ID: {}, 产品ID: {}, SKU ID: {}, 数量: {}, 最大限制: {}", userId, productId, skuId, quantity,
                CommonConstants.SHOPPING_CART_SINGLE_ITEM_MAX_QUANTITY);
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.SHOPPING_CART_QUANTITY_EXCEEDED, String
                .valueOf(CommonConstants.SHOPPING_CART_SINGLE_ITEM_MAX_QUANTITY));
        }

        // 2. 验证ID格式
        Long productIdLong;
        Long skuIdLong;
        try {
            productIdLong = Long.valueOf(productId);
            skuIdLong = Long.valueOf(skuId);
        } catch (NumberFormatException e) {
            log.warn("更新购物车数量失败，ID格式无效，用户ID: {}, 产品ID: {}, SKU ID: {}", userId, productId, skuId);
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PARAMETER_INVALID, "产品ID或SKU ID格式无效");
        }

        // 3. 查找购物车记录
        LambdaQueryWrapper<TzShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzShoppingCart::getUserId, userId)
            .eq(TzShoppingCart::getProductId, productIdLong)
            .eq(TzShoppingCart::getSkuId, skuIdLong);

        TzShoppingCart shoppingCart = this.tzShoppingCartMapper.selectOne(queryWrapper);
        if (shoppingCart == null) {
            log.warn("更新购物车数量失败，商品不在购物车中，用户ID: {}, 产品ID: {}, SKU ID: {}", userId, productId, skuId);
            throw BusinessExceptionI18n
                .of(FulfillmenValidationCodeEnum.SHOPPING_CART_ITEM_NOT_FOUND, productId + ":" + skuId);
        }

        // 4. 验证商品价格（防止价格为空或无效的情况）
        if (shoppingCart.getPrice() == null || shoppingCart.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            log.error("更新购物车数量失败，商品价格无效，用户ID: {}, 产品ID: {}, SKU ID: {}, 价格: {}", userId, productId, skuId, shoppingCart
                .getPrice());
            throw BusinessExceptionI18n
                .of(FulfillmenValidationCodeEnum.PRODUCT_PRICE_NOT_AVAILABLE, productId + ":" + skuId);
        }

        // 5. 更新数量和总价
        Integer oldQuantity = shoppingCart.getQuantity();
        BigDecimal oldTotalPrice = shoppingCart.getTotalPrice();

        try {
            shoppingCart.setQuantity(quantity);
            shoppingCart.setTotalPrice(NumberUtil.mul(shoppingCart.getPrice(), quantity));

            int updateResult = this.tzShoppingCartMapper.updateById(shoppingCart);
            if (updateResult > 0) {
                log.info("更新购物车数量成功，用户ID: {}, 产品ID: {}, SKU ID: {}, 数量: {} -> {}, 总价: {} -> {}", userId, productId, skuId, oldQuantity, quantity, oldTotalPrice,
                    shoppingCart
                        .getTotalPrice());
            } else {
                log.warn("更新购物车数量失败，数据库更新返回0，用户ID: {}, 产品ID: {}, SKU ID: {}", userId, productId, skuId);
                throw BusinessExceptionI18n
                    .of(FulfillmenErrorCodeEnum.SHOPPING_CART_UPDATE_FAILED, productId + ":" + skuId);
            }
        } catch (Exception e) {
            log.error("更新购物车数量时发生数据库异常，用户ID: {}, 产品ID: {}, SKU ID: {}, 数量: {}", userId, productId, skuId, quantity, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.DATABASE_OPERATION_FAILED);
        }
    }

    /**
     * 处理多规格商品购物车添加
     *
     * @param userId           用户ID
     * @param productSpu       产品SPU信息
     * @param skuList          SKU列表
     * @param purchaseSkuInfos 购买的SKU信息列表
     * @param addShoppingCarts 待添加的购物车列表
     */
    private void handleSkuItemCart(Long userId,
        TzProductSpu productSpu,
        List<TzProductSku> skuList,
        List<AddSkuInfoToCartRecordDTO> purchaseSkuInfos,
        List<TzShoppingCart> addShoppingCarts) {
        Long spuId = productSpu.getId();
        String pdcPlatformProductId = productSpu.getPdcPlatformProductId();
        // 创建SKU映射，方便查找（此时已通过validateProductAndSkuStatus验证列表非空）
        Map<Long, TzProductSku> platformSkuMap = skuList.stream()
            .collect(Collectors.toMap(TzProductSku::getId, sku -> sku));

        for (AddSkuInfoToCartRecordDTO skuInfo : purchaseSkuInfos) {
            final int buyQty = skuInfo.getQty();
            Long skuId = skuInfo.getSkuId();

            // 多规格商品必须提供skuId
            if (Objects.isNull(skuId)) {
                // fixed bug
                log.warn("多规格商品缺少SKU ID，spuId: {}, platformProductId: {}", spuId, pdcPlatformProductId);
                throw BusinessExceptionI18n
                    .of(FulfillmenValidationCodeEnum.PRODUCT_SKU_NOT_FOUND, spuId + ": sku is " + skuId);
            }

            // 查找对应的SKU信息
            TzProductSku tzProductSku = platformSkuMap.get(skuId);
            if (tzProductSku == null) {
                log.warn("未找到对应的SKU信息，spuId: {}, platformSkuId: {}", spuId, skuId);
                throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PRODUCT_SKU_NOT_FOUND, spuId + ":" + skuId);
            }

            // 验证SKU价格有效性
            BigDecimal price = tzProductSku.getDropShippingPrice() != null
                ? tzProductSku.getDropShippingPrice()
                : tzProductSku.getPrice();

            if (price == null || price.compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("SKU价格无效，spuId: {}, skuId: {}, price: {}", spuId, skuId, price);
                throw BusinessExceptionI18n
                    .of(FulfillmenValidationCodeEnum.PRODUCT_PRICE_NOT_AVAILABLE, spuId + ":" + skuId);
            }

            // 检查购物车中是否已存在
            LambdaQueryWrapper<TzShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TzShoppingCart::getUserId, userId)
                .eq(TzShoppingCart::getSourcePlatform, ShoppingCartProductType.ALIBABA)
                .eq(TzShoppingCart::getProductId, spuId)
                .eq(TzShoppingCart::getSkuId, tzProductSku.getId());

            TzShoppingCart existingCart = this.tzShoppingCartMapper.selectOne(queryWrapper);
            if (existingCart != null) {
                // 如果已存在则更新数量
                existingCart.setQuantity(existingCart.getQuantity() + buyQty);
                existingCart.setTotalPrice(NumberUtil.mul(existingCart.getPrice(), existingCart.getQuantity()));
                this.tzShoppingCartMapper.updateById(existingCart);
                log.debug("更新购物车数量，spuId: {}, skuId: {}, 新数量: {}", spuId, tzProductSku.getId(), existingCart
                    .getQuantity());
            } else {
                // 创建新的购物车记录
                IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");
                TzShoppingCart shoppingCart = TzShoppingCart.builder()
                    .id(idGenerator.generate())
                    .userId(userId)
                    .productId(productSpu.getId())
                    .skuId(tzProductSku.getId())
                    .quantity(buyQty)
                    .minOrderQuantity(productSpu.getMinOrderQuantity())
                    .price(price)
                    .title(productSpu.getTitle())
                    .titleTrans(productSpu.getTitleTrans())
                    .totalPrice(NumberUtil.mul(price, buyQty))
                    .sourcePlatform(ShoppingCartProductType.ALIBABA)
                    .mainImgUrl(StringUtils.hasText(tzProductSku.getImage())
                        ? tzProductSku.getImage()
                        : productSpu.getMainImage())
                    .specs(tzProductSku.getSpecs())
                    .sourcePlatformPdcId(productSpu.getPdcProductMappingId())
                    .sourcePlatformSellerOpenId(productSpu.getSourcePlatformSellerOpenId())
                    .sourcePlatformSellerName(productSpu.getSourcePlatformSellerName())
                    .build();

                addShoppingCarts.add(shoppingCart);
                log.debug("创建购物车记录，spuId: {}, skuId: {}, 数量: {}", productSpu.getId(), tzProductSku.getId(), buyQty);
            }
        }
    }

    @Override
    public List<ShoppingCartVO> listCartItems(SearchShoppingCartRecordDTO searchShoppingCartRecordDTO) {
        Long userId = UserContextHolder.getUserId();
        LambdaQueryWrapper<TzShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzShoppingCart::getUserId, userId)
            .like(StringUtils.hasText(searchShoppingCartRecordDTO
                .getKeyword()), TzShoppingCart::getTitle, searchShoppingCartRecordDTO.getKeyword())
            .or()
            .like(StringUtils.hasText(searchShoppingCartRecordDTO
                .getKeyword()), TzShoppingCart::getTitleTrans, searchShoppingCartRecordDTO.getKeyword())
            .orderByDesc(TzShoppingCart::getGmtModified);
        List<TzShoppingCart> shoppingCartPage = this.tzShoppingCartMapper.selectList(queryWrapper);

        return FrontendShoppingCartConvert.INSTANCE.toVoList(shoppingCartPage);
    }

    @Override
    @CacheInvalidate(name = "cart:", key = "T(com.fulfillmen.shop.common.context.UserContextHolder).getUserId()")
    public void updateItemCheckedStatus(Long id, Integer isChecked) {
        Long userId = UserContextHolder.getUserId();

        // 1. 参数验证
        if (id == null) {
            log.warn("更新购物车商品选中状态失败，购物车ID为空，用户ID: {}", userId);
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PARAMETER_INVALID, "shopping.cart.id.required");
        }
        if (isChecked == null || (isChecked != 0 && isChecked != 1)) {
            log.warn("更新购物车商品选中状态失败，选中状态无效，用户ID: {}, 购物车ID: {}, 选中状态: {}", userId, id, isChecked);
            throw BusinessExceptionI18n
                .of(FulfillmenValidationCodeEnum.PARAMETER_INVALID, "shopping.cart.checked.status.invalid");
        }

        // 2. 查找购物车记录并验证权限
        LambdaQueryWrapper<TzShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzShoppingCart::getId, id).eq(TzShoppingCart::getUserId, userId);

        TzShoppingCart shoppingCart = this.tzShoppingCartMapper.selectOne(queryWrapper);
        if (shoppingCart == null) {
            log.warn("更新购物车商品选中状态失败，购物车记录不存在或无权限，用户ID: {}, 购物车ID: {}", userId, id);
            throw BusinessExceptionI18n
                .of(FulfillmenValidationCodeEnum.SHOPPING_CART_ITEM_NOT_FOUND, "shopping.cart.item.not.found");
        }

        // 3. 更新选中状态
        shoppingCart.setIsChecked(isChecked);
        int updateResult = this.tzShoppingCartMapper.updateById(shoppingCart);
        if (updateResult <= 0) {
            log.error("更新购物车商品选中状态失败，数据库更新返回0，用户ID: {}, 购物车ID: {}", userId, id);
            throw BusinessExceptionI18n
                .of(FulfillmenErrorCodeEnum.SHOPPING_CART_UPDATE_FAILED, "shopping.cart.update.failed");
        }

        log.info("更新购物车商品选中状态成功，用户ID: {}, 购物车ID: {}, 选中状态: {}", userId, id, isChecked);
    }

    @Override
    @CacheInvalidate(name = "cart:", key = "T(com.fulfillmen.shop.common.context.UserContextHolder).getUserId()")
    public void batchUpdateItemCheckedStatus(List<Long> ids, Integer isChecked) {
        Long userId = UserContextHolder.getUserId();

        // 1. 参数验证
        if (ids == null || ids.isEmpty()) {
            log.warn("批量更新购物车商品选中状态失败，购物车ID列表为空，用户ID: {}", userId);
            return;
        }
        if (isChecked == null || (isChecked != 0 && isChecked != 1)) {
            log.warn("批量更新购物车商品选中状态失败，选中状态无效，用户ID: {}, 选中状态: {}", userId, isChecked);
            throw BusinessExceptionI18n
                .of(FulfillmenValidationCodeEnum.PARAMETER_INVALID, "shopping.cart.checked.status.invalid");
        }

        // 2. 批量查询购物车记录，同时验证用户权限
        LambdaQueryWrapper<TzShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TzShoppingCart::getId, ids).eq(TzShoppingCart::getUserId, userId); // 直接在查询中加上用户ID条件

        List<TzShoppingCart> cartItems = this.tzShoppingCartMapper.selectList(queryWrapper);

        // 3. 验证数量是否匹配（确保所有ID都存在且属于当前用户）
        if (cartItems.size() != ids.size()) {
            log.warn("批量更新购物车选中状态失败，部分购物车记录不存在或无权限，用户ID: {}, 请求数量: {}, 实际数量: {}", userId, ids.size(), cartItems.size());
            throw BusinessExceptionI18n
                .of(FulfillmenValidationCodeEnum.SHOPPING_CART_ITEM_NOT_FOUND, "shopping.cart.item.not.found.or.no.permission");
        }

        // 4. 批量更新选中状态
        cartItems.forEach(item -> item.setIsChecked(isChecked));

        // 5. 执行批量更新
        try {
            int successCount = 0;
            for (TzShoppingCart item : cartItems) {
                int updateResult = this.tzShoppingCartMapper.updateById(item);
                if (updateResult > 0) {
                    successCount++;
                } else {
                    log.warn("批量更新购物车选中状态失败，购物车ID: {}", item.getId());
                }
            }

            if (successCount != cartItems.size()) {
                log.error("批量更新购物车选中状态部分失败，用户ID: {}, 总数: {}, 成功数: {}", userId, cartItems.size(), successCount);
                throw BusinessExceptionI18n
                    .of(FulfillmenErrorCodeEnum.SHOPPING_CART_UPDATE_FAILED, "shopping.cart.batch.update.partial.failed");
            }

        } catch (Exception e) {
            log.error("批量更新购物车选中状态异常，用户ID: {}, 购物车ID数量: {}", userId, ids.size(), e);
            throw BusinessExceptionI18n
                .of(FulfillmenErrorCodeEnum.DATABASE_OPERATION_FAILED, "shopping.cart.database.operation.failed");
        }

        String operation = isChecked == 1 ? "选中" : "取消选中";
        log.info("批量{}购物车商品成功，用户ID: {}, 更新数量: {}", operation, userId, ids.size());
    }
}
