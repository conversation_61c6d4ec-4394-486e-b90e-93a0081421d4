/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service;

import java.util.List;
import com.fulfillmen.shop.domain.res.AlibabaCategoryRes;
import com.fulfillmen.shop.domain.vo.ProductInfoVO;

/**
 * 首页的数据业务类
 *
 * <AUTHOR>
 * @date 2025/2/15 10:57
 * @description: todo
 * @since 1.0.0
 */
public interface IHomeService {

    /**
     * 加载全部类目数据
     *
     * @return List<AlibabaCategoryVO>
     */
    List<AlibabaCategoryRes> loadCategories();

    /**
     * 加载热门商品列表
     *
     * @return List<TrendingProductInfoVO>
     */
    List<ProductInfoVO> loadTrendingProducts();

    /**
     * 加载热词类目
     *
     * @return List<String>
     */
    List<String> loadHotCategoryKeywords();

    /**
     * 搜索电子产品
     * <pre>
     * 1. 第一次将进行加载数据，并加入缓存
     * 2. 每 2 小时自动刷新缓存
     * </pre>
     *
     * @return List<SearchProductInfoVO>
     */
    List<ProductInfoVO> searchProductsByElectronicsWhitCache();
}
