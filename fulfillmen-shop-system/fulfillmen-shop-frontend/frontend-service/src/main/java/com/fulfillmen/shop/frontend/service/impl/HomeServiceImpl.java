/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service.impl;

import com.fulfillmen.shop.domain.convert.AlibabaCategoryDTOMapping;
import com.fulfillmen.shop.domain.dto.AlibabaCategoryTreeDTO;
import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.dto.ProductInfoDTO;
import com.fulfillmen.shop.domain.dto.ProductSearchRequestDTO;
import com.fulfillmen.shop.domain.res.AlibabaCategoryRes;
import com.fulfillmen.shop.frontend.convert.FrontendProductConvert;
import com.fulfillmen.shop.frontend.service.IHomeService;
import com.fulfillmen.shop.domain.vo.ProductInfoVO;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.core.repository.SysAlibabaCategoryRepository;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 首页数据业务
 *
 * <AUTHOR>
 * @date 2025/2/15 10:58
 * @description: 首页数据业务实现，统一使用ProductInfoVO返回商品信息，带同步入库操作
 * @since 1.0.0
 */
@Slf4j
@Service
public class HomeServiceImpl implements IHomeService {

    private final SysAlibabaCategoryRepository sysAlibabaCategoryService;
    private final PdcProductMappingRepository pdcProductMappingRepository;

    public HomeServiceImpl(SysAlibabaCategoryRepository sysAlibabaCategoryService,
        PdcProductMappingRepository pdcProductMappingRepository) {
        this.sysAlibabaCategoryService = sysAlibabaCategoryService;
        this.pdcProductMappingRepository = pdcProductMappingRepository;
    }

    @Override
    public List<AlibabaCategoryRes> loadCategories() {
        log.info("加载所有类目数据");
        List<AlibabaCategoryTreeDTO> categories = sysAlibabaCategoryService.getAlibabaCategoryAllWithCache();
        return AlibabaCategoryDTOMapping.INSTANCE.dtoToVOList(categories);
    }

    @Override
    public List<ProductInfoVO> loadTrendingProducts() {
        log.info("加载热门商品列表（带缓存和同步入库）");

        try {
            // 调用Repository获取推荐商品数据（使用优化后的缓存+同步方法）
            PageDTO<ProductInfoDTO> recommendResult = pdcProductMappingRepository
                .recommendProducts(LanguageEnum.EN, 1, 20);

            if (recommendResult == null || recommendResult.getRecords() == null) {
                log.debug("推荐商品数据为空");
                return Collections.emptyList();
            }

            // 转换为前端VO对象，现在ID已经正确同步到数据库
            List<ProductInfoVO> productInfoVOList = FrontendProductConvert.INSTANCE.toProductInfoVOList(recommendResult
                .getRecords());

            log.info("成功加载热门商品，数量: {}，ID同步状态：正常", productInfoVOList.size());
            return productInfoVOList;

        } catch (Exception e) {
            log.error("加载热门商品失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> loadHotCategoryKeywords() {
        log.info("加载热词类目");

        try {
            // 获取热门类目关键词
            List<AlibabaCategoryTreeDTO> hotCategories = sysAlibabaCategoryService
                .getTop20AlibabaCategoryByLevelOrderBySort(3);

            if (hotCategories != null) {
                return hotCategories.stream()
                    .map(category -> category.getChineseName() != null
                        ? category.getChineseName()
                        : category.getTranslatedName())
                    .filter(name -> name != null && !name.trim().isEmpty())
                    .limit(10) // 限制返回10个热门关键词
                    .toList();
            }

            return Collections.emptyList();

        } catch (Exception e) {
            log.error("加载热门类目关键词失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ProductInfoVO> searchProductsByElectronicsWhitCache() {
        log.info("搜索电子产品（带缓存和同步入库）");

        try {
            // 使用Repository的搜索功能，支持同步入库
            ProductSearchRequestDTO request = ProductSearchRequestDTO.builder()
                // 货盘数据 电子产品 - 使用具体的分类ID或关键词
                .productCollectionId(String.valueOf(262069983L))
                .page(1)
                .pageSize(10)
                .build();

            // 调用带同步功能的搜索方法
            PageDTO<ProductInfoDTO> electronicsResult = pdcProductMappingRepository
                .searchProductInfoListSyncWithCache(request, false);

            if (electronicsResult == null || electronicsResult.getRecords() == null) {
                log.debug("电子产品搜索结果为空");
                return Collections.emptyList();
            }

            // 转换为前端VO对象，直接映射字段
            List<ProductInfoVO> productInfoVOList = FrontendProductConvert.INSTANCE
                .toProductInfoVOList(electronicsResult.getRecords());

            log.info("成功搜索电子产品，数量: {}", productInfoVOList.size());
            return productInfoVOList;

        } catch (Exception e) {
            log.error("搜索电子产品失败", e);
            return Collections.emptyList();
        }
    }

}
