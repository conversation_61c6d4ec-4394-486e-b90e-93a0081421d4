/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.TzShoppingCart;
import com.fulfillmen.shop.domain.entity.enums.ShoppingCartProductType;
import com.fulfillmen.shop.domain.util.CurrencyConversionUtils;
import com.fulfillmen.shop.domain.vo.ShoppingCartVO;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.util.StringUtils;

/**
 * 购物车转换器
 *
 * <AUTHOR>
 * @date 2025/6/16 16:51
 * @description: 购物车转换器
 * @since 1.0.0
 */
@Mapper(componentModel = "spring")
public interface FrontendShoppingCartConvert {

    FrontendShoppingCartConvert INSTANCE = Mappers.getMapper(FrontendShoppingCartConvert.class);

    /**
     * 将 TzShoppingCart 转换为 ShoppingCartVO
     *
     * 注意：这里不使用 @Mapping 注解，而是使用 default 方法手动转换
     * 避免 MapStruct 的自动类型推断导致的字段映射错误
     *
     * @param entity TzShoppingCart 对象
     * @return ShoppingCartVO 对象
     */
    default ShoppingCartVO toVo(TzShoppingCart entity) {
        if (entity == null) {
            return null;
        }

        ShoppingCartVO vo = new ShoppingCartVO();

        // 基础字段映射
        vo.setId(entity.getId());
        vo.setProductId(entity.getProductId());
        vo.setTitle(entity.getTitle());
        vo.setTitleTrans(entity.getTitleTrans());
        vo.setMainImgUrl(entity.getMainImgUrl());
        vo.setSkuId(entity.getSkuId());
        vo.setSpecs(entity.getSpecs());
        vo.setQuantity(entity.getQuantity());
        vo.setMinOrderQuantity(entity.getMinOrderQuantity());
        vo.setIsChecked(entity.getIsChecked());

        // 卖家信息映射
        vo.setSellerOpenId(entity.getSourcePlatformSellerOpenId());
        vo.setSellerName(entity.getSourcePlatformSellerName());

        // 平台产品ID映射 - 使用购物车中存储的PDC映射ID
        if (entity.getSourcePlatformPdcId() != null) {
            vo.setPlatformProductId(String.valueOf(entity.getSourcePlatformPdcId()));
        } else {
            // 如果没有PDC映射ID，使用产品ID作为fallback
            vo.setPlatformProductId(String.valueOf(entity.getProductId()));
        }

        // 价格字段映射 - 明确区分CNY和USD
        vo.setPrice(entity.getPrice());                    // CNY 价格
        vo.setTotalPrice(entity.getTotalPrice());          // CNY 总价
        vo.setUsdPrice(calculateUsdPrice(entity.getPrice()));        // USD 价格
        vo.setUsdTotalPrice(calculateUsdPrice(entity.getTotalPrice())); // USD 总价

        return vo;
    }

    /**
     * 将 List<TzShoppingCart> 转换为 List<ShoppingCartVO>
     *
     * @param entityList TzShoppingCart 列表
     * @return ShoppingCartVO 列表
     */
    default List<ShoppingCartVO> toVoList(List<TzShoppingCart> entityList) {
        return entityList.stream().map(this::toVo).collect(Collectors.toList());
    }

    /**
     * 将 Page<TzShoppingCart> 转换为 Page<ShoppingCartVO>
     *
     * @param entityPage TzShoppingCart 分页对象
     * @return ShoppingCartVO 分页对象
     */
    @Mappings({@Mapping(target = "pageIndex", expression = "java((int)entityPage.getCurrent())"),
        @Mapping(target = "pageSize", expression = "java((int)entityPage.getSize())")})
    PageDTO<ShoppingCartVO> toVoPage(IPage<TzShoppingCart> entityPage);

    /**
     * 计算美元价格
     *
     * @param cnyPrice CNY价格
     * @return USD价格，如果转换失败则使用默认汇率
     */
    default BigDecimal calculateUsdPrice(BigDecimal cnyPrice) {
        if (cnyPrice == null) {
            return null;
        }

        // 尝试使用汇率服务转换
        BigDecimal usdPrice = CurrencyConversionUtils.cnyToUsd(cnyPrice);

        // 如果转换失败（返回null），使用默认汇率
        if (usdPrice == null) {
            // 使用默认汇率 1 CNY = 0.14 USD 进行兜底转换
            BigDecimal defaultRate = new BigDecimal("0.14");
            usdPrice = cnyPrice.multiply(defaultRate);
        }

        return usdPrice;
    }

    /**
     * 构建购物车基础信息
     *
     * @param userId     用户ID
     * @param productSpu 产品SPU信息
     * @param sku        SKU信息
     * @param quantity   数量
     * @param price      价格
     * @return 购物车构建器
     */
    static TzShoppingCart.TzShoppingCartBuilder buildShoppingCartBase(Long userId,
        TzProductSpu productSpu,
        TzProductSku sku,
        Integer quantity,
        BigDecimal price) {

        return TzShoppingCart.builder()
            .userId(userId)
            .productId(productSpu.getId())
            .skuId(sku.getId())
            .quantity(quantity)
            .minOrderQuantity(productSpu.getMinOrderQuantity())
            .price(price)
            .title(productSpu.getTitle())
            .titleTrans(productSpu.getTitleTrans())
            .totalPrice(price.multiply(BigDecimal.valueOf(quantity)))
            .sourcePlatform(ShoppingCartProductType.ALIBABA)
            .mainImgUrl(StringUtils.hasText(sku.getImage()) ? sku.getImage() : productSpu.getMainImage())
            .specs(sku.getSpecs())
            // 新增字段
            .sourcePlatformPdcId(productSpu.getPdcProductMappingId())
            .sourcePlatformSellerOpenId(productSpu.getSourcePlatformSellerOpenId())
            .sourcePlatformSellerName(productSpu.getSourcePlatformSellerName());
    }

}
