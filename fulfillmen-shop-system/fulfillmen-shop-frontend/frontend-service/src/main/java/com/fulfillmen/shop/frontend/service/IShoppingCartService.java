/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service;

import com.fulfillmen.shop.domain.req.ShoppingCartReq;
import com.fulfillmen.shop.domain.req.ShoppingCartReq.SearchShoppingCartRecordDTO;
import com.fulfillmen.shop.domain.vo.ShoppingCartVO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28 09:40
 * @description: todo
 * @since 1.0.0
 */
public interface IShoppingCartService {

    /**
     * 从购物车中移除商品
     * <pre>
     * 1. 如果 skuIds 为空，则移除 productId 下的所有商品
     * 2. 如果 skuIds 不为空，则移除 productId 下的指定 skuIds 的商品
     * </pre>
     *
     * @param productId 商品 Id
     * @param skuIds    skuIds 可选
     */
    void removeItemsToCart(String productId, List<String> skuIds);

    /**
     * 获取购物车中的商品列表
     * <pre>
     * 1. 如果 keyword 为空，则返回所有商品
     * 2. 如果 keyword 不为空，则返回所有包含 keyword 的商品
     * 3. 降序 按最近修改时间降序排序
     * </pre>
     *
     * @param searchShoppingCartRecordDTO 获取购物车列表
     * @return 购物车中的商品列表
     */
    List<ShoppingCartVO> getCartItems(SearchShoppingCartRecordDTO searchShoppingCartRecordDTO);

    /**
     * 添加商品到购物车
     *
     * @param addProductToCartRecordDTO 添加商品到购物车 DTO
     */
    void addOrUpdateToCart(@Valid ShoppingCartReq.AddProductToCartRecordDTO addProductToCartRecordDTO);

    /**
     * 清空购物车
     */
    void clearItemsToCart();

    /**
     * 获取购物车列表里某个商品列表
     *
     * @return 某个产品下的 sku 列表
     */
    List<ShoppingCartVO> getCartItems(String productId);

    /**
     * 获取购物车列表
     *
     * @param searchShoppingCartRecordDTO 搜索条件
     * @return 购物车列表
     */
    List<ShoppingCartVO> listCartItems(SearchShoppingCartRecordDTO searchShoppingCartRecordDTO);

    /**
     * 更新购物车中的商品数量
     *
     * @param productId 商品 Id
     * @param skuId     skuId
     * @param quantity  数量
     */
    void updateProductQuantity(String productId, String skuId, Integer quantity);

    /**
     * 更新购物车商品选中状态
     *
     * @param id        购物车ID
     * @param isChecked 是否选中（0否，1是）
     */
    void updateItemCheckedStatus(Long id, Integer isChecked);

    /**
     * 批量更新购物车商品选中状态
     *
     * @param ids       购物车ID列表
     * @param isChecked 是否选中（0否，1是）
     */
    void batchUpdateItemCheckedStatus(List<Long> ids, Integer isChecked);

}
