/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.math.BigDecimal;
import java.util.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.feiniaojin.gracefulresponse.GracefulResponseException;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.properties.CommonConstants;
import com.fulfillmen.shop.dao.mapper.TzShoppingCartMapper;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.TzShoppingCart;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.domain.entity.enums.ShoppingCartProductType;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.domain.entity.json.AttrJson;
import com.fulfillmen.shop.domain.req.ShoppingCartReq.AddProductToCartRecordDTO;
import com.fulfillmen.shop.domain.req.ShoppingCartReq.AddSkuInfoToCartRecordDTO;
import com.fulfillmen.shop.domain.req.ShoppingCartReq.SearchShoppingCartRecordDTO;
import com.fulfillmen.shop.domain.vo.ShoppingCartVO;
import com.fulfillmen.shop.manager.service.IProductSyncService;

/**
 * 购物车服务测试用例
 *
 * <AUTHOR>
 * @date 2025/6/14
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("购物车服务测试")
class ShoppingCartServiceImplTest {

    @Mock
    private TzShoppingCartMapper tzShoppingCartMapper;

    @Mock
    private IProductSyncService productSyncService;

    @InjectMocks
    private ShoppingCartServiceImpl shoppingCartService;

    private static final Long TEST_USER_ID = 1001L;
    private static final Long TEST_TENANT_ID = 100L;
    private static final String TEST_PLATFORM_PRODUCT_ID = "12345";
    private static final String TEST_PLATFORM_SKU_ID = "67890";

    private TzProductSpu testProductSpu;
    private TzProductDTO testProductDTO;
    private TzProductSku testProductSku;
    private TzShoppingCart testShoppingCart;
    private AddProductToCartRecordDTO testAddProductRequest;

    @BeforeEach
    void setUp() {
        initTestData();
    }

    private void initTestData() {
        // 初始化测试用的 TzProductSpu
        testProductSpu = TzProductSpu.builder()
            .id(2001L)
            .title("测试商品")
            .titleTrans("Test Product")
            .mainImage("https://example.com/image.jpg")
            .isSingleItem(TzProductSpuSingleItemEnum.NO)
            .pdcProductMappingId(1001L)
            .sourcePlatformSellerOpenId("seller123")
            .sourcePlatformSellerName("Test Seller")
            .build();

        // 初始化测试用的 TzProductDTO
        testProductDTO = TzProductDTO.builder()
            .id(2001L)
            .title("测试商品")
            .titleTrans("Test Product")
            .mainImage("https://example.com/image.jpg")
            .isSingleItem(TzProductSpuSingleItemEnum.NO)
            .pdcProductMappingId(1001L)
            .sourcePlatformSellerOpenId("seller123")
            .sourcePlatformSellerName("Test Seller")
            .build();

        // 创建测试SKU
        List<AttrJson> specs = List.of();

        testProductSku = TzProductSku.builder()
            .id(3001L)
            .spuId(testProductSpu.getId())
            .platformCode(PlatformCodeEnum.PLATFORM_CODE_1688)
            .platformProductId(TEST_PLATFORM_PRODUCT_ID)
            .platformSku(TEST_PLATFORM_SKU_ID)
            .price(new BigDecimal("100.00"))
            .dropShippingPrice(new BigDecimal("90.00"))
            .specs(specs)
            .image("http://example.com/sku.jpg")
            .tenantId(TEST_TENANT_ID)
            .build();

        // 创建测试购物车记录
        testShoppingCart = TzShoppingCart.builder()
            .id(4001L)
            .userId(TEST_USER_ID)
            .sourcePlatform(ShoppingCartProductType.ALIBABA)
            .productId(testProductSpu.getId())
            .skuId(testProductSku.getId())
            .title(testProductSpu.getTitle())
            .titleTrans(testProductSpu.getTitleTrans())
            .price(testProductSku.getDropShippingPrice())
            .quantity(2)
            .totalPrice(new BigDecimal("180.00"))
            .mainImgUrl(testProductSku.getImage())
            .specs(testProductSku.getSpecs())
            .tenantId(TEST_TENANT_ID)
            .build();

        // 创建测试添加购物车请求
        AddSkuInfoToCartRecordDTO skuInfo = new AddSkuInfoToCartRecordDTO();
        skuInfo.setSkuId(Long.valueOf(TEST_PLATFORM_SKU_ID));
        skuInfo.setQty(2);

        testAddProductRequest = new AddProductToCartRecordDTO();
        testAddProductRequest.setProductId(Long.valueOf(TEST_PLATFORM_PRODUCT_ID));
        testAddProductRequest.setPurchaseSkuInfos(Arrays.asList(skuInfo));
    }

    @Test
    @DisplayName("添加单品到购物车 - 成功")
    void testAddSingleItemToCart_Success() {
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            // Mock UserContextHolder
            mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);
            mockedUserContext.when(UserContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);

            // Mock 购物车数量检查
            when(tzShoppingCartMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(10L);

            // Mock 产品同步服务
            when(productSyncService.syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID)).thenReturn(testProductDTO);
            when(productSyncService.isSingleItem(TEST_PLATFORM_PRODUCT_ID)).thenReturn(true);
            when(productSyncService.getSingleItemDefaultSku(testProductDTO.getId())).thenReturn(testProductSku);

            // Mock 购物车查询（不存在）
            when(tzShoppingCartMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

            // Mock 批量插入
            when(tzShoppingCartMapper.insertBatch(anyList())).thenReturn(true);

            // 执行测试
            assertDoesNotThrow(() -> {
                shoppingCartService.addOrUpdateToCart(testAddProductRequest);
            });

            // 验证调用
            verify(productSyncService).syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID);
            verify(productSyncService).isSingleItem(TEST_PLATFORM_PRODUCT_ID);
            verify(productSyncService).getSingleItemDefaultSku(testProductDTO.getId());
            verify(tzShoppingCartMapper).insertBatch(anyList());
        }
    }

    @Test
    @DisplayName("添加多规格商品到购物车 - 成功")
    void testAddMultiSkuItemToCart_Success() {
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            // Mock UserContextHolder
            mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);
            mockedUserContext.when(UserContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);

            // Mock 购物车数量检查
            when(tzShoppingCartMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(10L);

            // Mock 产品同步服务
            when(productSyncService.syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID)).thenReturn(testProductDTO);
            when(productSyncService.isSingleItem(TEST_PLATFORM_PRODUCT_ID)).thenReturn(false);
            when(productSyncService.getSkuListBySpuId(testProductDTO.getId())).thenReturn(Arrays
                .asList(testProductSku));

            // Mock 购物车查询（不存在）
            when(tzShoppingCartMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

            // Mock 批量插入
            when(tzShoppingCartMapper.insertBatch(anyList())).thenReturn(true);

            // 执行测试
            assertDoesNotThrow(() -> {
                shoppingCartService.addOrUpdateToCart(testAddProductRequest);
            });

            // 验证调用
            verify(productSyncService).syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID);
            verify(productSyncService).isSingleItem(TEST_PLATFORM_PRODUCT_ID);
            verify(productSyncService).getSkuListBySpuId(testProductDTO.getId());
            verify(tzShoppingCartMapper).insertBatch(anyList());
        }
    }

    @Test
    @DisplayName("更新已存在购物车商品数量 - 成功")
    void testUpdateExistingCartItem_Success() {
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            // Mock UserContextHolder
            mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);
            mockedUserContext.when(UserContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);

            // Mock 购物车数量检查
            when(tzShoppingCartMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(10L);

            // Mock 产品同步服务
            when(productSyncService.syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID)).thenReturn(testProductDTO);
            when(productSyncService.isSingleItem(TEST_PLATFORM_PRODUCT_ID)).thenReturn(true);
            when(productSyncService.getSingleItemDefaultSku(testProductDTO.getId())).thenReturn(testProductSku);

            // Mock 购物车查询（存在）
            when(tzShoppingCartMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testShoppingCart);

            // Mock 更新
            when(tzShoppingCartMapper.updateById(any(TzShoppingCart.class))).thenReturn(1);

            // 执行测试
            assertDoesNotThrow(() -> {
                shoppingCartService.addOrUpdateToCart(testAddProductRequest);
            });

            // 验证调用
            verify(tzShoppingCartMapper).updateById(any(TzShoppingCart.class));
            verify(tzShoppingCartMapper, never()).insertBatch(anyList());
        }
    }

    @Test
    @DisplayName("产品同步失败 - 抛出异常")
    void testProductSyncFailed_ThrowsException() {
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            // Mock UserContextHolder
            mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);

            // Mock 购物车数量检查
            when(tzShoppingCartMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(10L);

            // Mock 产品同步失败
            when(productSyncService.syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID)).thenReturn(null);

            // 执行测试并验证异常
            GracefulResponseException exception = assertThrows(GracefulResponseException.class, () -> {
                shoppingCartService.addOrUpdateToCart(testAddProductRequest);
            });

            assertEquals("Product sync failed", exception.getMessage());
        }
    }

    @Test
    @DisplayName("单品默认SKU不存在 - 抛出异常")
    void testSingleItemSkuNotFound_ThrowsException() {
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            // Mock UserContextHolder
            mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);

            // Mock 购物车数量检查
            when(tzShoppingCartMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(10L);

            // Mock 产品同步服务
            when(productSyncService.syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID)).thenReturn(testProductDTO);
            when(productSyncService.isSingleItem(TEST_PLATFORM_PRODUCT_ID)).thenReturn(true);
            when(productSyncService.getSingleItemDefaultSku(testProductDTO.getId())).thenReturn(null);

            // 执行测试并验证异常
            GracefulResponseException exception = assertThrows(GracefulResponseException.class, () -> {
                shoppingCartService.addOrUpdateToCart(testAddProductRequest);
            });

            assertEquals("Single item SKU not found", exception.getMessage());
        }
    }

    @Test
    @DisplayName("多规格商品SKU不存在 - 抛出异常")
    void testMultiSkuItemNotFound_ThrowsException() {
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            // Mock UserContextHolder
            mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);

            // Mock 购物车数量检查
            when(tzShoppingCartMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(10L);

            // Mock 产品同步服务
            when(productSyncService.syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID)).thenReturn(testProductDTO);
            when(productSyncService.isSingleItem(TEST_PLATFORM_PRODUCT_ID)).thenReturn(false);
            when(productSyncService.getSkuListBySpuId(testProductDTO.getId())).thenReturn(Collections.emptyList());

            // 执行测试并验证异常
            GracefulResponseException exception = assertThrows(GracefulResponseException.class, () -> {
                shoppingCartService.addOrUpdateToCart(testAddProductRequest);
            });

            assertTrue(exception.getMessage().contains("SKU not found"));
        }
    }

    @Test
    @DisplayName("购物车数量达到上限 - 抛出异常")
    void testCartCountLimit_ThrowsException() {
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            // Mock UserContextHolder
            mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);

            // Mock 购物车数量达到上限
            when(tzShoppingCartMapper.selectCount(any(LambdaQueryWrapper.class)))
                .thenReturn((long) CommonConstants.SHOPPING_CART_MAX_LIMIT);

            // 执行测试并验证异常
            GracefulResponseException exception = assertThrows(GracefulResponseException.class, () -> {
                shoppingCartService.addOrUpdateToCart(testAddProductRequest);
            });

            assertEquals("The number of items in the shopping cart has reached the limit.", exception.getMessage());
        }
    }

    @Test
    @DisplayName("商品价格无效 - 抛出异常")
    void testInvalidPrice_ThrowsException() {
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            // Mock UserContextHolder
            mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);

            // Mock 购物车数量检查
            when(tzShoppingCartMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(10L);

            // 创建价格为0的SKU
            TzProductSku invalidPriceSku = TzProductSku.builder()
                .id(3002L)
                .spuId(testProductDTO.getId())
                .price(BigDecimal.ZERO)
                .dropShippingPrice(null)
                .build();

            // Mock 产品同步服务
            when(productSyncService.syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID)).thenReturn(testProductDTO);
            when(productSyncService.isSingleItem(TEST_PLATFORM_PRODUCT_ID)).thenReturn(true);
            when(productSyncService.getSingleItemDefaultSku(testProductDTO.getId())).thenReturn(invalidPriceSku);

            // 执行测试并验证异常
            GracefulResponseException exception = assertThrows(GracefulResponseException.class, () -> {
                shoppingCartService.addOrUpdateToCart(testAddProductRequest);
            });

            assertEquals("Product price not available", exception.getMessage());
        }
    }

    @Test
    @DisplayName("删除购物车商品 - 成功")
    void testRemoveItemsToCart_Success() {
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            // Mock UserContextHolder
            mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);

            // Mock 删除操作
            when(tzShoppingCartMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(1);

            // 执行测试
            List<String> skuIds = Arrays.asList("3001", "3002");
            assertDoesNotThrow(() -> {
                shoppingCartService.removeItemsToCart(TEST_PLATFORM_PRODUCT_ID, skuIds);
            });

            // 验证调用
            verify(tzShoppingCartMapper).delete(any(LambdaQueryWrapper.class));
        }
    }

    @Test
    @DisplayName("获取购物车列表 - 成功")
    void testGetCartItems_Success() {
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            // Mock UserContextHolder
            mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);

            // Mock 查询结果
            when(tzShoppingCartMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Arrays
                .asList(testShoppingCart));

            // 创建搜索请求
            SearchShoppingCartRecordDTO searchRequest = new SearchShoppingCartRecordDTO();
            searchRequest.setKeyword("测试");

            // 执行测试
            List<ShoppingCartVO> result = shoppingCartService.getCartItems(searchRequest);

            // 验证结果
            assertNotNull(result);
            verify(tzShoppingCartMapper).selectList(any(LambdaQueryWrapper.class));
        }
    }

    @Test
    @DisplayName("获取指定产品的购物车项 - 成功")
    void testGetCartItemsByProductId_Success() {
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            // Mock UserContextHolder
            mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);

            // Mock 查询结果
            when(tzShoppingCartMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Arrays
                .asList(testShoppingCart));

            // 执行测试
            List<ShoppingCartVO> result = shoppingCartService.getCartItems(TEST_PLATFORM_PRODUCT_ID);

            // 验证结果
            assertNotNull(result);
            assertFalse(result.isEmpty());
            verify(tzShoppingCartMapper).selectList(any(LambdaQueryWrapper.class));
        }
    }

    @Test
    @DisplayName("获取指定产品的购物车项 - 空结果")
    void testGetCartItemsByProductId_EmptyResult() {
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            // Mock UserContextHolder
            mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);

            // Mock 空查询结果
            when(tzShoppingCartMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

            // 执行测试
            List<ShoppingCartVO> result = shoppingCartService.getCartItems(TEST_PLATFORM_PRODUCT_ID);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    @Test
    @DisplayName("清空购物车 - 成功")
    void testClearItemsToCart_Success() {
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            // Mock UserContextHolder
            mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);

            // Mock 删除操作
            when(tzShoppingCartMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(5);

            // 执行测试
            assertDoesNotThrow(() -> {
                shoppingCartService.clearItemsToCart();
            });

            // 验证调用
            verify(tzShoppingCartMapper).delete(any(LambdaQueryWrapper.class));
        }
    }

    @Test
    @DisplayName("更新购物车商品数量 - 成功")
    void testUpdateProductQuantity_Success() {
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            // Mock UserContextHolder
            mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);

            // Mock 查询购物车记录
            when(tzShoppingCartMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testShoppingCart);

            // Mock 更新操作
            when(tzShoppingCartMapper.updateById(any(TzShoppingCart.class))).thenReturn(1);

            // 执行测试
            assertDoesNotThrow(() -> {
                shoppingCartService.updateProductQuantity(TEST_PLATFORM_PRODUCT_ID, "3001", 5);
            });

            // 验证调用
            verify(tzShoppingCartMapper).selectOne(any(LambdaQueryWrapper.class));
            verify(tzShoppingCartMapper).updateById(any(TzShoppingCart.class));
        }
    }

    @Test
    @DisplayName("更新购物车商品数量 - 商品不存在")
    void testUpdateProductQuantity_ItemNotFound() {
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            // Mock UserContextHolder
            mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);

            // Mock 查询购物车记录（不存在）
            when(tzShoppingCartMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

            // 执行测试并验证异常
            GracefulResponseException exception = assertThrows(GracefulResponseException.class, () -> {
                shoppingCartService.updateProductQuantity(TEST_PLATFORM_PRODUCT_ID, "3001", 5);
            });

            assertEquals("The item is not in the shopping cart.", exception.getMessage());
        }
    }

    @Test
    @DisplayName("边界情况测试 - 空的SKU信息列表")
    void testAddToCart_EmptySkuList() {
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            // Mock UserContextHolder
            mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);

            // Mock 购物车数量检查
            when(tzShoppingCartMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(10L);

            // Mock 产品同步服务
            when(productSyncService.syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID)).thenReturn(testProductDTO);
            when(productSyncService.isSingleItem(TEST_PLATFORM_PRODUCT_ID)).thenReturn(true);
            when(productSyncService.getSingleItemDefaultSku(testProductDTO.getId())).thenReturn(testProductSku);

            // 创建空的SKU信息列表
            AddProductToCartRecordDTO emptyRequest = new AddProductToCartRecordDTO();
            emptyRequest.setProductId(Long.valueOf(TEST_PLATFORM_PRODUCT_ID));
            emptyRequest.setPurchaseSkuInfos(Collections.emptyList());

            // 执行测试
            assertDoesNotThrow(() -> {
                shoppingCartService.addOrUpdateToCart(emptyRequest);
            });

            // 验证不会执行插入操作
            verify(tzShoppingCartMapper, never()).insertBatch(anyList());
        }
    }

    @Test
    @DisplayName("边界情况测试 - 数量为0的SKU")
    void testAddToCart_ZeroQuantity() {
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            // Mock UserContextHolder
            mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);

            // Mock 购物车数量检查
            when(tzShoppingCartMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(10L);

            // Mock 产品同步服务
            when(productSyncService.syncProductByPlatformId(TEST_PLATFORM_PRODUCT_ID)).thenReturn(testProductDTO);
            when(productSyncService.isSingleItem(TEST_PLATFORM_PRODUCT_ID)).thenReturn(true);
            when(productSyncService.getSingleItemDefaultSku(testProductDTO.getId())).thenReturn(testProductSku);

            // 创建数量为0的SKU信息
            AddSkuInfoToCartRecordDTO zeroQtySkuInfo = new AddSkuInfoToCartRecordDTO();
            zeroQtySkuInfo.setSkuId(Long.valueOf(TEST_PLATFORM_SKU_ID));
            zeroQtySkuInfo.setQty(0);

            AddProductToCartRecordDTO zeroQtyRequest = new AddProductToCartRecordDTO();
            zeroQtyRequest.setProductId(Long.valueOf(TEST_PLATFORM_PRODUCT_ID));
            zeroQtyRequest.setPurchaseSkuInfos(Arrays.asList(zeroQtySkuInfo));

            // Mock 购物车查询（不存在）
            when(tzShoppingCartMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

            // Mock 批量插入
            when(tzShoppingCartMapper.insertBatch(anyList())).thenReturn(true);

            // 执行测试
            assertDoesNotThrow(() -> {
                shoppingCartService.addOrUpdateToCart(zeroQtyRequest);
            });

            // 验证调用
            verify(tzShoppingCartMapper).insertBatch(anyList());
        }
    }

}
