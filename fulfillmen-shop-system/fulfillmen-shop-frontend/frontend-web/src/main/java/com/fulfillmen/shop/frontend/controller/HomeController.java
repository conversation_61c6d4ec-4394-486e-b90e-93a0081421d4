/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.fulfillmen.shop.domain.res.AlibabaCategoryRes;
import com.fulfillmen.shop.domain.vo.ProductInfoVO;
import com.fulfillmen.shop.frontend.service.IHomeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 首页相关API接口
 *
 * <p>
 * 提供首页所需的各类数据接口，包括：
 * <ul>
 * <li>商品分类数据</li>
 * <li>首页导航数据</li>
 * <li>推荐商品数据</li>
 * </ul>
 * </p>
 *
 * <p>
 * 接口路径: /api/frontend/home</p>
 * <p>
 * 权限控制: 部分接口使用 @SaIgnore 注解标记为无需登录即可访问</p>
 *
 * <AUTHOR>
 * @date 2025/2/18 16:55
 * @since 1.0.0
 */
@Tag(name = "首页接口", description = "提供首页相关的数据接口，包括分类、导航、推荐商品等")
@SaIgnore
@RestController
@RequestMapping("/api/home")
public class HomeController {

    private final IHomeService homeService;

    public HomeController(IHomeService homeService) {
        this.homeService = homeService;
    }

    @Operation(summary = "获取全部商品分类列表", description = "获取完整的商品分类树形结构，包含所有层级的分类信息")
    @ApiResponse(responseCode = "200", description = "成功获取分类列表", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = AlibabaCategoryRes.class))))
    @GetMapping("/categories")
    public List<AlibabaCategoryRes> getCategories() {
        return homeService.loadCategories();
    }

    @Operation(summary = "获取热门商品推荐列表", description = "获取热门商品推荐列表")
    @ApiResponse(responseCode = "200", description = "成功获取热门商品推荐列表", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = ProductInfoVO.class))))
    @GetMapping("/trending-products")
    public List<ProductInfoVO> getTrendingProducts() {
        return homeService.loadTrendingProducts();
    }

    @Operation(summary = "获取热门类目关键词列表", description = "获取热门类目关键词列表")
    @ApiResponse(responseCode = "200", description = "成功获取热门类目关键词列表", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = String.class))))
    @GetMapping("/hot-category-keywords")
    public List<String> getHotCategoryKeywords() {
        return homeService.loadHotCategoryKeywords();
    }

    @Operation(summary = "获取(电子产品)货盘数据相关产品信息", description = "获取(电子产品)货盘数据相关产品信息")
    @ApiResponse(responseCode = "200", description = "成功获取商品列表", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = ProductInfoVO.class))))
    @GetMapping("/electronics-products")
    public List<ProductInfoVO> listProductsByElectronicsGoodsSearch() {
        return homeService.searchProductsByElectronicsWhitCache();
    }

}
