/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.controller;

import java.time.LocalDateTime;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.req.OrderReq;
import com.fulfillmen.shop.domain.req.OrderReq.CancelOrderRequest;
import com.fulfillmen.shop.domain.req.OrderReq.ConfirmReceiptRequest;
import com.fulfillmen.shop.domain.req.OrderReq.CreateOrderSubmitReq;
import com.fulfillmen.shop.domain.req.OrderReq.RefundApplicationRequest;
import com.fulfillmen.shop.domain.req.OrderReq.RefundApplicationVO;
import com.fulfillmen.shop.frontend.service.IFrontendOrderService;
import com.fulfillmen.shop.domain.vo.OrderPreviewVO;
import com.fulfillmen.shop.domain.vo.OrderSubmitVO;
import com.fulfillmen.shop.domain.vo.UserOrderDetailVO;
import com.fulfillmen.shop.domain.vo.UserPurchaseOrderListVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单模块
 *
 * <pre>
 * 1. 订单预览
 * 2. 订单创建
 * 3. 订单支付
 * 4. 订单取消
 * 5. 订单退款
 * 6. 订单退货
 * 7. 订单列表查询
 * 8. 订单详情查询
 * 9. 物流追踪
 * 10. 确认收货
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/6/23
 * @description 订单控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "订单接口", description = "前台订单相关接口")
@RequestMapping("/api/orders")
public class OrderController {

    private final IFrontendOrderService frontendOrderService;

    /**
     * 订单预览
     *
     * @param orderPreviewReq 订单预览请求
     * @return 订单预览结果
     */
    @PostMapping("/preview")
    @Operation(summary = "订单预览", description = "创建订单前的预览信息")
    public OrderPreviewVO previewOrder(@RequestBody OrderReq.OrderPreviewReq orderPreviewReq) {
        return frontendOrderService.previewOrder(orderPreviewReq);
    }

    /**
     * 提交订单
     *
     * @param orderSubmitReq 订单提交请求
     * @return 订单提交结果
     */
    @PostMapping("/submit")
    @Operation(summary = "提交订单", description = "用户提交订单")
    public OrderSubmitVO submitOrder(@RequestBody CreateOrderSubmitReq orderSubmitReq) {
        return frontendOrderService.submitOrder(orderSubmitReq);
    }

    /**
     * 根据令牌查询预览信息
     *
     * @param idempotentToken 幂等令牌
     * @return 订单预览结果
     */
    @GetMapping("/preview/{idempotentToken}")
    @Operation(summary = "根据令牌查询预览", description = "根据幂等令牌查询订单预览信息")
    public OrderPreviewVO getPreviewByToken(@PathVariable String idempotentToken) {
        return frontendOrderService.getPreviewByToken(idempotentToken);
    }

    /**
     * 分页查询用户订单列表
     */
    @GetMapping
    @Operation(summary = "分页查询订单列表", description = "用户查询自己的订单列表")
    public PageDTO<UserPurchaseOrderListVO> getOrderList(
        @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int page,
        @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") int size,
        @Parameter(description = "订单状态", example = "1") @RequestParam(required = false) Integer status,
        @Parameter(description = "搜索关键词", example = "商品名称") @RequestParam(required = false) String keyword,
        @Parameter(description = "开始时间", example = "2025-01-01 00:00:00") @RequestParam(required = false) LocalDateTime startTime,
        @Parameter(description = "结束时间", example = "2025-01-31 23:59:59") @RequestParam(required = false) LocalDateTime endTime) {

        log.info("用户查询订单列表: page={}, size={}, status={}, keyword={}, startTime={}, endTime={}", page, size, status,
            keyword, startTime, endTime);

        return frontendOrderService.getOrderList(page, size, status, keyword, startTime, endTime);
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/{orderNo}")
    @Operation(summary = "获取订单详情", description = "根据订单号获取订单详细信息")
    public UserOrderDetailVO getOrderDetail(
        @Parameter(description = "订单号", example = "PO202501030001") @PathVariable String orderNo) {

        log.info("用户查询订单详情: orderNo={}", orderNo);

        return frontendOrderService.getOrderDetail(orderNo);
    }

    /**
     * 获取订单物流追踪信息
     */
    @GetMapping("/{orderNo}/tracking")
    @Operation(summary = "获取物流追踪", description = "获取订单的物流追踪信息")
    public UserOrderDetailVO.TrackingInfo getOrderTracking(
        @Parameter(description = "订单号", example = "PO202501030001") @PathVariable String orderNo) {

        log.info("用户查询订单物流: orderNo={}", orderNo);

        UserOrderDetailVO.TrackingInfo trackingInfo = frontendOrderService.getOrderTracking(orderNo);
        return trackingInfo;
    }

    /**
     * 确认收货
     */
    @PostMapping("/{orderNo}/confirm-receipt")
    @Operation(summary = "确认收货", description = "用户确认收到商品")
    public void confirmReceipt(
        @Parameter(description = "订单号", example = "PO202501030001") @PathVariable String orderNo,
        @RequestBody(required = false) ConfirmReceiptRequest request) {

        log.info("用户确认收货: orderNo={}, request={}", orderNo, request);

        frontendOrderService.confirmReceipt(orderNo, request);
    }

    /**
     * 取消订单
     */
    @PostMapping("/{orderNo}/cancel")
    @Operation(summary = "取消订单", description = "用户取消订单")
    public void cancelOrder(
        @Parameter(description = "订单号", example = "PO202501030001") @PathVariable String orderNo,
        @RequestBody CancelOrderRequest request) {

        log.info("用户取消订单: orderNo={}, request={}", orderNo, request);

        frontendOrderService.cancelOrder(orderNo, request);
    }

    /**
     * 申请退款
     */
    @PostMapping("/{orderNo}/apply-refund")
    @Operation(summary = "申请退款", description = "用户申请订单退款")
    public RefundApplicationVO applyRefund(
        @Parameter(description = "订单号", example = "PO202501030001") @PathVariable String orderNo,
        @RequestBody RefundApplicationRequest request) {

        log.info("用户申请退款: orderNo={}, request={}", orderNo, request);

        RefundApplicationVO result = frontendOrderService.applyRefund(orderNo, request);
        return result;
    }

    /**
     * 如果采购订单创建 wms 订单失败，可以通过这个接口重试
     *
     * <pre>
     * WMS 订单同步
     * </pre>
     */
    @GetMapping("/sync-wms/{orderNo}")
    @Operation(summary = "同步WMS订单", description = "重试同步采购订单到WMS系统")
    public void orderSyncWms(
        @Parameter(description = "采购订单号", example = "PO202501030001") @PathVariable String orderNo) {
        log.info("采购订单同步 wms : [{}] ", orderNo);

        // 调用前端订单服务进行WMS同步
        frontendOrderService.syncOrderToWms(orderNo);

        log.info("采购订单同步 wms 完成 : [{}] ", orderNo);
    }

}
