/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.fulfillmen.shop.common.annotation.RateLimit;
import com.fulfillmen.shop.common.enums.RateLimitAlgorithm;
import com.fulfillmen.shop.common.enums.RateLimitKeyType;
import com.fulfillmen.shop.common.enums.RateLimitStorageType;
import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.req.AggregateSearchReq;
import com.fulfillmen.shop.frontend.service.IProductService;
import com.fulfillmen.shop.domain.vo.ProductDetailVO;
import com.fulfillmen.shop.domain.vo.ProductInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 商品搜索 1. 关键词搜索 2. 图片上传+搜索 3. 图片地址检索 4. offerId 搜索
 *
 * <AUTHOR>
 * @date 2025/2/25 19:02
 * @description: 商品搜索控制器，通过SearchService实现业务逻辑
 * @since 1.0.0
 */
@Slf4j
@SaIgnore
@Validated
@RestController
@RequestMapping("/api/products")
@RequiredArgsConstructor
@Tag(name = "产品接口", description = "产品相关接口")
public class ProductController {

    private final IProductService productService;

    /**
     * 获取商品详情
     *
     * @param productId 商品ID
     * @return 商品详情
     */
    @GetMapping("/{productId}")
    @Operation(summary = "获取商品详情信息", description = "获取商品详情信息，支持中英文字段")
    public ProductDetailVO getProductDetail(@PathVariable Long productId) {
        log.info("获取商品详情: productId={}", productId);
        return productService.getProductDetailVO(productId);
    }

    /**
     * 获取商品详情
     *
     * @param productId 商品ID
     * @return 商品详情
     */
    @GetMapping("/{productId}/sync")
    @Operation(summary = "重新同步商品详情信息", description = "重新同步商品详情信息")
    public ProductDetailVO reSyncProductDetail(@PathVariable Long productId) {
        log.info("获取商品详情: productId={}", productId);
        return productService.reSyncProductDetailVO(productId);
    }

    /**
     * 关键词搜索商品
     *
     * @param reqDTO 搜索请求对象
     * @return 搜索结果
     */
    @GetMapping("/search")
    @Operation(summary = "聚合搜索 - 1. 关键词搜索 2.相似图片搜索", description = "通过关键词搜索商品，支持分页、分类筛选、价格区间和排序，返回分页数据")
    public PageDTO<ProductInfoVO> searchByKeywordOrImageId(@Valid AggregateSearchReq reqDTO,
        @Parameter(description = "是否使用缓存，默认 true 使用缓存，false 将强制刷新缓存", example = "true", required = false) @RequestParam(required = false, defaultValue = "true") Boolean useCache) {
        log.info("关键词搜索商品: keyword={}, pageIndex={}, pageSize={}, reqDTO={}", reqDTO.getKeyword(), reqDTO.getPage(), reqDTO.getPageSize(), reqDTO);
        return productService.unifiedAggregateSearch(reqDTO, useCache);
    }

    /**
     * 上传图片获取图片ID
     *
     * @param file 图片文件，限制大小2MB
     * @return 图片ID
     */
    @PostMapping(value = "/upload-image", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "上传图片获取图片ID", description = "上传图片文件（最大2MB）并返回图片ID用于后续图片搜索")
    // 文件上传特殊限流：每IP每分钟最多20次上传，使用令牌桶算法
    @RateLimit(keyType = RateLimitKeyType.IP, storageType = RateLimitStorageType.REDIS, algorithm = RateLimitAlgorithm.TOKEN_BUCKET, rate = 20, window = 60, tokenRefillRate = 5, keyPrefix = "upload_rate_limit", description = "图片上传限流", includeHeaders = true)
    public String uploadImage(@RequestPart("file") MultipartFile file) {
        log.info("上传图片: fileName={}, contentType={}, size={}", file.getOriginalFilename(), file.getContentType(), file.getSize());
        return productService.uploadImage(file);
    }

    @Profile("dev")
    @GetMapping("/cache/{productId}")
    @Operation(summary = "清理产品详情缓存", description = "清理指定产品ID的详情缓存，用于测试和调试")
    public String clearProductDetailCache(@PathVariable String productId) {
        log.info("清理产品详情缓存: productId={}", productId);
        return productService.clearProductDetailCache(productId);
    }

}
