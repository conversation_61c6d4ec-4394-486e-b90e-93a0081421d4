/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.controller;

import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.fulfillmen.shop.common.annotation.RateLimit;
import com.fulfillmen.shop.common.enums.RateLimitAlgorithm;
import com.fulfillmen.shop.common.enums.RateLimitKeyType;
import com.fulfillmen.shop.common.enums.RateLimitStorageType;
import com.fulfillmen.shop.domain.req.ShoppingCartReq;
import com.fulfillmen.shop.frontend.service.IShoppingCartService;
import com.fulfillmen.shop.domain.vo.ShoppingCartVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;

/**
 * 购物车相关接口
 *
 * <AUTHOR>
 * @date 2025/4/28 18:07
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Tag(name = "购物车接口", description = "购物车 相关接口")
@Validated
@RestController
@RequestMapping("/api/users/shopping-cart")
public class ShoppingCartController {

    private final IShoppingCartService shoppingCartService;

    public ShoppingCartController(IShoppingCartService shoppingCartService) {
        this.shoppingCartService = shoppingCartService;
    }

    /**
     * 获取购物车列表
     *
     * @param searchShoppingCartRecordDTO 搜索条件（可选）
     * @return 购物车列表，包含卖家信息和USD价格等完整信息
     */
    @GetMapping
    @Operation(summary = "获取购物车列表", description = "获取用户的购物车列表，包含商品详情、价格信息和卖家信息")
    public List<ShoppingCartVO> getShoppingCartList(@Valid ShoppingCartReq.SearchShoppingCartRecordDTO searchShoppingCartRecordDTO) {
        List<ShoppingCartVO> cartItems = shoppingCartService.getCartItems(searchShoppingCartRecordDTO);
        log.debug("获取购物车列表，用户购物车商品数量: {}", cartItems.size());
        return cartItems;
    }

    @PostMapping
    @Operation(summary = "添加或更新产品进购物车", description = """
        添加或更新产品进购物车。
        请求体必须包含产品ID和要购买的SKU列表，每个SKU项都必须有skuId和数量。

        示例:
        ```json
        {
          "productId": "12345",
          "purchaseSkuInfos": [
            {
              "skuId": "sku001",
              "qty": 1
            },
            {
              "skuId": "sku002",
              "qty": 3
            }
          ]
        }
        ```
        """)
    // 添加商品限流：每用户每分钟最多60次添加操作
    @RateLimit(keyType = RateLimitKeyType.USER_ID, storageType = RateLimitStorageType.REDIS, algorithm = RateLimitAlgorithm.TOKEN_BUCKET, rate = 60, window = 60, tokenRefillRate = 20, keyPrefix = "add_cart_rate_limit", description = "添加购物车限流", includeHeaders = true)
    public void addProductToCart(@Valid @RequestBody ShoppingCartReq.AddProductToCartRecordDTO shoppingCartRequestDTO) {
        log.debug("添加产品到购物车，产品ID: {}, SKU数量: {}", shoppingCartRequestDTO.getProductId(), shoppingCartRequestDTO
            .getPurchaseSkuInfos() != null ? shoppingCartRequestDTO.getPurchaseSkuInfos().size() : 0);

        shoppingCartService.addOrUpdateToCart(shoppingCartRequestDTO);

        log.info("成功添加产品到购物车，产品ID: {}", shoppingCartRequestDTO.getProductId());
    }

    /**
     * 获取购物车列表里某个商品列表
     *
     * @param productId 产品ID
     * @return 某个产品下的 sku 列表，包含完整的商品信息和价格信息
     */
    @GetMapping("/{productId}")
    @Operation(summary = "根据产品 id 获取购物车列表", description = "根据产品 id 获取购物车列表，返回该产品的所有SKU信息")
    public List<ShoppingCartVO> getShoppingCartByProductId(@NotNull(message = "product id is required") @PathVariable String productId) {
        List<ShoppingCartVO> cartItems = shoppingCartService.getCartItems(productId);
        log.debug("根据产品ID {} 获取购物车商品，找到 {} 个SKU", productId, cartItems.size());
        return cartItems;
    }

    @PatchMapping("/{productId}")
    @Operation(summary = "更新购物车中的产品数量", description = "更新购物车中的产品数量")
    // 更新商品限流：每用户每分钟最多100次更新操作
    @RateLimit(keyType = RateLimitKeyType.USER_ID, storageType = RateLimitStorageType.REDIS, algorithm = RateLimitAlgorithm.SLIDING_WINDOW, rate = 100, window = 60, keyPrefix = "update_cart_rate_limit", description = "更新购物车限流", includeHeaders = true)
    public void updateProductQuantity(@NotNull(message = "product id is required") @PathVariable String productId,
        @NotNull(message = "sku id is required") @RequestParam String skuId,
        @NotNull(message = "quantity is required") @RequestParam Integer quantity) {
        log.debug("更新购物车商品数量，产品ID: {}, SKU ID: {}, 新数量: {}", productId, skuId, quantity);

        shoppingCartService.updateProductQuantity(productId, skuId, quantity);

        log.info("成功更新购物车商品数量，产品ID: {}, SKU ID: {}, 数量: {}", productId, skuId, quantity);
    }

    @DeleteMapping("/{productId}")
    @Operation(summary = "从购物车中移除产品", description = "从购物车中移除产品，skuIds 非必填。传了则移除指定的 sku，不传则移除某产品下的所有sku")
    // 删除商品限流：每用户每分钟最多50次删除操作
    @RateLimit(keyType = RateLimitKeyType.USER_ID, storageType = RateLimitStorageType.REDIS, algorithm = RateLimitAlgorithm.SLIDING_WINDOW, rate = 50, window = 60, keyPrefix = "remove_cart_rate_limit", description = "删除购物车商品限流", includeHeaders = true)
    public void removeItemsToCart(@NotNull(message = "product id is required") @PathVariable String productId,
        @RequestParam(required = false) List<String> skuIds) {
        log.debug("从购物车移除商品，产品ID: {}, SKU IDs: {}", productId, skuIds);

        shoppingCartService.removeItemsToCart(productId, skuIds);

        if (skuIds == null || skuIds.isEmpty()) {
            log.info("成功从购物车移除整个产品，产品ID: {}", productId);
        } else {
            log.info("成功从购物车移除指定SKU，产品ID: {}, SKU数量: {}", productId, skuIds.size());
        }
    }

    @DeleteMapping()
    @Operation(summary = "清空购物车", description = "清空购物车")
    // 清空购物车限流：每用户每小时最多10次清空操作
    @RateLimit(keyType = RateLimitKeyType.USER_ID, storageType = RateLimitStorageType.REDIS, algorithm = RateLimitAlgorithm.FIXED_WINDOW, rate = 10, window = 3600, keyPrefix = "clear_cart_rate_limit", description = "清空购物车限流", includeHeaders = true)
    public void clearItemsToCart() {
        log.debug("用户请求清空购物车");

        shoppingCartService.clearItemsToCart();

        log.info("成功清空用户购物车");
    }

    /**
     * 更新购物车商品选中状态
     *
     * @param id        购物车ID
     * @param isChecked 是否选中（0否，1是）
     * @return 操作结果
     */
    @PatchMapping("/checked")
    @Operation(summary = "更新购物车商品选中状态")
    public void updateItemCheckedStatus(@RequestParam @Parameter(description = "购物车ID") Long id,
        @RequestParam @Parameter(description = "是否选中（0否，1是）") Integer isChecked) {
        shoppingCartService.updateItemCheckedStatus(id, isChecked);
    }

    /**
     * 批量更新购物车商品选中状态
     *
     * @param request 批量更新请求
     * @return 操作结果
     */
    @PostMapping("/checked/batch")
    @Operation(summary = "批量更新购物车商品选中状态")
    public void batchUpdateItemCheckedStatus(@RequestBody @Parameter(description = "批量更新请求") ShoppingCartReq.BatchUpdateCheckedRequest request) {
        shoppingCartService.batchUpdateItemCheckedStatus(request.getIds(), request.getIsChecked());
    }

}
