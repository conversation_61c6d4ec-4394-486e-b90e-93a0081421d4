/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.helper;

import com.fulfillmen.shop.common.context.OrderContext;
import com.fulfillmen.shop.common.enums.FulfillmenValidationCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContext;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContextHolder;
import com.fulfillmen.shop.domain.dto.order.OrderItemInfo;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierMultipleOrdersEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.domain.req.OrderReq.CreateOrderSubmitReq;
import com.fulfillmen.shop.domain.util.CurrencyConversionUtils;
import com.fulfillmen.shop.domain.vo.OrderPreviewVO;
import com.fulfillmen.shop.domain.vo.OrderSubmitVO;
import com.fulfillmen.support.alibaba.api.request.model.TradeFastAddress;
import com.fulfillmen.support.alibaba.api.request.model.TradeFastCargo;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import me.ahoo.cosid.IdGenerator;
import me.ahoo.cosid.provider.DefaultIdGeneratorProvider;
import org.springframework.util.StringUtils;

/**
 * 订单上下文构建助手类。
 * <p>
 * 该工具类封装了在订单创建流程中所有与数据处理和实体构建相关的逻辑。 主要职责包括： 1. 将前端请求的商品按供应商进行分组。 2. 校验商品是否满足最小起订量等业务规则。 3. 创建采购主订单 ({@link TzOrderPurchase})。 4. 为每个供应商创建对应的供应商订单 ({@link TzOrderSupplier})。 5.
 * 创建所有订单的商品明细 ({@link TzOrderItem})。 6. 将上述所有实体聚合到 {@link OrderContext} 中，供上层服务使用。
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@UtilityClass
@Slf4j
public class OrderContextHelper {

    /**
     * 默认服务费率（如果租户未配置）
     */
    private static final BigDecimal DEFAULT_SERVICE_FEE_RATE = new BigDecimal("0.15");

    /**
     * 构建订单上下文 (OrderContext)，这是创建订单所需的所有数据的聚合根。
     *
     * @param orderSubmitReq  订单提交请求
     * @param userId          用户ID
     * @param skuQuantityMap  商品SKU与购买数量的Map
     * @param productSkuList  商品SKU实体列表
     * @param spuMap          商品SPU实体的Map，以SPU ID为键
     * @param shoppingCartIds 购物车ID列表
     * @param previewVO       订单预览VO，包含预览价格信息
     * @return 包含所有待创建订单实体的上下文对象
     */
    public OrderContext buildContext(CreateOrderSubmitReq orderSubmitReq, Long userId, Map<Long, Integer> skuQuantityMap, List<TzProductSku> productSkuList,
        Map<Long, TzProductSpu> spuMap, List<Long> shoppingCartIds, OrderPreviewVO previewVO) {

        Map<String, List<OrderItemInfo>> supplierGroupMap = groupBySupplier(productSkuList, spuMap, skuQuantityMap);

        // 获取租户服务费配置
        BigDecimal serviceFeeRate = getTenantServiceFeeRate();

        // 构建预览价格映射，用于价格比较
        Map<Long, BigDecimal> previewPriceMap = buildPreviewPriceMap(previewVO);

        // 构建采购主订单、供应商订单、订单项
        TzOrderPurchase purchaseOrder = createPurchaseOrder(orderSubmitReq, supplierGroupMap, userId, previewPriceMap, serviceFeeRate, previewVO);

        // 创建供应商订单
        List<TzOrderSupplier> supplierOrders = createSupplierOrders(purchaseOrder, supplierGroupMap, orderSubmitReq, previewPriceMap, serviceFeeRate);
        // 创建订单项
        List<TzOrderItem> orderItems = Lists.newArrayList();
        supplierOrders.forEach(supplierOrder -> {
            log.debug("供应商订单创建: {}", supplierOrder);
            // 获取当前供应商ID
            String supplierId = supplierOrder.getSupplierId();
            // 只获取该供应商对应的商品项
            List<OrderItemInfo> supplierItems = supplierGroupMap.get(supplierId);
            if (supplierItems != null && !supplierItems.isEmpty()) {
                // 只为当前供应商创建订单项
                orderItems.addAll(createOrderItems(purchaseOrder, supplierOrder, supplierItems, previewPriceMap));
            }
        });

        return OrderContext.builder()
            .purchaseOrder(purchaseOrder)
            .supplierOrders(supplierOrders)
            .orderItems(orderItems)
            .shoppingCartIds(shoppingCartIds)
            .build();
    }

    /**
     * 获取租户服务费率
     *
     * @return 服务费率（小数形式，如 0.15 表示 15%）
     */
    private BigDecimal getTenantServiceFeeRate() {
        try {
            Optional<EnhancedTenantContext> contextOpt = EnhancedTenantContextHolder.getEnhancedTenantContext();
            if (contextOpt.isPresent()) {
                EnhancedTenantContext.TenantDetailInfo detailInfo = contextOpt.get().getDetailInfo();
                if (detailInfo != null && detailInfo.getServiceFee() != null) {
                    // 假设数据库中存储的是百分比整数，如 15 表示 15%
                    return new BigDecimal(detailInfo.getServiceFee()).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);
                }
            }
        } catch (Exception e) {
            log.warn("获取租户服务费配置失败，使用默认值: {}", DEFAULT_SERVICE_FEE_RATE, e);
        }

        log.debug("使用默认服务费率: {}", DEFAULT_SERVICE_FEE_RATE);
        return DEFAULT_SERVICE_FEE_RATE;
    }

    /**
     * 构建预览价格映射
     *
     * @param previewVO 订单预览VO
     * @return SKU ID 到预览单价的映射
     */
    private Map<Long, BigDecimal> buildPreviewPriceMap(OrderPreviewVO previewVO) {
        Map<Long, BigDecimal> priceMap = new HashMap<>();

        if (previewVO != null && previewVO.getProductItems() != null) {
            for (OrderPreviewVO.ProductItemPreview item : previewVO.getProductItems()) {
                if (item.getSkuId() != null && item.getUnitPrice() != null) {
                    priceMap.put(item.getSkuId(), item.getUnitPrice());
                }
            }
        }

        return priceMap;
    }

    /**
     * 比较并选择更优的价格（预览价格 vs SKU价格）
     *
     * @param sku             SKU实体
     * @param previewPriceMap 预览价格映射
     * @return 选择的价格
     */
    private BigDecimal selectBetterPrice(TzProductSku sku, Map<Long, BigDecimal> previewPriceMap) {
        BigDecimal skuPrice = sku.getPrice();
        BigDecimal previewPrice = previewPriceMap.get(sku.getId());

        if (previewPrice == null) {
            return skuPrice;
        }

        // 如果预览价格更高，使用预览价格；否则使用SKU价格
        if (previewPrice.compareTo(skuPrice) > 0) {
            log.debug("SKU {} 使用预览价格 {} 替代原价格 {}", sku.getId(), previewPrice, skuPrice);
            return previewPrice;
        }

        return skuPrice;
    }

    /**
     * 将商品列表按其供应商进行分组。
     * <p>
     * 使用1688平台卖家的OpenId (source_platform_seller_open_id) 作为供应商的唯一标识。
     *
     * @param productSkuList 商品SKU列表
     * @param spuMap         商品SPU Map
     * @param skuQuantityMap 商品SKU数量Map
     * @return 按供应商ID分组的商品信息Map
     */
    public Map<String, List<OrderItemInfo>> groupBySupplier(List<TzProductSku> productSkuList, Map<Long, TzProductSpu> spuMap, Map<Long, Integer> skuQuantityMap) {
        Map<String, List<OrderItemInfo>> groupMap = new HashMap<>();
        productSkuList.stream()
            .filter(sku -> {
                TzProductSpu spu = spuMap.get(sku.getSpuId());
                if (spu == null) {
                    // 如果商品没有SPU信息，则忽略此商品，并由上层服务记录日志。
                    log.warn("商品 SKU ID: {} 缺少SPU信息，已跳过。", sku.getId());
                    return false;
                }
                return true;
            })
            .filter(sku -> {
                TzProductSpu spu = spuMap.get(sku.getSpuId());
                String supplierId = spu.getSourcePlatformSellerOpenId();
                if (!StringUtils.hasText(supplierId)) {
                    // 如果商品没有供应商信息，则忽略此商品，并由上层服务记录日志。
                    log.warn("商品 SPU ID: {} (SKU ID: {}) 缺少供应商信息 (source_platform_seller_open_id)，已跳过。",
                        spu.getId(), sku.getId());
                    return false;
                }
                return true;
            })
            .forEach(sku -> {
                TzProductSpu spu = spuMap.get(sku.getSpuId());
                String supplierId = spu.getSourcePlatformSellerOpenId();

                OrderItemInfo itemInfo = OrderItemInfo.builder()
                    .sku(sku)
                    .spu(spu)
                    .quantity(skuQuantityMap.get(sku.getId()))
                    .build();

                groupMap.computeIfAbsent(supplierId, k -> new ArrayList<>()).add(itemInfo);
            });

        return groupMap;
    }

    /**
     * 批量检查订单中的Spu采购量，是否都符合其SPU设定的最低采购量要求。(只要采购量达到 Spu 最低采购量，即可提交订单)
     * <p>
     * 如果有任何一个不满足，将收集所有不满足的商品信息并抛出业务异常。
     * </p>
     *
     * @param productSkuList 商品SKU列表
     * @param skuQuantityMap 商品SKU与购买数量的Map
     * @param spuMap         商品SPU Map
     */
    public void checkSpuMinPurchaseQuantity(List<TzProductSku> productSkuList, Map<Long, Integer> skuQuantityMap, Map<Long, TzProductSpu> spuMap) {
        boolean hasError = false;
        Map<Long, String> errorSkuQuantityMap = new HashMap<>();
        // MODIFY: 2025/7/9 检查逻辑修改下，只需要满足 Spu 采购数量即可，如果有 sku 即是 sku 累加 qty >= moq 即可
        // 根据 spu 分组获取，sku 所有的数量 , key spuId, value sku 购买数量
        Map<Long, Integer> spuQuantityMap = productSkuList.stream()
            .collect(Collectors.groupingBy(TzProductSku::getSpuId, Collectors.summingInt(sku -> skuQuantityMap.getOrDefault(sku.getId(), 0))));
        for (Entry<Long, Integer> e : spuQuantityMap.entrySet()) {
            Long spuId = e.getKey();
            Integer totalQty = e.getValue();
            TzProductSpu spu = spuMap.get(spuId);
            // 小于 spu 数量
            if (spu != null && totalQty < spu.getMinOrderQuantity()) {
                log.warn("SPU {} 的采购量不符合最低采购量标准，最低采购量: {}", spuId, spu.getMinOrderQuantity());
                String errorMessage = String.format("current is %d, minimum is %d", totalQty,
                    spu.getMinOrderQuantity());
                errorSkuQuantityMap.put(spu.getId(), errorMessage);
                hasError = true;
            }
        }
        if (hasError) {
            // 将复杂对象（Map）格式化为人类可读的字符串
            String formattedDetails = errorSkuQuantityMap.entrySet().stream()
                .map(entry -> String.format("%s (%s)", entry.getKey(), entry.getValue()))
                .collect(Collectors.joining(", "));
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.PURCHASE_QUANTITY_NOT_MET_BATCH,
                formattedDetails);
        }
    }

    /**
     * 创建采购主订单 (TzOrderPurchase)。 此订单是整个采购的父订单，关联了所有的供应商子订单。
     *
     * @param orderSubmitReq   订单提交请求
     * @param supplierGroupMap 按供应商分组的商品信息
     * @param userId           下单用户ID
     * @param previewPriceMap  预览价格映射
     * @param serviceFeeRate   服务费率
     * @param previewVO        订单预览VO
     * @return 采购订单实体
     */
    private TzOrderPurchase createPurchaseOrder(CreateOrderSubmitReq orderSubmitReq, Map<String, List<OrderItemInfo>> supplierGroupMap, Long userId,
        Map<Long, BigDecimal> previewPriceMap, BigDecimal serviceFeeRate, OrderPreviewVO previewVO) {
        // 计算订单汇总信息
        int totalQuantity = supplierGroupMap.values().stream()
            .flatMap(List::stream)
            .mapToInt(OrderItemInfo::getQuantity)
            .sum();
        // 计算订单明细(sku)数量
        int lineItemCount = supplierGroupMap.values().stream()
            .mapToInt(List::size)
            .sum();

        // 获取当前汇率，默认只记录 美元作为当前汇率价格。作为美元快照
        BigDecimal exchangeRate = CurrencyConversionUtils.getExchangeRate("CNY", "USD");
        log.info("当前汇率: {}", exchangeRate);

        // 计算商品总金额（使用更精确的价格）
        BigDecimal goodsAmount = supplierGroupMap.values().stream()
            .flatMap(List::stream)
            .map(item -> selectBetterPrice(item.getSku(), previewPriceMap).multiply(BigDecimal.valueOf(item.getQuantity())))
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 从预览VO中获取运费和折扣信息
        BigDecimal totalFreight = BigDecimal.ZERO;
        BigDecimal totalDiscount = BigDecimal.ZERO;

        if (previewVO != null && previewVO.getPriceDetails() != null) {
            OrderPreviewVO.PriceDetails priceDetails = previewVO.getPriceDetails();
            totalFreight = priceDetails.getShippingAmount() != null ? priceDetails.getShippingAmount() : BigDecimal.ZERO;

            // 如果预览中的商品金额与计算的不同，使用预览中的金额
            if (priceDetails.getMerchandiseAmount() != null && priceDetails.getMerchandiseAmount().compareTo(goodsAmount) != 0) {
                log.info("使用预览商品金额 {} 替代计算金额 {}", priceDetails.getMerchandiseAmount(), goodsAmount);
                goodsAmount = priceDetails.getMerchandiseAmount();
            }
        }

        // 计算服务费
        BigDecimal serviceFee = goodsAmount.multiply(serviceFeeRate).setScale(2, RoundingMode.HALF_UP);

        // 计算订单总金额和净额
        BigDecimal totalAmount = goodsAmount.add(totalFreight).add(serviceFee).subtract(totalDiscount);

        IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");
        TradeFastAddress defaultAddress = buildDefaultAddress(userId);

        return TzOrderPurchase.builder()
            .id(idGenerator.generate())
            .purchaseOrderNo(generateOrderNo("C"))
            .buyerId(userId)
            .orderStatus(TzOrderPurchaseStatusEnum.PAYMENT_PENDING)
            .orderDate(LocalDateTime.now())
            .customerGoodsAmount(goodsAmount)
            .customerTotalFreight(totalFreight)
            .payableDiscountAmount(totalDiscount)
            .exchangeRateSnapshot(exchangeRate)
            .serviceFee(serviceFee)
            .customerTotalAmount(totalAmount)
            // 废弃
            .deliveryAddress(defaultAddress.getAddress())
            .postalCode(defaultAddress.getPostCode())
            .province(defaultAddress.getProvinceText())
            .city(defaultAddress.getCityText())
            .district(defaultAddress.getAreaText())
            .consigneeName(defaultAddress.getFullName())
            .consigneePhone(defaultAddress.getMobile())
            .supplierCount(supplierGroupMap.size())
            .lineItemCount(lineItemCount)
            .totalQuantity(totalQuantity)
            .purchaseNotes(orderSubmitReq.getBuyerMessage())
            .build();
    }

    /**
     * 创建供应商订单 (TzOrderSupplier)。 每个供应商订单都是采购主订单的子订单。
     * <p>
     * 创建供应商订单
     * 计算供应商的价格并提交商品
     * 注意：价格的计算可能存在个别差异有问题。总体需要测试整体流程
     * </p>
     *
     * @param purchaseOrder    采购主订单
     * @param supplierGroupMap 按供应商分组的商品信息
     * @param orderSubmitReq   订单提交请求
     * @param previewPriceMap  预览价格映射
     * @param serviceFeeRate   服务费率
     * @return 供应商订单列表
     */
    private List<TzOrderSupplier> createSupplierOrders(TzOrderPurchase purchaseOrder, Map<String, List<OrderItemInfo>> supplierGroupMap, CreateOrderSubmitReq orderSubmitReq,
        Map<Long, BigDecimal> previewPriceMap, BigDecimal serviceFeeRate) {
        List<TzOrderSupplier> supplierOrders = new ArrayList<>();
        IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");

        // 计算总的商品金额，用于按比例分配运费和折扣
        BigDecimal totalGoodsAmount = supplierGroupMap.values().stream()
            .flatMap(List::stream)
            .map(item -> selectBetterPrice(item.getSku(), previewPriceMap).multiply(BigDecimal.valueOf(item.getQuantity())))
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        for (Map.Entry<String, List<OrderItemInfo>> entry : supplierGroupMap.entrySet()) {
            String supplierId = entry.getKey();
            List<OrderItemInfo> items = entry.getValue();

            // 计算该供应商的商品金额
            BigDecimal supplierGoodsAmount = items.stream()
                .map(item -> selectBetterPrice(item.getSku(), previewPriceMap).multiply(BigDecimal.valueOf(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 按商品金额比例分配运费和折扣
            BigDecimal supplierFreightAmount = BigDecimal.ZERO;
            BigDecimal supplierDiscountAmount = BigDecimal.ZERO;

            if (totalGoodsAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal ratio = supplierGoodsAmount.divide(totalGoodsAmount, 4, RoundingMode.HALF_UP);
                supplierFreightAmount = purchaseOrder.getCustomerTotalAmount().multiply(ratio).setScale(2, RoundingMode.HALF_UP);
                supplierDiscountAmount = purchaseOrder.getPayableDiscountAmount().multiply(ratio).setScale(2, RoundingMode.HALF_UP);
            }

            // 计算该供应商的服务费
            BigDecimal supplierServiceFee = supplierGoodsAmount.multiply(serviceFeeRate).setScale(2, RoundingMode.HALF_UP);

            // 计算应付金额 = 商品金额 + 运费 + 服务费
            BigDecimal userPayableAmount = supplierGoodsAmount.add(supplierFreightAmount).add(supplierServiceFee);

            // 1688 原始付款信息 - 折扣 - 红包/优惠券
            BigDecimal originPayableAmount = userPayableAmount.subtract(supplierDiscountAmount);

            TzOrderSupplier supplierOrder = TzOrderSupplier.builder()
                .id(idGenerator.generate())
                .purchaseOrderId(purchaseOrder.getId())
                .supplierOrderNo(generateOrderNo("SO"))
                .platformCode(PlatformCodeEnum.PLATFORM_CODE_1688)
                .supplierId(supplierId)
                // 默认不是多个订单，1688 订单返回后影响该值
                .isMultipleOrders(TzOrderSupplierMultipleOrdersEnum.NO)
                .supplierName(items.get(0).getSpu().getSourcePlatformSellerName())
                .status(TzOrderSupplierStatusEnum.PENDING_PAYMENT)
                .orderDate(LocalDateTime.now())
                .customerGoodsAmount(supplierGoodsAmount)
                .customerFreightAmount(supplierFreightAmount)
                .serviceFee(supplierServiceFee)
                .payableAmountTotal(userPayableAmount)
                // 1688 显示的价格字段
                .payablePlusDiscountAmount(BigDecimal.ZERO)
                .payableDiscountAmount(supplierDiscountAmount)
                .payableAmountTotal(originPayableAmount)
                .payableGoodsAmount(supplierGoodsAmount)
                .payableFreightAmount(supplierFreightAmount)
                // 改价后将修改的 三个字段
                .actualPaymentAmount(userPayableAmount)
                .actualPaymentGoodsAmount(supplierGoodsAmount)
                .actualPaymentFreightAmount(supplierFreightAmount)
                .lineItemCount(items.size())
                .supplierNotes(orderSubmitReq.getBuyerMessage())
                .gmtCreated(LocalDateTime.now())
                .gmtModified(LocalDateTime.now())
                .build();

            supplierOrders.add(supplierOrder);

            log.debug("供应商 {} 订单创建: 商品金额={}, 运费={}, 服务费={}, 折扣={}, 应付金额={}",
                supplierId, supplierGoodsAmount, supplierFreightAmount, supplierServiceFee,
                supplierDiscountAmount, userPayableAmount);
        }
        return supplierOrders;
    }

    /**
     * 为订单中的每种商品（SKU）创建一个订单项 (TzOrderItem)。
     *
     * @param purchaseOrder   采购主订单
     * @param tzOrderSupplier 供应商订单
     * @param items           该供应商的商品信息列表
     * @param previewPriceMap 预览价格映射
     * @return 订单商品项列表
     */
    private List<TzOrderItem> createOrderItems(TzOrderPurchase purchaseOrder, TzOrderSupplier tzOrderSupplier, List<OrderItemInfo> items, Map<Long, BigDecimal> previewPriceMap) {

        List<TzOrderItem> orderItems = new ArrayList<>();
        int lineNumber = 1;
        IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");

        for (OrderItemInfo itemInfo : items) {
            // 单价
            BigDecimal unitPrice = selectBetterPrice(itemInfo.getSku(), previewPriceMap);
            // 订单项数量
            int orderedQuantity = itemInfo.getQuantity();
            // 计算行总金额
            BigDecimal lineTotalAmount = unitPrice.multiply(BigDecimal.valueOf(itemInfo.getQuantity()));
            TzOrderItem orderItem = TzOrderItem.builder()
                .id(idGenerator.generate())
                .purchaseOrderId(purchaseOrder.getId())
                .supplierOrderId(tzOrderSupplier.getId())
                .lineNumber(lineNumber++)
                .productSpuId(itemInfo.getSpu().getId())
                .productSkuId(itemInfo.getSku().getId())
                .platformProductId(itemInfo.getSku().getPlatformProductId())
                .platformSkuId(itemInfo.getSku().getPlatformSku())
                .platformSpecId(itemInfo.getSku().getPlatformSpecId())
                .productTitle(itemInfo.getSpu().getTitle())
                .productTitleEn(itemInfo.getSpu().getTitleTrans())
                .skuSpecs(itemInfo.getSku().getSpecs())
                .productImageUrl(itemInfo.getSku().getImage())
                .price(unitPrice)
                .quantity(BigDecimal.valueOf(orderedQuantity))
                .totalAmount(lineTotalAmount)
                .isSingleItem(itemInfo.getSpu().getIsSingleItem())
                .unitEn(
                    itemInfo.getSku().getUnitInfo() != null ? itemInfo.getSku().getUnitInfo().getUnitTrans()
                        : "Piece"
                )
                .unit(
                    itemInfo.getSku().getUnitInfo() != null ? itemInfo.getSku().getUnitInfo().getUnit()
                        : "件")
                .status(TzOrderItemStatusEnum.PENDING)
                .build();
            orderItems.add(orderItem);
        }
        return orderItems;
    }

    /**
     * 构建一个默认的收货地址。 注意：此地址为系统固定的仓库收货地址，而非用户的个人地址。
     *
     * @param userId 用户ID (当前用于填充收件人姓名以作区分)
     * @return 交易地址对象
     */
    public TradeFastAddress buildDefaultAddress(Long userId) {
        // TODO: 后期应将此硬编码地址移至系统配置管理中。
        return TradeFastAddress.builder()
            .fullName("中田 " + userId)
            .mobile("18575207550")
            // .phone("0571-88888888")
            .postCode("516000")
            .provinceText("广东省")
            .cityText("惠州市")
            .areaText("惠城区")
            .townText("江北")
            .address("广东省 惠州市 惠城区 汝湖镇 金泽物流园二期一号楼四楼-14101")
            .districtCode("441302")
            .build();
    }

    /**
     * 生成采购订单号 via: C + 日期(yyyyMMddHHmmssSSS) + 段号
     *
     * @param prefix 前缀
     * @return 采购订单号
     */
    public String generateOrderNo(String prefix) {
        IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("order_no");
        return prefix + idGenerator.generateAsString();
    }

    /**
     * 根据已创建的订单上下文，构建返回给前端的提交成功VO。 包含完整的价格信息（含USD价格）和中英文字段。
     *
     * @param orderContext 订单上下文
     * @return 订单提交VO
     */
    public OrderSubmitVO buildOrderSubmitResponse(OrderContext orderContext) {
        TzOrderPurchase purchaseOrder = orderContext.getPurchaseOrder();
        List<TzOrderItem> orderItems = orderContext.getOrderItems();

        // 计算订单概览信息
        BigDecimal totalQuantity = orderItems.stream().map(TzOrderItem::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        int productTypeCount = orderItems.size();

        // 构建提交概览
        OrderSubmitVO.OrderSubmitSummary submitSummary = OrderSubmitVO.OrderSubmitSummary.builder()
            .totalQuantity(totalQuantity)
            .productTypeCount(productTypeCount)
            .submitTime(purchaseOrder.getOrderDate())
            .build();

        // 获取完整的价格信息
        BigDecimal merchandiseAmount = purchaseOrder.getCustomerGoodsAmount() != null ? purchaseOrder.getCustomerGoodsAmount() : BigDecimal.ZERO;
        BigDecimal shippingAmount = purchaseOrder.getCustomerTotalFreight() != null ? purchaseOrder.getCustomerTotalFreight() : BigDecimal.ZERO;
        BigDecimal serviceFee = purchaseOrder.getServiceFee() != null ? purchaseOrder.getServiceFee() : BigDecimal.ZERO;
        BigDecimal discountAmount = purchaseOrder.getPayableDiscountAmount() != null ? purchaseOrder.getPayableDiscountAmount() : BigDecimal.ZERO;
        BigDecimal totalAmount = purchaseOrder.getCustomerTotalAmount() != null ? purchaseOrder.getCustomerTotalAmount() : BigDecimal.ZERO;

        // 进行货币转换，并记录详细日志
        log.debug("开始进行货币转换，商品金额: {}, 运费: {}, 服务费: {}, 折扣: {}, 总金额: {}",
            merchandiseAmount, shippingAmount, serviceFee, discountAmount, totalAmount);

        BigDecimal merchandiseAmountUsd = convertCurrencyWithLogging(merchandiseAmount, "商品金额");
        BigDecimal shippingAmountUsd = convertCurrencyWithLogging(shippingAmount, "运费");
        BigDecimal serviceFeeUsd = convertCurrencyWithLogging(serviceFee, "服务费");
        BigDecimal totalAmountUsd = convertCurrencyWithLogging(totalAmount, "总金额");

        // 构建价格详情
        OrderSubmitVO.OrderPriceDetails priceDetails = OrderSubmitVO.OrderPriceDetails.builder()
            .merchandiseAmount(merchandiseAmount)
            .merchandiseAmountUsd(merchandiseAmountUsd)
            .shippingAmount(shippingAmount)
            .shippingAmountUsd(shippingAmountUsd)
            .serviceFee(serviceFee)
            .serviceFeeUsd(serviceFeeUsd)
            .totalAmount(totalAmount)
            .totalAmountUsd(totalAmountUsd)
            .build();

        // 构建并返回OrderSubmitVO
        return OrderSubmitVO.builder()
            .purchaseOrderId(purchaseOrder.getId())
            .purchaseOrderNo(purchaseOrder.getPurchaseOrderNo())
            .submitSummary(submitSummary)
            .priceDetails(priceDetails)
            .build();
    }

    /**
     * 根据商品信息列表构建1688下单接口所需的商品(Cargo)列表。
     *
     * @param items 订单商品信息列表
     * @return 1688接口所需的TradeFastCargo列表
     */
    public List<TradeFastCargo> buildCargoList(List<OrderItemInfo> items) {
        return items.stream().map(item -> {
            // 对于单规格商品，1688接口不要求传入specId
            String specId = item.getSpu().getIsSingleItem() == TzProductSpuSingleItemEnum.YES
                ? null
                : item.getSku().getPlatformSpecId();
            return TradeFastCargo.builder()
                .offerId(Long.valueOf(item.getSku().getPlatformProductId()))
                .specId(specId)
                .quantity(Double.valueOf(item.getQuantity()))
                .build();
        })
            .collect(Collectors.toList());
    }

    /**
     * 进行货币转换，并记录详细日志
     * <pre>
     * CNY -> USD
     * </pre>
     *
     * @param amount      金额
     * @param description 转换描述
     * @return 转换后的金额，转换失败时返回零值
     */
    private BigDecimal convertCurrencyWithLogging(BigDecimal amount, String description) {
        try {
            BigDecimal convertedAmount = CurrencyConversionUtils.convertCurrency(amount, "CNY", "USD");
            if (convertedAmount != null) {
                log.debug("{} 转换为 {} 成功，转换前金额: {}, 转换后金额: {}", description, "USD", amount, convertedAmount);
                return convertedAmount;
            } else {
                log.warn("{} 转换为 {} 失败，返回结果为null，使用零值", description, "USD");
                return BigDecimal.ZERO;
            }
        } catch (Exception e) {
            log.warn("{} 转换为 {} 失败，使用零值代替，原金额: {}", description, "USD", amount, e);
            return BigDecimal.ZERO;
        }
    }

}
