/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.handler;

import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.starter.core.exception.BusinessException;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeProductItem;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

/**
 * webhook 订单上下文
 * <pre>
 * 将消息组装到一个上下文对象中，方便后续的业务处理
 * 同时，提供一些常用的方法，方便后续的业务处理
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/25 14:57
 * @since 1.0.0
 */
@Slf4j
@Builder
public record OrderContextRecord(
  /*
   * 1688 订单详情
   */
  OrderDetailResponse.OrderDetail orderDetail,
  /*
   * wms 采购订单详情
   */
  List<WmsPurchaseOrderDetailsRes> wmsPurchaseOrderDetailsRes,
    /*
      采购订单
     */
  TzOrderPurchase tzOrderPurchase,
    /*
      供应商订单 / 店铺
     */
  List<TzOrderSupplier> tzOrderSuppliers,
    /*
      订单项
     */
  List<TzOrderItem> tzOrderItems,
    /*
      订单消息类型
     */
  OrderMessageTypeEnums orderMessageTypeEnums
) {

    /**
     * 获取采购单号
     */
    public String getPurchaseOrderNo() {
        return tzOrderPurchase.getPurchaseOrderNo();
    }

    /**
     * 获取采购单ID
     */
    public Long getPurchaseOrderId() {
        return tzOrderPurchase.getId();
    }

    /**
     * 获取1688 订单号 字符串
     */
    public String getAlibabaOrderIdStr() {
        return orderDetail.getBaseInfo().getIdOfStr();
    }

    /**
     * 获取1688 订单号
     *
     * @return Long
     */
    public Long getAlibabaOrderId() {
        return orderDetail.getBaseInfo().getId();
    }

    /**
     * 获取1688 订单状态
     */
    public String getAlibabaOrderStatus() {
        return orderDetail.getBaseInfo().getStatus();
    }

    /**
     * 获取1688 订单创建时间
     */
    public LocalDateTime getAlibabaOrderCreateTime() {
        return orderDetail.getBaseInfo().getCreateTime();
    }

    /**
     * 获取1688 订单商品列表
     */
    public List<TradeProductItem> getAlibabaOrderProducts() {
        return orderDetail.getProductItems();
    }

    /**
     * 获取 wms 采购订单详情
     * <pre>
     * note: 如果存在多个 wms 采购订单详情，则取第一个
     * 如果需要获取全部，则直接使用 wmsPurchaseOrderDetailsRes
     * </pre>
     */
    @NonNull
    public WmsPurchaseOrderDetailsRes getWmsPurchaseOrderDetail() {
        return wmsPurchaseOrderDetailsRes.getFirst();
    }

    /**
     * 根据供应商订单ID获取订单项
     */
    public List<TzOrderItem> getOrderItemsBySupplierOrderId(Long supplierOrderId) {
        return this.tzOrderItems.stream().filter(item -> Objects.equals(item.getSupplierOrderId(), supplierOrderId)).collect(Collectors.toList());
    }

    /**
     * 根据供应商订单ID 获取订单项,并以 ProductId + SpecId 组合字段进行作为 key ，转成 map value 为订单项
     * <pre>
     *     因为下单的时候，需要使用 ProductId + SpecId 组合来确定唯一商品
     * </pre>
     * @param supplierOrderId 供应商订单ID
     * @return Map
     */
    public Map<String, TzOrderItem> getOrderItemsMapBySupplierOrderId(Long supplierOrderId) {
        return this.tzOrderItems.stream()
          .filter(item -> Objects.equals(item.getSupplierOrderId(), supplierOrderId))
          .collect(Collectors.toMap(item -> item.getPlatformProductId() + "_" + item.getPlatformSpecId(), item -> item));
    }


    /**
     * 根据 orderId 获取对应的 供应商订单
     * <pre>
     *     note: 一个 1688 订单 - wms 订单 - 供应商订单 一一对应。
     * </pre>
     */
    public TzOrderSupplier getTzOrderSupplierByOrderId(String orderId) {
        return tzOrderSuppliers.stream()
          .filter(tzOrderSupplier -> {
              // 根据 1688 订单号 或者 wms 订单号 匹配
              return Objects.equals(tzOrderSupplier.getPlatformOrderId(), orderId)
                || Objects.equals(tzOrderSupplier.getWmsPurchaseOrderNo(), getWmsPurchaseOrderDetail().getPurchaseNo());
          })
          .findFirst().orElseThrow(() -> {
              log.error("❌ 未找到对应的供应商订单: orderId={}", orderId);
              return new BusinessException(String.format("未找到对应的供应商订单,%s", orderId));
          });
    }

}
