/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.service.OrderCompatibilityService;
import com.fulfillmen.shop.manager.support.alibaba.webhook.service.OrderDataSyncService;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse;
import com.fulfillmen.support.alibaba.enums.OrderStatusEnums;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单兼容性处理服务实现
 *
 * <pre>
 * 实现要点：
 * 1. 全面的兼容性检查机制
 * 2. 智能的数据转换和映射
 * 3. 灵活的状态映射规则
 * 4. 完整的数据验证流程
 * 5. 详细的问题诊断和报告
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/25 17:20
 * @description 订单兼容性处理服务实现
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderCompatibilityServiceImpl implements OrderCompatibilityService {

    private final OrderDataSyncService orderDataSyncService;

    @Override
    public CompatibilityCheckResult checkCompatibility(String orderId,
            OrderDetailResponse.OrderDetail orderDetail,
            List<WmsPurchaseOrderDetailsRes> wmsOrderDetails) {
        log.debug("开始兼容性检查: orderId={}", orderId);

        List<String> issues = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        // 1. 检查数据版本
        String dataVersion = determineDataVersion(orderDetail, wmsOrderDetails);

        // 2. 检查格式问题
        boolean hasFormatIssues = checkFormatIssues(orderDetail, wmsOrderDetails, issues, warnings);

        // 3. 检查缺失字段
        boolean hasMissingFields = checkMissingFields(orderDetail, wmsOrderDetails, issues, warnings);

        // 4. 检查业务规则冲突
        boolean hasBusinessRuleConflicts = checkBusinessRuleConflicts(orderDetail, wmsOrderDetails, issues, warnings);

        // 5. 检查状态映射问题
        boolean hasStatusMappingIssues = checkStatusMappingIssues(orderDetail, wmsOrderDetails, issues, warnings);

        // 6. 确定兼容性级别
        CompatibilityLevel compatibilityLevel = determineCompatibilityLevel(
                hasFormatIssues, hasMissingFields, hasBusinessRuleConflicts, hasStatusMappingIssues);

        boolean isCompatible = compatibilityLevel == CompatibilityLevel.FULLY_COMPATIBLE ||
                compatibilityLevel == CompatibilityLevel.MOSTLY_COMPATIBLE;

        CompatibilityCheckResult result = new CompatibilityCheckResult(
                isCompatible, dataVersion, hasFormatIssues, hasMissingFields,
                hasBusinessRuleConflicts, hasStatusMappingIssues, issues, warnings, compatibilityLevel);

        log.info("兼容性检查完成: orderId={}, compatible={}, level={}, issues={}, warnings={}",
                orderId, isCompatible, compatibilityLevel, issues.size(), warnings.size());

        return result;
    }

    @Override
    public OrderContextRecord handleCompatibilityIssues(String orderId,
            CompatibilityCheckResult compatibilityResult,
            OrderDetailResponse.OrderDetail orderDetail,
            List<WmsPurchaseOrderDetailsRes> wmsOrderDetails) {
        log.info("开始处理兼容性问题: orderId={}, level={}", orderId, compatibilityResult.compatibilityLevel());

        try {
            // 1. 如果完全兼容，直接处理
            if (compatibilityResult.compatibilityLevel() == CompatibilityLevel.FULLY_COMPATIBLE) {
                return handleFullyCompatibleData(orderId, orderDetail, wmsOrderDetails);
            }

            // 2. 转换旧版数据格式
            LegacyDataConversionResult conversionResult = convertLegacyData(orderDetail, wmsOrderDetails);
            if (!conversionResult.conversionSuccessful()) {
                throw new RuntimeException("旧版数据转换失败: " + conversionResult.conversionWarnings());
            }

            // 3. 使用转换后的数据进行处理
            OrderDetailResponse.OrderDetail convertedOrderDetail = conversionResult.convertedOrderDetail();
            List<WmsPurchaseOrderDetailsRes> convertedWmsOrderDetails = conversionResult.convertedWmsOrderDetails();

            // 4. 检查数据完整性并补齐
            OrderDataSyncService.OrderDataIntegrityResult integrityResult = orderDataSyncService
                    .checkOrderDataIntegrity(orderId);

            // 5. 同步和补齐数据
            OrderContextRecord orderContextRecord = orderDataSyncService.syncAndCompleteOrderData(
                    orderId, integrityResult, convertedOrderDetail, convertedWmsOrderDetails);

            // 6. 验证最终数据完整性
            DataIntegrityValidationResult validationResult = validateDataIntegrity(orderContextRecord);
            if (!validationResult.passedValidation()) {
                log.warn("数据完整性验证未通过: orderId={}, errors={}",
                        orderId, validationResult.getValidationErrorsDescription());
            }

            log.info("兼容性问题处理完成: orderId={}, conversionNotes={}",
                    orderId, conversionResult.getConversionNotesDescription());

            return orderContextRecord;

        } catch (Exception e) {
            log.error("处理兼容性问题失败: orderId={}", orderId, e);
            throw new RuntimeException("兼容性问题处理失败", e);
        }
    }

    @Override
    public LegacyDataConversionResult convertLegacyData(OrderDetailResponse.OrderDetail orderDetail,
            List<WmsPurchaseOrderDetailsRes> wmsOrderDetails) {
        log.debug("开始转换旧版数据");

        List<String> conversionNotes = new ArrayList<>();
        List<String> conversionWarnings = new ArrayList<>();

        try {
            // 1. 转换订单详情数据
            OrderDetailResponse.OrderDetail convertedOrderDetail = convertOrderDetail(orderDetail, conversionNotes,
                    conversionWarnings);

            // 2. 转换WMS订单数据
            List<WmsPurchaseOrderDetailsRes> convertedWmsOrderDetails = convertWmsOrderDetails(wmsOrderDetails,
                    conversionNotes, conversionWarnings);

            // 3. 验证转换结果
            boolean conversionSuccessful = validateConversionResult(convertedOrderDetail, convertedWmsOrderDetails,
                    conversionWarnings);

            LegacyDataConversionResult result = new LegacyDataConversionResult(
                    conversionSuccessful, convertedOrderDetail, convertedWmsOrderDetails, conversionNotes,
                    conversionWarnings);

            log.info("旧版数据转换完成: successful={}, notes={}, warnings={}",
                    conversionSuccessful, conversionNotes.size(), conversionWarnings.size());

            return result;

        } catch (Exception e) {
            log.error("旧版数据转换失败", e);
            conversionWarnings.add("数据转换异常: " + e.getMessage());
            return new LegacyDataConversionResult(false, orderDetail, wmsOrderDetails, conversionNotes,
                    conversionWarnings);
        }
    }

    @Override
    public OrderStatusMappingResult mapOrderStatus(String alibabaStatus, String wmsStatus) {
        log.debug("开始映射订单状态: alibabaStatus={}, wmsStatus={}", alibabaStatus, wmsStatus);

        List<String> mappingNotes = new ArrayList<>();
        boolean hasConflicts = false;
        String mappingReason = "标准状态映射";

        try {
            // 1. 解析1688状态
            OrderStatusEnums alibabaStatusEnum = OrderStatusEnums.fromCode(alibabaStatus);

            // 2. 映射到系统状态
            TzOrderPurchaseStatusEnum purchaseOrderStatus = mapToPurchaseOrderStatus(alibabaStatusEnum, mappingNotes);
            TzOrderSupplierStatusEnum supplierOrderStatus = null;
            // TzOrderSupplierStatusEnum supplierOrderStatus =
            // mapToSupplierOrderStatus(alibabaStatusEnum, mappingNotes);
            TzOrderItemStatusEnum orderItemStatus = null;
            // TzOrderItemStatusEnum orderItemStatus =
            // mapToOrderItemStatus(alibabaStatusEnum, mappingNotes);

            // 3. 处理WMS状态冲突（如果有）
            if (StringUtils.hasText(wmsStatus)) {
                hasConflicts = resolveStatusConflicts(wmsStatus, purchaseOrderStatus, supplierOrderStatus,
                        orderItemStatus, mappingNotes);
                if (hasConflicts) {
                    mappingReason = "存在状态冲突，使用1688状态为准";
                }
            }

            OrderStatusMappingResult result = new OrderStatusMappingResult(
                    purchaseOrderStatus, supplierOrderStatus, orderItemStatus, hasConflicts, mappingReason,
                    mappingNotes);

            log.info("订单状态映射完成: {}", result.getMappingDescription());
            return result;

        } catch (Exception e) {
            log.error("订单状态映射失败: alibabaStatus={}, wmsStatus={}", alibabaStatus, wmsStatus, e);
            mappingNotes.add("状态映射异常: " + e.getMessage());

            // 返回默认状态
            return new OrderStatusMappingResult(
                    TzOrderPurchaseStatusEnum.PAYMENT_PENDING,
                    TzOrderSupplierStatusEnum.PENDING_PAYMENT,
                    TzOrderItemStatusEnum.PENDING,
                    true,
                    "映射失败，使用默认状态",
                    mappingNotes);
        }
    }

    @Override
    public DataIntegrityValidationResult validateDataIntegrity(OrderContextRecord orderContextRecord) {
        log.debug("开始验证数据完整性");

        List<String> validationErrors = new ArrayList<>();
        List<String> validationWarnings = new ArrayList<>();

        // 1. 检查必需字段
        boolean hasRequiredFields = validateRequiredFields(orderContextRecord, validationErrors);

        // 2. 检查关联关系
        boolean hasValidRelationships = validateRelationships(orderContextRecord, validationErrors);

        // 3. 检查数据一致性
        boolean hasConsistentData = validateDataConsistency(orderContextRecord, validationWarnings);

        boolean isValid = validationErrors.isEmpty();

        DataIntegrityValidationResult result = new DataIntegrityValidationResult(
                isValid, hasRequiredFields, hasValidRelationships, hasConsistentData, validationErrors,
                validationWarnings);

        log.info("数据完整性验证完成: valid={}, errors={}, warnings={}",
                isValid, validationErrors.size(), validationWarnings.size());

        return result;
    }

    /**
     * 确定数据版本
     */
    private String determineDataVersion(OrderDetailResponse.OrderDetail orderDetail,
            List<WmsPurchaseOrderDetailsRes> wmsOrderDetails) {
        // 根据数据特征判断版本
        if (CollectionUtils.isEmpty(wmsOrderDetails)) {
            return "legacy-v1.0"; // 旧版本，没有WMS数据
        }
        return "current-v2.0"; // 当前版本
    }

    /**
     * 检查格式问题
     */
    private boolean checkFormatIssues(OrderDetailResponse.OrderDetail orderDetail,
            List<WmsPurchaseOrderDetailsRes> wmsOrderDetails,
            List<String> issues, List<String> warnings) {
        boolean hasIssues = false;

        // 检查订单详情格式
        if (orderDetail == null || orderDetail.getBaseInfo() == null) {
            issues.add("订单详情格式异常");
            hasIssues = true;
        }

        // 检查商品列表格式
        if (orderDetail != null && CollectionUtils.isEmpty(orderDetail.getProductItems())) {
            warnings.add("商品列表为空");
        }

        return hasIssues;
    }

    /**
     * 检查缺失字段
     */
    private boolean checkMissingFields(OrderDetailResponse.OrderDetail orderDetail,
            List<WmsPurchaseOrderDetailsRes> wmsOrderDetails,
            List<String> issues, List<String> warnings) {
        boolean hasMissingFields = false;

        if (orderDetail != null && orderDetail.getBaseInfo() != null) {
            if (!StringUtils.hasText(orderDetail.getBaseInfo().getIdOfStr())) {
                issues.add("缺少订单ID");
                hasMissingFields = true;
            }
        }

        return hasMissingFields;
    }

    /**
     * 检查业务规则冲突
     */
    private boolean checkBusinessRuleConflicts(OrderDetailResponse.OrderDetail orderDetail,
            List<WmsPurchaseOrderDetailsRes> wmsOrderDetails,
            List<String> issues, List<String> warnings) {
        // TODO: 实现业务规则冲突检查
        return false;
    }

    /**
     * 检查状态映射问题
     */
    private boolean checkStatusMappingIssues(OrderDetailResponse.OrderDetail orderDetail,
            List<WmsPurchaseOrderDetailsRes> wmsOrderDetails,
            List<String> issues, List<String> warnings) {
        boolean hasIssues = false;

        if (orderDetail != null && orderDetail.getBaseInfo() != null) {
            String status = orderDetail.getBaseInfo().getStatus();
            if (!StringUtils.hasText(status)) {
                issues.add("订单状态为空");
                hasIssues = true;
            } else {
                OrderStatusEnums statusEnum = OrderStatusEnums.fromCode(status);
                if (statusEnum == null) {
                    warnings.add("未知的订单状态: " + status);
                }
            }
        }

        return hasIssues;
    }

    /**
     * 确定兼容性级别
     */
    private CompatibilityLevel determineCompatibilityLevel(boolean hasFormatIssues,
            boolean hasMissingFields,
            boolean hasBusinessRuleConflicts,
            boolean hasStatusMappingIssues) {
        if (!hasFormatIssues && !hasMissingFields && !hasBusinessRuleConflicts && !hasStatusMappingIssues) {
            return CompatibilityLevel.FULLY_COMPATIBLE;
        } else if (!hasFormatIssues && !hasMissingFields) {
            return CompatibilityLevel.MOSTLY_COMPATIBLE;
        } else if (!hasFormatIssues) {
            return CompatibilityLevel.PARTIALLY_COMPATIBLE;
        } else {
            return CompatibilityLevel.INCOMPATIBLE;
        }
    }

    /**
     * 处理完全兼容的数据
     */
    private OrderContextRecord handleFullyCompatibleData(String orderId,
            OrderDetailResponse.OrderDetail orderDetail,
            List<WmsPurchaseOrderDetailsRes> wmsOrderDetails) {
        // 对于完全兼容的数据，直接使用数据同步服务处理
        OrderDataSyncService.OrderDataIntegrityResult integrityResult = orderDataSyncService
                .checkOrderDataIntegrity(orderId);

        return orderDataSyncService.syncAndCompleteOrderData(orderId, integrityResult, orderDetail, wmsOrderDetails);
    }

    /**
     * 转换订单详情数据
     */
    private OrderDetailResponse.OrderDetail convertOrderDetail(OrderDetailResponse.OrderDetail orderDetail,
            List<String> conversionNotes,
            List<String> conversionWarnings) {
        // TODO: 实现订单详情数据转换逻辑
        conversionNotes.add("订单详情数据转换完成");
        return orderDetail;
    }

    /**
     * 转换WMS订单数据
     */
    private List<WmsPurchaseOrderDetailsRes> convertWmsOrderDetails(List<WmsPurchaseOrderDetailsRes> wmsOrderDetails,
            List<String> conversionNotes,
            List<String> conversionWarnings) {
        // TODO: 实现WMS订单数据转换逻辑
        if (CollectionUtils.isEmpty(wmsOrderDetails)) {
            conversionWarnings.add("WMS订单数据为空，可能是旧版数据");
        } else {
            conversionNotes.add("WMS订单数据转换完成");
        }
        return wmsOrderDetails;
    }

    /**
     * 验证转换结果
     */
    private boolean validateConversionResult(OrderDetailResponse.OrderDetail convertedOrderDetail,
            List<WmsPurchaseOrderDetailsRes> convertedWmsOrderDetails,
            List<String> conversionWarnings) {
        if (convertedOrderDetail == null) {
            conversionWarnings.add("转换后的订单详情为空");
            return false;
        }
        return true;
    }

    /**
     * 映射到采购订单状态
     */
    private TzOrderPurchaseStatusEnum mapToPurchaseOrderStatus(OrderStatusEnums alibabaStatus,
            List<String> mappingNotes) {
        if (alibabaStatus == null) {
            mappingNotes.add("1688状态为空，使用默认状态");
            return TzOrderPurchaseStatusEnum.PAYMENT_PENDING;
        }

        return switch (alibabaStatus) {
            case WAIT_BUYER_PAY -> {
                mappingNotes.add("等待买家付款 -> 待支付");
                yield TzOrderPurchaseStatusEnum.PAYMENT_PENDING;
            }
            case WAIT_SELLER_SEND -> {
                mappingNotes.add("等待卖家发货 -> 支付完成");
                yield TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED;
            }
            case WAIT_BUYER_RECEIVE -> {
                mappingNotes.add("等待买家确认收货 -> 采购中");
                yield TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS;
            }
            case SUCCESS -> {
                mappingNotes.add("交易完成 -> 已完成");
                yield TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED;
            }
            case CONFIRM_GOODS -> {
                mappingNotes.add("确认收货 -> 已完成");
                yield TzOrderPurchaseStatusEnum.WAREHOUSE_PENDING_RECEIVED;
            }
            case TERMINATED -> {
                mappingNotes.add("交易终止 -> 已取消");
                yield TzOrderPurchaseStatusEnum.ORDER_CANCELLED;
            }
            case CANCEL -> {
                mappingNotes.add("交易取消 -> 已取消");
                yield TzOrderPurchaseStatusEnum.ORDER_CANCELLED;
            }
            default -> {
                mappingNotes.add("未知状态 " + alibabaStatus + " -> 待支付");
                yield TzOrderPurchaseStatusEnum.PAYMENT_PENDING;
            }
        };
    }

    /**
     * 映射到供应商订单状态
     */
    // private TzOrderSupplierStatusEnum mapToSupplierOrderStatus(OrderStatusEnums
    // alibabaStatus, List<String> mappingNotes) {
    // if (alibabaStatus == null) {
    // return TzOrderSupplierStatusEnum.PENDING_PAYMENT;
    // }
    //
    // return switch (alibabaStatus) {
    // case WAIT_BUYER_PAY -> TzOrderSupplierStatusEnum.PENDING_PAYMENT;
    // case WAIT_SELLER_SEND_GOODS -> TzOrderSupplierStatusEnum.PAID;
    // case WAIT_BUYER_CONFIRM_GOODS -> TzOrderSupplierStatusEnum.SHIPPED;
    // case TRADE_FINISHED -> TzOrderSupplierStatusEnum.COMPLETED;
    // case TRADE_CLOSED -> TzOrderSupplierStatusEnum.CANCELLED;
    // default -> TzOrderSupplierStatusEnum.PENDING_PAYMENT;
    // };
    // }

    /**
     * 映射到订单项状态
     */
    // private TzOrderItemStatusEnum mapToOrderItemStatus(OrderStatusEnums
    // alibabaStatus, List<String> mappingNotes) {
    // if (alibabaStatus == null) {
    // return TzOrderItemStatusEnum.PENDING;
    // }
    //
    // return switch (alibabaStatus) {
    // case WAIT_BUYER_PAY -> TzOrderItemStatusEnum.PENDING;
    // case WAIT_SELLER_SEND_GOODS -> TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS;
    // case WAIT_BUYER_CONFIRM_GOODS -> TzOrderItemStatusEnum.SHIPPED;
    // case TRADE_FINISHED -> TzOrderItemStatusEnum.COMPLETED;
    // case TRADE_CLOSED -> TzOrderItemStatusEnum.CANCELLED;
    // default -> TzOrderItemStatusEnum.PENDING;
    // };
    // }

    /**
     * 解决状态冲突
     */
    private boolean resolveStatusConflicts(String wmsStatus,
            TzOrderPurchaseStatusEnum purchaseOrderStatus,
            TzOrderSupplierStatusEnum supplierOrderStatus,
            TzOrderItemStatusEnum orderItemStatus,
            List<String> mappingNotes) {
        // TODO: 实现状态冲突解决逻辑
        if (StringUtils.hasText(wmsStatus)) {
            mappingNotes.add("检测到WMS状态冲突，优先使用1688状态");
            return true;
        }
        return false;
    }

    /**
     * 验证必需字段
     */
    private boolean validateRequiredFields(OrderContextRecord orderContextRecord, List<String> validationErrors) {
        boolean hasRequiredFields = true;

        if (orderContextRecord.tzOrderPurchase() == null) {
            validationErrors.add("缺少采购订单数据");
            hasRequiredFields = false;
        }

        if (CollectionUtils.isEmpty(orderContextRecord.tzOrderSuppliers())) {
            validationErrors.add("缺少供应商订单数据");
            hasRequiredFields = false;
        }

        if (CollectionUtils.isEmpty(orderContextRecord.tzOrderItems())) {
            validationErrors.add("缺少订单项数据");
            hasRequiredFields = false;
        }

        return hasRequiredFields;
    }

    /**
     * 验证关联关系
     */
    private boolean validateRelationships(OrderContextRecord orderContextRecord, List<String> validationErrors) {
        // TODO: 实现关联关系验证逻辑
        return true;
    }

    /**
     * 验证数据一致性
     */
    private boolean validateDataConsistency(OrderContextRecord orderContextRecord, List<String> validationWarnings) {
        // TODO: 实现数据一致性验证逻辑
        return true;
    }
}
