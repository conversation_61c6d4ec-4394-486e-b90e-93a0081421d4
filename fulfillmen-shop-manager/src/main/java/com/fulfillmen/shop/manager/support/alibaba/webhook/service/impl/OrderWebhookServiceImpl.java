/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl;

import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemLogisticsStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.manager.support.alibaba.IOrderManager;
import com.fulfillmen.shop.manager.support.alibaba.IPayManager;
import com.fulfillmen.shop.manager.support.alibaba.webhook.event.OrderWebhookEvent;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.service.OrderDataSyncService;
import com.fulfillmen.shop.manager.support.alibaba.webhook.service.OrderWebhookService;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.support.alibaba.api.request.order.OrderDetailRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.AlipayUrlRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.PayWayQueryRequestRecord;
import com.fulfillmen.support.alibaba.api.response.model.PayTypeInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeBaseInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeBaseInfo.NewStepOrder;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeNativeLogisticsInfo.LogisticsItem;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeProductItem;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse.OrderDetail;
import com.fulfillmen.support.alibaba.api.response.pay.AlipayUrlResponse;
import com.fulfillmen.support.alibaba.api.response.pay.PayWayQueryResponse;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.webhook.MessageEvent;
import com.fulfillmen.support.alibaba.webhook.data.OrderMessage;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailsReq;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

/**
 * 订单Webhook业务处理服务实现
 *
 * <pre>
 * 实现要点：
 * 1. 统一的异常处理和日志记录
 * 2. 数据完整性检查和补齐
 * 3. 新旧版本数据兼容处理
 * 4. 事件驱动的异步处理
 * 5. 事务管理和数据一致性保证
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/25 16:30
 * @description 订单webhook业务处理服务实现
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderWebhookServiceImpl implements OrderWebhookService {

    private final OrderDataSyncService orderDataSyncService;
    private final IOrderManager orderManager;
    private final IPayManager payManager;
    private final IWmsManager wmsManager;
    private final TzOrderSupplierMapper tzOrderSupplierMapper;
    private final TzOrderPurchaseMapper tzOrderPurchaseMapper;
    private final TzOrderItemMapper tzOrderItemMapper;
    private final ApplicationEventPublisher eventPublisher;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final TransactionTemplate transactionTemplate;

    @Override
    public void processOrderWebhook(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
      OrderMessageTypeEnums messageType) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        String msgId = messageEvent.getMsgId();

        // 设置MDC上下文
        MDC.put("orderId", orderId);
        MDC.put("msgId", msgId);
        MDC.put("messageType", messageType.getMessageType());

        try {
            log.info("开始处理订单webhook消息: orderId={}, msgId={}, messageType={}, currentStatus={}",
              orderId, msgId, messageType.getMessageType(), orderMessage.getCurrentStatus());

            // 1. 异步获取订单数据
            CompletableFuture<OrderDetailResponse.OrderDetail> orderDetailFuture = getOrderDetailAsync(
              orderMessage.getOrderId());
            CompletableFuture<List<WmsPurchaseOrderDetailsRes>> wmsOrderDetailsFuture = getWmsOrderDetailsAsync(
              orderId);

            // 等待全部任务完成
            CompletableFuture.allOf(orderDetailFuture, wmsOrderDetailsFuture).join();

            // 2. 等待数据获取完成
            OrderDetailResponse.OrderDetail orderDetail = orderDetailFuture.join();
            List<WmsPurchaseOrderDetailsRes> wmsOrderDetails = wmsOrderDetailsFuture.join();
            log.debug("订单数据获取完成: orderId={}, orderDetail={}, wmsOrderDetails={}", orderId, orderDetail, wmsOrderDetails);

            // 3. 验证数据有效性
            validateOrderData(orderDetail, wmsOrderDetails, orderId);

            // 4. 检查数据完整性
            OrderDataSyncService.OrderDataIntegrityResult integrityResult = orderDataSyncService
              .checkOrderDataIntegrity(orderId);

            log.info("订单数据完整性检查结果: orderId={}, isComplete={}, isNewVersion={}, missing={}",
              orderId, integrityResult.isDataComplete(), integrityResult.isNewVersionData(),
              integrityResult.getMissingDataDescription());

            // 5. 同步和补齐数据
            OrderContextRecord orderContextRecord = orderDataSyncService.syncAndCompleteOrderData(
              orderId, integrityResult, orderDetail, wmsOrderDetails);

            // 6. 发布数据同步完成事件
            publishDataSyncCompletedEvent(orderMessage, messageEvent, messageType, orderContextRecord);

            // 7. 路由到具体的业务处理逻辑
            routeToBusinessLogic(orderMessage, messageEvent, messageType, orderContextRecord);

            log.info("订单webhook消息处理完成: orderId={}, msgId={}, messageType={}",
              orderId, msgId, messageType.getMessageType());

        } catch (Exception e) {
            log.error("订单webhook消息处理失败: orderId={}, msgId={}, messageType={}, error={}",
              orderId, msgId, messageType.getMessageType(), e.getMessage(), e);

            // 发布处理失败事件
            publishProcessingFailedEvent(orderMessage, messageEvent, messageType, e);
            throw e;
        } finally {
            // 清理MDC上下文
            MDC.remove("orderId");
            MDC.remove("msgId");
            MDC.remove("messageType");
        }
    }

    /**
     * 异步获取1688订单详情
     */
    private CompletableFuture<OrderDetailResponse.OrderDetail> getOrderDetailAsync(Long orderId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                OrderDetailRequestRecord request = OrderDetailRequestRecord.builder()
                  .webSite("1688")
                  .orderId(orderId)
                  .build();
                return orderManager.getOrderDetail(request);
            } catch (Exception e) {
                log.error("获取1688订单详情失败: orderId={}", orderId, e);
                throw new RuntimeException("获取1688订单详情失败", e);
            }
        }, threadPoolTaskExecutor);
    }

    /**
     * 异步获取WMS订单详情
     */
    private CompletableFuture<List<WmsPurchaseOrderDetailsRes>> getWmsOrderDetailsAsync(String orderId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                PurchaseOrderDetailReq request = PurchaseOrderDetailReq.builder()
                  .orderId(orderId)
                  .build();
                return wmsManager.queryOrderDetail(request);
            } catch (Exception e) {
                log.error("获取WMS订单详情失败: orderId={}", orderId, e);
                throw new RuntimeException("获取WMS订单详情失败", e);
            }
        }, threadPoolTaskExecutor);
    }

    /**
     * 验证订单数据有效性
     */
    private void validateOrderData(OrderDetailResponse.OrderDetail orderDetail,
      List<WmsPurchaseOrderDetailsRes> wmsOrderDetails,
      String orderId) {
        if (orderDetail == null) {
            throw new IllegalArgumentException("1688订单详情为空: orderId=" + orderId);
        }

        if (CollectionUtils.isEmpty(wmsOrderDetails)) {
            log.warn("WMS订单详情为空，可能是旧版数据: orderId={}", orderId);
            // 注意：这里不抛异常，因为旧版数据可能没有WMS记录，不存在没有采购记录。如果没有那么这个订单有问题。
            throw new IllegalArgumentException("WMS订单详情为空: orderId=" + orderId);
        }
    }

    /**
     * 路由到具体的业务处理逻辑
     */
    private void routeToBusinessLogic(OrderMessage orderMessage,
      MessageEvent<OrderMessage> messageEvent,
      OrderMessageTypeEnums messageType,
      OrderContextRecord orderContextRecord) {
        switch (messageType) {
            case ORDER_BUYER_VIEW_BUYER_MAKE -> handleOrderCreation(orderMessage, messageEvent, orderContextRecord);
            case ORDER_BUYER_VIEW_ORDER_PAY -> handleOrderPayment(orderMessage, messageEvent, orderContextRecord);
            case ORDER_BATCH_PAY -> handleBatchOrderPayment(orderMessage, messageEvent, orderContextRecord);
            case ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS -> handleOrderShipment(orderMessage, messageEvent, orderContextRecord);
            case ORDER_BUYER_VIEW_PART_PART_SENDGOODS -> handlePartialShipment(orderMessage, messageEvent, orderContextRecord);
            case ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS -> handleOrderConfirmation(orderMessage, messageEvent, orderContextRecord);
            case ORDER_BUYER_VIEW_ORDER_SUCCESS -> handleOrderCompletion(orderMessage, messageEvent, orderContextRecord);
            case ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY -> handleOrderPriceModification(orderMessage, messageEvent, orderContextRecord);
            default -> log.warn("未支持的订单消息类型: messageType={}, orderId={}", messageType, orderMessage.getOrderId());
        }
    }

    /**
     * 发布数据同步完成事件
     */
    private void publishDataSyncCompletedEvent(OrderMessage orderMessage,
      MessageEvent<OrderMessage> messageEvent,
      OrderMessageTypeEnums messageType,
      OrderContextRecord orderContextRecord) {
        OrderWebhookEvent event = OrderWebhookEvent.createDataSyncCompletedEvent(this, orderMessage, messageEvent, messageType, orderContextRecord);
        eventPublisher.publishEvent(event);
    }

    /**
     * 发布处理失败事件
     */
    private void publishProcessingFailedEvent(OrderMessage orderMessage,
      MessageEvent<OrderMessage> messageEvent,
      OrderMessageTypeEnums messageType,
      Exception error) {
        // TODO: 实现处理失败事件的发布
        log.error("发布订单处理失败事件: orderId={}, error={}", orderMessage.getOrderId(), error.getMessage());
    }

    @Override
    public void handleOrderCreation(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
      OrderContextRecord orderContextRecord) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        log.info("处理订单创建事件: orderId={}", orderId);

        try {

            WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetail = orderContextRecord.getWmsPurchaseOrderDetail();
            // 获取 支付连接
            WmsPurchaseOrderDetailReq wmsPurchaseOrderDetailReq = WmsPurchaseOrderDetailReq.builder()
              .purchaseNo(wmsPurchaseOrderDetail.getPurchaseNo())
              .orderId(Long.valueOf(orderId))
              .build();
            // 获取供应商订单
            TzOrderSupplier orderSupplier = orderContextRecord.getTzOrderSupplierByOrderId(orderId);
            TzOrderPurchase purchase = orderContextRecord.tzOrderPurchase();


            // 并行异步获取 支付连接和支付方式
            CompletableFuture<Void> alipayUrlFuture = CompletableFuture.runAsync(() -> {
                AlipayUrlResponse alipayUrl = this.payManager
                  .getAlipayUrl(AlipayUrlRequestRecord.builder().orderIdList(Collections.singletonList(orderMessage.getOrderId())).build());
                // 设置支付链接
                wmsPurchaseOrderDetailReq.setPayUrl(alipayUrl.getPayUrl());
                // 更新供应商信息
                orderSupplier.setPlatformPayUrl(wmsPurchaseOrderDetailReq.getPayUrl());
            }, threadPoolTaskExecutor);

            // 获取支付方式
            CompletableFuture<Void> payWayQueryFuture = CompletableFuture.runAsync(() -> {
                PayWayQueryResponse response = this.payManager.getPayWayQuery(PayWayQueryRequestRecord.of(orderMessage.getOrderId()));
                // 支付方式名称
                String payTypeDesc = response.getResultList().getChannels().stream().map(PayTypeInfo::getName).collect(Collectors.joining(","));
                String payType = response.getResultList().getChannels().stream().map(PayTypeInfo::getCode).map(String::valueOf).collect(Collectors.joining(","));
                // 将支付渠道名称用逗号连接，存储到采购订单平台中
                wmsPurchaseOrderDetailReq.setPlatformPayType(payTypeDesc);
                orderSupplier.setPlatformTradeType(payType);
                orderSupplier.setPlatformTradeTypeDesc(payTypeDesc);
            }, threadPoolTaskExecutor);
            // 等待全部完成
            CompletableFuture.allOf(alipayUrlFuture, payWayQueryFuture).join();
            //
            TradeBaseInfo baseInfo = orderContextRecord.orderDetail().getBaseInfo();
            List<NewStepOrder> newStepOrderList = baseInfo.getNewStepOrderList();
            if (!CollectionUtils.isEmpty(newStepOrderList)) {
                NewStepOrder newStepOrder = newStepOrderList.getFirst();
                // 优惠金额
                BigDecimal discountFee = newStepOrder.getDiscountFee();
                orderSupplier.setPayableDiscountAmount(discountFee);
                wmsPurchaseOrderDetailReq.setCouponDiscount(discountFee);
            }
            // 保存
            transactionTemplate.executeWithoutResult(transactionStatus -> {
                try {
                    // 保存供应商订单信息
                    this.tzOrderSupplierMapper.updateById(orderSupplier);
                    // 重新计算采购单的应付金额
                    orderContextRecord.recalculatePayableInformationToPurchaseOrder();
                    // 更新采购单
                    this.tzOrderPurchaseMapper.updateById(purchase);
                    // 同步处理 订单项
                    orderContextRecord.resyncTzOrderItem(orderId);
                    // 更新订单项
                    this.tzOrderItemMapper.updateBatchById(orderContextRecord.tzOrderItems());
                } catch (Exception ex) {
                    log.error("更新供应商和采购单失败 : [{}] ", ex.getMessage(), ex);
                    transactionStatus.setRollbackOnly();
                }
            });
            List<WmsPurchaseOrderDetailsReq> wmsPurchaseOrderDetailsRequestList = orderContextRecord.resyncWmsPurchaseOrderDetailsReqList();
            wmsPurchaseOrderDetailReq.setOrderDetails(wmsPurchaseOrderDetailsRequestList);
            // 更新WMS采购订单
            this.wmsManager.updateWmsPurchaseOrder(wmsPurchaseOrderDetailReq);

            // 发布订单创建完成事件
            publishOrderProcessingCompletedEvent(orderMessage, messageEvent, OrderMessageTypeEnums.ORDER_BUYER_VIEW_BUYER_MAKE);

            log.info("订单创建事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("订单创建事件处理失败: orderId={}", orderId, e);
            throw e;
        }
    }

    @Override
    public void handleOrderPayment(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
      OrderContextRecord orderContextRecord) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        log.info("处理订单支付事件: orderId={}", orderId);

        try {
            // 1. 更新订单状态为已支付
            updateOrderStatusToPaid(orderId);

            // 2. 触发后续业务流程（如通知WMS、发送邮件等）
            triggerPostPaymentProcesses(orderId);

            // 3. 发布支付完成事件
            publishOrderProcessingCompletedEvent(orderMessage, messageEvent,
              OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_PAY);

            log.info("订单支付事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("订单支付事件处理失败: orderId={}", orderId, e);
            throw e;
        }
    }

    @Override
    public void handleOrderShipment(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
      OrderContextRecord orderContextRecord) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        log.info("处理订单发货事件: orderId={}", orderId);

        try {
            // 1. 更新订单状态为已发货
            updateOrderStatusToShipped(orderId,orderContextRecord);

            // 2. 更新物流信息
            updateShippingInformation(orderId);

            // 3. 发送发货通知
            sendShipmentNotification(orderId);

            // 4. 发布发货完成事件
            publishOrderProcessingCompletedEvent(orderMessage, messageEvent,
              OrderMessageTypeEnums.ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS);

            log.info("订单发货事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("订单发货事件处理失败: orderId={}", orderId, e);
            throw e;
        }
    }

    @Override
    public void handleOrderConfirmation(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent, OrderContextRecord orderContextRecord) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        log.info("处理订单确认收货事件: orderId={}", orderId);

        try {
            // 1. 更新订单状态为已确认收货
            updateOrderStatusToConfirmed(orderId);

            // 2. 处理收货相关业务逻辑
            processOrderConfirmation(orderId);

            // 3. 发布确认收货完成事件
            publishOrderProcessingCompletedEvent(orderMessage, messageEvent,
              OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS);

            log.info("订单确认收货事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("订单确认收货事件处理失败: orderId={}", orderId, e);
            throw e;
        }
    }

    @Override
    public void handleOrderCompletion(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent, OrderContextRecord orderContextRecord) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        log.info("处理订单完成事件: orderId={}", orderId);

        try {
            // 1. 更新订单状态为已完成
            updateOrderStatusToCompleted(orderId);

            // 2. 处理订单完成相关业务逻辑
            processOrderCompletion(orderId);

            // 3. 发布订单完成事件
            publishOrderProcessingCompletedEvent(orderMessage, messageEvent,
              OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_SUCCESS);

            log.info("订单完成事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("订单完成事件处理失败: orderId={}", orderId, e);
            throw e;
        }
    }

    @Override
    public void handleOrderPriceModification(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent, OrderContextRecord orderContextRecord) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        log.info("处理订单改价事件: orderId={}", orderId);

        try {
            // 1. 更新订单价格信息
            updateOrderPriceInformation(orderId, orderContextRecord);

            // 2. 同步价格变更到WMS
            syncPriceChangeToWms(orderId);

            // 3. 发布改价完成事件
            publishOrderProcessingCompletedEvent(orderMessage, messageEvent,
              OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY);

            log.info("订单改价事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("订单改价事件处理失败: orderId={}", orderId, e);
            throw e;
        }
    }

    @Override
    public void handleBatchOrderPayment(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
      OrderContextRecord orderContextRecord) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        log.info("处理批量订单支付事件: orderId={}", orderId);

        try {
            // 批量支付的处理逻辑与单个支付类似
            handleOrderPayment(orderMessage, messageEvent, orderContextRecord);

            log.info("批量订单支付事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("批量订单支付事件处理失败: orderId={}", orderId, e);
            throw e;
        }
    }

    @Override
    public void handlePartialShipment(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
      OrderContextRecord orderContextRecord) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        log.info("处理部分发货事件: orderId={}", orderId);

        try {
            // 1. 更新部分发货状态
            updatePartialShipmentStatus(orderId);

            // 2. 处理部分发货逻辑
            processPartialShipment(orderId);

            // 3. 发布部分发货完成事件
            publishOrderProcessingCompletedEvent(orderMessage, messageEvent,
              OrderMessageTypeEnums.ORDER_BUYER_VIEW_PART_PART_SENDGOODS);

            log.info("部分发货事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("部分发货事件处理失败: orderId={}", orderId, e);
            throw e;
        }
    }

    /**
     * 发布订单处理完成事件
     */
    private void publishOrderProcessingCompletedEvent(OrderMessage orderMessage,
      MessageEvent<OrderMessage> messageEvent,
      OrderMessageTypeEnums messageType) {
        // TODO: 实现订单处理完成事件的发布
        log.debug("发布订单处理完成事件: orderId={}, messageType={}", orderMessage.getOrderId(), messageType);
    }

    /**
     * 更新订单状态为已支付
     */
    private void updateOrderStatusToPaid(String orderId) {
        log.info("更新订单状态为已支付: orderId={}", orderId);
        // TODO: 实现订单状态更新逻辑
        // 1. 查询采购订单
        // 2. 更新状态为PAYMENT_COMPLETED
        // 3. 更新支付时间
        // 4. 触发状态同步
    }

    /**
     * 触发支付后处理流程
     */
    private void triggerPostPaymentProcesses(String orderId) {
        log.info("触发支付后处理流程: orderId={}", orderId);
        // TODO: 实现支付后处理逻辑
        // 1. 通知WMS系统
        // 2. 发送支付成功邮件
        // 3. 更新用户余额记录
        // 4. 触发采购流程
    }

    /**
     * 更新订单状态为已发货
     */
    private void updateOrderStatusToShipped(String orderId, OrderContextRecord orderContextRecord) {
        log.info("更新订单状态为已发货: orderId={}", orderId);
        OrderDetailResponse.OrderDetail orderDetail = orderContextRecord.orderDetail();
        WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetail = orderContextRecord.getWmsPurchaseOrderDetail();
        TzOrderPurchase purchase = orderContextRecord.tzOrderPurchase();
        TzOrderSupplier orderSupplier = orderContextRecord.getTzOrderSupplierByOrderId(orderId);

        // 判断是否是全部发货
        boolean isAllShipped = orderDetail.getProductItems().stream()
          .allMatch(item -> TzOrderItemLogisticsStatusEnum.SHIPPED.equals(TzOrderItemLogisticsStatusEnum.getByCode(item.getLogisticsStatus())));
        // 获取 多个运单号
        List<String> trackingNos = orderDetail.getNativeLogistics().getLogisticsItems().stream().map(LogisticsItem::getLogisticsBillNo).toList();
        orderSupplier.setStatus(TzOrderSupplierStatusEnum.SHIPPED);
        // 1. 更新采购订单状态
        if(isAllShipped) {
            orderSupplier.setShippedDate(LocalDateTime.now());
            // 使用逗号分割，保存多个运单号
            orderSupplier.setPlatformTrackingNo(String.join(",", trackingNos));
        }else{

        }
        // 2. 更新供应商订单状态
        // 3. 更新订单项状态
        // 4. 记录发货时间
    }

    /**
     * 更新物流信息
     */
    private void updateShippingInformation(String orderId) {
        log.info("更新物流信息: orderId={}", orderId);
        // TODO: 实现物流信息更新逻辑
        // 1. 获取最新的物流信息
        // 2. 更新物流状态
        // 3. 记录物流轨迹
    }

    /**
     * 发送发货通知
     */
    private void sendShipmentNotification(String orderId) {
        log.info("发送发货通知: orderId={}", orderId);
        // TODO: 实现发货通知逻辑
        // 1. 发送邮件通知
        // 2. 发送短信通知（如果需要）
        // 3. 推送站内消息
    }

    /**
     * 更新订单状态为已确认收货
     */
    private void updateOrderStatusToConfirmed(String orderId) {
        log.info("更新订单状态为已确认收货: orderId={}", orderId);
        // TODO: 实现确认收货状态更新逻辑
    }

    /**
     * 处理订单确认收货业务逻辑
     */
    private void processOrderConfirmation(String orderId) {
        log.info("处理订单确认收货业务逻辑: orderId={}", orderId);
        // TODO: 实现确认收货处理逻辑
        // 1. 更新WMS入库状态
        // 2. 触发质检流程
        // 3. 更新库存信息
    }

    /**
     * 更新订单状态为已完成
     */
    private void updateOrderStatusToCompleted(String orderId) {
        log.info("更新订单状态为已完成: orderId={}", orderId);
        // TODO: 实现订单完成状态更新逻辑
    }

    /**
     * 处理订单完成业务逻辑
     */
    private void processOrderCompletion(String orderId) {
        log.info("处理订单完成业务逻辑: orderId={}", orderId);
        // TODO: 实现订单完成处理逻辑
        // 1. 结算供应商费用
        // 2. 更新统计数据
        // 3. 发送完成通知
    }

    /**
     * 更新订单价格信息
     */
    private void updateOrderPriceInformation(String orderId, OrderContextRecord orderContextRecord) {
        log.info("更新订单价格信息: orderId={}", orderId);
        // 1. 获取最新价格信息
        // 更新供应商订单信息
        TzOrderPurchase purchase = orderContextRecord.tzOrderPurchase();
        TzOrderSupplier supplier = orderContextRecord.getTzOrderSupplierByOrderId(orderId);
        Map<String, TzOrderItem> orderItemMap = orderContextRecord.getOrderItemsMapBySupplierOrderId(supplier.getId());

        // 更新仓储订单
        WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetail = orderContextRecord.getWmsPurchaseOrderDetail();

        // 1688 订单信息
        OrderDetail orderDetail = orderContextRecord.orderDetail();
        TradeBaseInfo baseInfo = orderDetail.getBaseInfo();
        // 商品总金额
        BigDecimal productFinalTotalAmount = orderDetail.getProductItems().stream().map(TradeProductItem::getItemAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 2. 更新采购订单金额
        // Plus 折扣
        BigDecimal plusDiscount = baseInfo.getTotalAmount().subtract(baseInfo.getTotalAmount());
        WmsPurchaseOrderDetailReq wmsPurchaseOrderDetailReq = WmsPurchaseOrderDetailReq.builder()
          .purchaseNo(wmsPurchaseOrderDetail.getPurchaseNo())
          .orderId(baseInfo.getId())
          .alibabaFinalAmount(baseInfo.getTotalAmount())
          .finalShoppingFee(baseInfo.getShippingFee())
          .productFinalTotalAmount(productFinalTotalAmount)
          .plusDiscount(plusDiscount)
          .couponDiscount(baseInfo.getCouponFee())
          .build();

        // 3. 计算商品最终单价
        List<WmsPurchaseOrderDetailsReq> wmsOrderDetails =
          orderDetail.getProductItems().stream()
            .filter(productItem -> {
                boolean isExist = Objects.nonNull(orderItemMap.get(String.valueOf(productItem.getSkuId())));
                return false;
            })
            .map(productItem -> {
                // 商品最终单价 = 商品最终总金额 / 商品数量
                WmsPurchaseOrderDetailsReq wmsPurchaseOrderDetailsReq = WmsPurchaseOrderDetailsReq.builder()
//              .skuId()
//              .quantity()
//              .unitPrice(wmsPurchaseOrderDetailsRes.getItemAmount().divide(BigDecimal.valueOf(wmsPurchaseOrderDetailsRes.getQuantity()), 2, RoundingMode.HALF_UP))
                  .build();
                return wmsPurchaseOrderDetailsReq;
            }).collect(Collectors.toList());

        wmsPurchaseOrderDetailReq.setOrderDetails(wmsOrderDetails);
        // 3. 更新供应商订单金额
        // 4. 重新计算服务费
    }

    /**
     * 同步价格变更到WMS
     */
    private void syncPriceChangeToWms(String orderId) {
        log.info("同步价格变更到WMS: orderId={}", orderId);
        // TODO: 实现WMS价格同步逻辑
    }

    /**
     * 更新部分发货状态
     */
    private void updatePartialShipmentStatus(String orderId) {
        log.info("更新部分发货状态: orderId={}", orderId);
        // TODO: 实现部分发货状态更新逻辑
    }

    /**
     * 处理部分发货逻辑
     */
    private void processPartialShipment(String orderId) {
        log.info("处理部分发货逻辑: orderId={}", orderId);
        // TODO: 实现部分发货处理逻辑
        // 1. 更新已发货商品状态
        // 2. 计算剩余未发货商品
        // 3. 更新物流信息
    }
}
