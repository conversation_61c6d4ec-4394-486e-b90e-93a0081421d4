/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.wms.impl;

import com.fulfillmen.shop.common.context.OrderContext;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.dao.mapper.TzUserMapper;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzUser;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.shop.manager.support.wms.convert.WmsPurchaseOrderConvert;
import com.fulfillmen.support.wms.api.WmsApiClient;
import com.fulfillmen.support.wms.dto.common.WmsPageDTO;
import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateOrderReq;
import com.fulfillmen.support.wms.dto.request.WmsOrderQueryReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderReq;
import com.fulfillmen.support.wms.dto.response.WmsAccountInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsCreatePurchaseOrderRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderInfoRes;
import com.fulfillmen.support.wms.exception.WmsApiException;
import com.google.common.collect.Lists;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * WMS 管理器实现类
 *
 * <AUTHOR>
 * @date 2025/7/7
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WmsManagerImpl implements IWmsManager {

    private final WmsApiClient wmsApiClient;
    private final TzOrderSupplierMapper tzOrderSupplierMapper;
    private final TzUserMapper tzUserMapper;
    private final WmsPurchaseOrderConvert wmsPurchaseOrderConvert;

    @Override
    public Optional<WmsAccountInfoRes> getWmsAccountInfo(String authCode, String cusCode) {
        log.info("开始获取WMS账户信息: authCode=[{}], cusCode=[{}]", authCode, cusCode);

        try {
            // 1. 调用WMS API获取原始响应
            WmsAccountInfoRes accountInfo = wmsApiClient.getAccountInfoByAuthCode(authCode);
            return Optional.of(accountInfo);
        } catch (WmsApiException e) {
            log.error("WMS账户信息获取失败，API调用异常: authCode=[{}], error=[{}]", authCode, e.getMessage(), e);
            return Optional.empty();
        } catch (Exception e) {
            log.error("WMS账户信息获取失败，业务处理异常: authCode=[{}], error=[{}]", authCode, e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<List<WmsCreatePurchaseOrderRes>> createWmsPurchaseOrderNew(OrderContext orderContext,
        String cusCode) {
        log.info("开始构建 WMS 采购订单请求，采购单号: {}", orderContext.getPurchaseOrder().getPurchaseOrderNo());

        try {
            // 1. 构建 WMS 订单列表
            List<WmsPurchaseOrderReq> wmsOrders = buildWmsOrders(orderContext);

            // 2. 构建 WMS API 请求体
            WmsCreateOrderReq wmsCreateOrderRequest = buildWmsCreateOrderRequest(wmsOrders, cusCode);

            // 3. 调用 WMS API
            List<WmsCreatePurchaseOrderRes> response = wmsApiClient.createPurchaseOrderNew(wmsCreateOrderRequest);

            log.info("WMS采购订单创建成功，采购单号: {}", orderContext.getPurchaseOrder().getPurchaseOrderNo());
            return Optional.of(response);
        } catch (WmsApiException e) {
            log.error("WMS采购订单创建失败，采购单号: {}, API调用异常: {}",
                orderContext.getPurchaseOrder().getPurchaseOrderNo(), e.getMessage(), e);
        } catch (Exception e) {
            log.error("WMS采购订单创建失败，采购单号: {}, 业务处理异常: {}",
                orderContext.getPurchaseOrder().getPurchaseOrderNo(), e.getMessage(), e);
        }
        return Optional.empty();
    }

    @Override
    public void payWmsPurchaseOrder(OrderContext orderContext, String cusCode) {
        log.info("开始构建 WMS 采购订单请求，采购单号: {}", orderContext.getPurchaseOrder().getPurchaseOrderNo());
        // 1. 获取账户信息，查看余额
        // 1.1 余额不足，扣款失败
        // 2. 发起扣款请求， 采购单号、总金额(运费 5、服务费 1.5、商品金额 150) 156.5
        // 2.1 扣款成功，修改订单状态为已支付
        // 2.2 扣款失败，回滚处理。
        // 2.3 记录账上扣款失败 流水账
    }

    @Override
    public void updateWmsPurchaseOrder(OrderContext orderContext) {
        try {
            log.info("开始更新 WMS 采购订单请求，采购单号: {}", orderContext.getPurchaseOrder().getPurchaseOrderNo());
            List<WmsPurchaseOrderDetailReq> request = buildWmsOrderDetails(orderContext);
            this.wmsApiClient.updatePurchaseOrder(request);
        } catch (Exception e) {
            log.error("WMS采购订单更新失败，采购单号: {}, 业务处理异常: {}",
                orderContext.getPurchaseOrder().getPurchaseOrderNo(), e.getMessage(), e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.API_CALL_FAILED, "updateWmsPurchaseOrder", "更新采购单失败");
        }
    }

    @Override
    public void updateWmsPurchaseOrder(WmsPurchaseOrderDetailReq request) {
        try {
            log.info("开始更新 WMS 采购订单请求，采购单号: {}", request);
            this.wmsApiClient.updatePurchaseOrder(Collections.singletonList(request));
        } catch (Exception e) {
            log.error("WMS采购订单更新失败，采购单号: {}, 业务处理异常: {}",
              request.getPurchaseNo(), e.getMessage(), e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.API_CALL_FAILED, "updateWmsPurchaseOrder", "更新采购单失败");
        }
    }

    @Override
    public void cancelWmsPurchaseOrder(String purchaseOrderNo, String cusCode) {
        try {
            // 查询采购订单列表
            List<WmsPurchaseOrderDetailsRes> wmsPurchaseOrderDetailsRes = this.wmsApiClient.queryOrderDetail(PurchaseOrderDetailReq.builder().purchaseNo(purchaseOrderNo).build());
            // 如果不为空
            if (CollectionUtils.isEmpty(wmsPurchaseOrderDetailsRes)) {
                log.warn("对应的采购订单不存在 : [{}] ", purchaseOrderNo);
                return;
            }
            // 取消采购单下的所有供应商订单
            List<WmsPurchaseOrderDetailReq> wmsPurchaseOrderDetailReqs = Lists.newArrayList();
            wmsPurchaseOrderDetailsRes.forEach(wmsPurchaseOrderDetailsRes1 -> {
                WmsPurchaseOrderDetailReq wmsPurchaseOrderDetailReq = WmsPurchaseOrderDetailReq.builder()
                    .purchaseNo(purchaseOrderNo)
                    .shopOrderId(wmsPurchaseOrderDetailsRes1.getShopOrderId())
                    .status(WmsOrderStatusEnum.CANCELED)
                    .build();
                wmsPurchaseOrderDetailReqs.add(wmsPurchaseOrderDetailReq);
            });
            // 取消
            this.wmsApiClient.updatePurchaseOrder(wmsPurchaseOrderDetailReqs);
        } catch (Exception e) {
            log.error("WMS采购订单更新失败，采购单号: {}, 业务处理异常: {}",
                purchaseOrderNo, e.getMessage(), e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.API_CALL_FAILED, "updateWmsPurchaseOrder", "更新采购单失败");
        }
    }

    /**
     * 构建 WMS API 请求的主体对象
     *
     * @param wmsOrders WMS 订单列表
     * @param cusCode   WMS cusCode
     * @return WMS 创建订单的请求对象
     */
    private WmsCreateOrderReq buildWmsCreateOrderRequest(List<WmsPurchaseOrderReq> wmsOrders, String cusCode) {
        WmsCreateOrderReq request = new WmsCreateOrderReq();
        request.setCustomerCode(cusCode);
        request.setOrders(wmsOrders);

        return request;
    }

    /**
     * 将内部的供应商订单列表转换为 WMS 订单列表
     *
     * @param orderContext 订单上下文
     * @return WMS 订单列表
     */
    private List<WmsPurchaseOrderReq> buildWmsOrders(OrderContext orderContext) {
        // 将所有订单项按其所属的供应商订单ID进行分组，便于查找
        Map<Long, List<TzOrderItem>> itemsBySupplierOrder = orderContext.getOrderItems().stream()
            .collect(Collectors.groupingBy(TzOrderItem::getSupplierOrderId));
        TzOrderPurchase purchaseOrder = orderContext.getPurchaseOrder();
        // 遍历每一个供应商订单，将其转换为WMS订单
        return orderContext.getSupplierOrders().stream()
            .map(supplierOrder -> {
                List<TzOrderItem> orderItems = itemsBySupplierOrder.get(supplierOrder.getId());
                return wmsPurchaseOrderConvert.initWmsCreateOrderRequest(purchaseOrder, supplierOrder, orderItems);
            })
            .collect(Collectors.toList());
    }

    /**
     * 将内部的供应商订单列表转换为 WMS 订单列表
     *
     * @param orderContext 订单上下文
     * @return WMS 订单列表
     */
    private List<WmsPurchaseOrderDetailReq> buildWmsOrderDetails(OrderContext orderContext) {
        // 将所有订单项按其所属的供应商订单ID进行分组，便于查找
        Map<Long, List<TzOrderItem>> itemsBySupplierOrder = orderContext.getOrderItems().stream()
            .collect(Collectors.groupingBy(TzOrderItem::getSupplierOrderId));
        TzOrderPurchase purchaseOrder = orderContext.getPurchaseOrder();
        // 遍历每一个供应商订单，将其转换为WMS订单
        return orderContext.getSupplierOrders().stream()
            .map(supplierOrder -> {
                List<TzOrderItem> orderItems = itemsBySupplierOrder.get(supplierOrder.getId());
                return wmsPurchaseOrderConvert.toWmsPurchaseOrderDetailReq(purchaseOrder, supplierOrder, orderItems);
            })
            .collect(Collectors.toList());
    }

    /**
     * WMS 订单查询
     *
     * @param request WMS订单查询请求
     * @return WMS订单查询响应
     */
    @Override
    public Optional<WmsPageDTO<WmsPurchaseOrderInfoRes>> queryOrder(WmsOrderQueryReq request) {
        log.info("开始WMS订单查询，查询条件: {}", request);

        try {
            // 1. 获取当前登录用户的WMS账户信息
            TzUser currentUser = tzUserMapper.selectById(UserContextHolder.getUserId());
            if (currentUser == null || currentUser.getWmsCusCode() == null) {
                throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.UNABLE_RETRIEVE_WMS_ACCOUNT);
            }

            // 4. 调用WMS API
            WmsPageDTO<WmsPurchaseOrderInfoRes> response = wmsApiClient.queryOrder(request);

            log.info("WMS订单查询成功");
            return Optional.of(response);
        } catch (WmsApiException e) {
            log.error("WMS订单查询失败，API调用异常: {}", e.getMessage(), e);
            return Optional.empty();
        } catch (Exception e) {
            log.error("WMS订单查询失败，业务处理异常: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public List<WmsPurchaseOrderDetailsRes> queryOrderDetail(PurchaseOrderDetailReq request) {
        return wmsApiClient.queryOrderDetail(request);
    }


}
