/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.context.OrderContext;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.OrderSupplierSyncStatusEnums;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.support.wms.dto.response.WmsCreatePurchaseOrderRes;
import com.google.common.collect.Lists;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 创建 wms 订单事件
 *
 * <AUTHOR>
 * @date 2025/7/18 17:54
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CreatePurchaseOrderSyncWmsHandler {

    private final TzOrderSupplierMapper orderSupplierMapper;
    private final IWmsManager wmsManager;

    /**
     * 处理订单创建事件
     *
     * @param orderContext 订单上下文
     */
    public void handle(OrderContext orderContext) {
        log.info("开始创建 WMS 订单，采购订单号: {}", orderContext.getPurchaseOrderNo());
        try {
            // TODO: 2025/7/18 可以先通过 供应商订单号，进行查询。如果已经同步过，则不需要创建，仅同步
            syncToWms(orderContext);
        } catch (Exception e) {
            log.error("同步 wms 订单失败 : [{}] ", orderContext.getPurchaseOrderNo(), e);
        }
    }

    /**
     * 重试同步采购订单到WMS
     *
     * <pre>
     * 用于手动重试同步失败的订单
     * </pre>
     *
     * @param orderContext 订单上下文
     */
    public void retrySyncToWms(OrderContext orderContext) {
        log.info("开始重试同步WMS订单，采购订单号: {}", orderContext.getPurchaseOrderNo());

        try {
            // 检查是否有需要重试的供应商订单
            List<TzOrderSupplier> failedSyncOrders = orderContext.getSupplierOrders().stream()
                .filter(order -> order.getWmsSyncStatus() == null ||
                    order.getWmsSyncStatus() == OrderSupplierSyncStatusEnums.NOT_SYNCED ||
                    order.getWmsSyncStatus() == OrderSupplierSyncStatusEnums.SYNC_FAILED)
                .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(failedSyncOrders)) {
                log.info("所有供应商订单都已成功同步，无需重试: {}", orderContext.getPurchaseOrderNo());
                return;
            }

            log.info("找到需要重试同步的供应商订单: {} 个", failedSyncOrders.size());

            // 更新供应商订单状态为重试中
            updateSupplierOrderSyncStatus(failedSyncOrders, OrderSupplierSyncStatusEnums.NOT_SYNCED, null);

            // 执行同步
            syncToWms(orderContext);

        } catch (Exception e) {
            log.error("重试同步WMS订单失败: [{}]", orderContext.getPurchaseOrderNo(), e);

            // 更新失败状态
            List<TzOrderSupplier> allSupplierOrders = orderContext.getSupplierOrders();
            updateSupplierOrderSyncStatus(allSupplierOrders, OrderSupplierSyncStatusEnums.SYNC_FAILED, e.getMessage());

            throw e;
        }
    }

    /**
     * 同步采购订单到 WMS
     */
    private void syncToWms(OrderContext orderContext) {
        String purchaseOrderNo = orderContext.getPurchaseOrder().getPurchaseOrderNo();
        // TODO: 2025/7/11 同步 wms 采购订单
        // 如果用户未绑定 WMS 账户，则获取租户的信息来采购订单
        String cusCode = UserContextHolder.getWmsCusCodeOrTenantCusCode();

        if (!StringUtils.hasText(cusCode)) {
            log.error("无法获取 cusCode , 找不到 WMS 账户信息，无法创建 Wms 采购订单。请检查一下配置信息。");
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.UNABLE_RETRIEVE_WMS_ACCOUNT);
        }
        // 调用 API 接口
        Optional<List<WmsCreatePurchaseOrderRes>> wmsPurchaseOrder = wmsManager.createWmsPurchaseOrderNew(orderContext,
            cusCode);
        if (wmsPurchaseOrder.isEmpty()) {
            log.error("wms 订单创建失败 : [{}] ", purchaseOrderNo);
            return;
        }
        log.info("wms 订单创建成功 : [{}] ", wmsPurchaseOrder);
        // 更新成功的订单状态 设置同步状态
        updateSuccessOrderStatus(wmsPurchaseOrder.orElse(null));
    }

    /**
     * 更新成功的订单状态
     */
    private void updateSuccessOrderStatus(List<WmsCreatePurchaseOrderRes> successOrderInfos) {
        if (CollectionUtils.isEmpty(successOrderInfos)) {
            return;
        }
        // 获取 shopOrderId 分组 key 是 shopOrderId , value 是 purchaseNo
        Map<String, String> shopOrderIdToPurchaseNoMap = successOrderInfos.stream()
            .collect(Collectors.toMap(WmsCreatePurchaseOrderRes::getShopOrderId,
                WmsCreatePurchaseOrderRes::getPurchaseNo));
        // 获取所有的 供应商订单
        Set<String> supplierNos = shopOrderIdToPurchaseNoMap.keySet();
        // 根据供应商订单号查询供应商订单
        List<TzOrderSupplier> orderSuppliersSuccess = this.orderSupplierMapper.selectList(
            new LambdaQueryWrapper<TzOrderSupplier>().in(TzOrderSupplier::getSupplierOrderNo, supplierNos));
        // 根据 shopOrderIdToPurchaseNoMap 更新供应商订单
        log.info("wms 订单创建成功，更新供应商订单 : [{}] ", shopOrderIdToPurchaseNoMap);
        List<TzOrderSupplier> orderSuppliers = Lists.newArrayList();
        orderSuppliersSuccess.forEach(orderSupplier -> {
            String purchaseNo = shopOrderIdToPurchaseNoMap.get(orderSupplier.getSupplierOrderNo());
            TzOrderSupplier updateOrderSupplier = TzOrderSupplier.builder()
                .id(orderSupplier.getId())
                .wmsPurchaseOrderNo(purchaseNo)
                .wmsSyncStatus(OrderSupplierSyncStatusEnums.SYNCED)
                .build();
            orderSuppliers.add(updateOrderSupplier);
        });
        if (CollectionUtils.isEmpty(orderSuppliers)) {
            log.warn("更新数量空 : [{}] ", orderSuppliers);
            return;
        }
        // 批量更新
        this.orderSupplierMapper.updateBatchById(orderSuppliers);
    }

    /**
     * 批量更新供应商订单同步状态
     * 
     * <pre>
     * 用于更新多个供应商订单的WMS同步状态
     * </pre>
     *
     * @param supplierOrders 供应商订单列表
     * @param syncStatus     同步状态
     * @param errorMessage   错误信息（可选）
     */
    private void updateSupplierOrderSyncStatus(List<TzOrderSupplier> supplierOrders,
        OrderSupplierSyncStatusEnums syncStatus,
        String errorMessage) {
        if (CollectionUtils.isEmpty(supplierOrders)) {
            return;
        }

        List<TzOrderSupplier> updateList = Lists.newArrayList();
        for (TzOrderSupplier order : supplierOrders) {
            TzOrderSupplier updateOrderSupplier = TzOrderSupplier.builder()
                .id(order.getId())
                .wmsSyncStatus(syncStatus)
                .wmsFailedMessage(errorMessage)
                .build();
            updateList.add(updateOrderSupplier);
        }

        // 批量更新
        this.orderSupplierMapper.updateBatchById(updateList);

        log.info("批量更新供应商订单WMS同步状态完成，更新数量: {}, 状态: {}",
            updateList.size(), syncStatus.getDesc());
    }
}
