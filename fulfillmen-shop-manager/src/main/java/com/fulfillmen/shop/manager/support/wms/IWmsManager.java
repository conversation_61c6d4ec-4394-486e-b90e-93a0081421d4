/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.wms;

import com.fulfillmen.shop.common.context.OrderContext;
import com.fulfillmen.support.wms.dto.common.WmsPageDTO;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsOrderQueryReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.response.WmsAccountInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsCreatePurchaseOrderRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderInfoRes;
import java.util.List;
import java.util.Optional;

/**
 * WMS 管理器接口，负责与 WMS 系统进行交互
 *
 * <AUTHOR>
 * @date 2025/7/7
 * @description: todo
 * @since 1.0.0
 */
public interface IWmsManager {

    /**
     * 获取WMS账户信息
     * <p>
     * 通过授权码获取WMS账户信息，包含RSA解密和数据转换处理。
     * </p>
     *
     * @param authCode 授权码
     * @param cusCode  客户码
     * @return WMS账户信息
     */
    Optional<WmsAccountInfoRes> getWmsAccountInfo(String authCode, String cusCode);

    /**
     * 在 WMS 系统中创建采购订单。
     * <p>
     * 此方法会将内部的 OrderContext 对象转换为 WMS API 所需的格式，并调用 WMS 接口。
     *
     * @param orderContext 包含完整订单信息的上下文对象
     * @param cusCode      当前用户的 WMS 账户信息，用于认证
     * @return WMS API 的响应
     */
    Optional<List<WmsCreatePurchaseOrderRes>> createWmsPurchaseOrderNew(OrderContext orderContext,
        String cusCode);

    /**
     * 在 WMS 系统中支付采购订单。
     * <p>
     * 此方法会将内部的 OrderContext 对象转换为 WMS API 所需的格式，并调用 WMS 接口。
     *
     * @param orderContext 包含完整订单信息的上下文对象
     * @param cusCode      当前用户的 WMS 账户信息，用于认证
     */
    void payWmsPurchaseOrder(OrderContext orderContext, String cusCode);

    /**
     * 更新 WMS 采购订单
     *
     * @param orderContext 包含完整订单信息的上下文对象
     */
    void updateWmsPurchaseOrder(OrderContext orderContext);

    /**
     * 更新 WMS 采购订单
     *
     * @param request 更新请求
     */
    void updateWmsPurchaseOrder(WmsPurchaseOrderDetailReq request);

    /**
     * 取消 WMS 采购订单
     *
     * @param purchaseOrderNo 采购订单号
     * @param cusCode         当前用户的 WMS 账户信息，用于认证
     */
    void cancelWmsPurchaseOrder(String purchaseOrderNo, String cusCode);

    /**
     * 查询 WMS 订单
     *
     * @param request WMS订单查询请求
     * @return WMS API 的响应
     */
    Optional<WmsPageDTO<WmsPurchaseOrderInfoRes>> queryOrder(WmsOrderQueryReq request);

    /**
     * 查询 WMS 订单详情
     *
     * @param request 采购订单详情查询请求
     * @return WMS API 的响应
     */
    List<WmsPurchaseOrderDetailsRes> queryOrderDetail(PurchaseOrderDetailReq request);
}
