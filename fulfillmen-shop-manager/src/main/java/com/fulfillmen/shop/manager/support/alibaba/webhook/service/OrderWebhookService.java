/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.service;

import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.webhook.MessageEvent;
import com.fulfillmen.support.alibaba.webhook.data.OrderMessage;

/**
 * 订单Webhook业务处理服务接口
 *
 * <pre>
 * 职责：
 * 1. 处理订单webhook消息的核心业务逻辑
 * 2. 协调数据补齐和兼容性处理
 * 3. 集成订单状态管理系统
 * 4. 提供统一的错误处理和恢复机制
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/25 16:00
 * @description 订单webhook业务处理服务，支持新旧版本数据兼容
 * @since 1.0.0
 */
public interface OrderWebhookService {

    /**
     * 处理订单webhook消息
     *
     * <pre>
     * 处理流程：
     * 1. 验证消息有效性
     * 2. 检查数据存在性（新版/旧版判断）
     * 3. 执行数据补齐（如果需要）
     * 4. 路由到具体的业务处理逻辑
     * 5. 触发相关事件和状态更新
     * </pre>
     *
     * @param orderMessage 订单消息数据
     * @param messageEvent 消息事件
     * @param messageType  消息类型枚举
     */
    void processOrderWebhook(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent, OrderMessageTypeEnums messageType);

    /**
     * 处理订单创建事件
     *
     * @param orderMessage       订单消息
     * @param messageEvent       消息事件
     * @param orderContextRecord
     */
    void handleOrderCreation(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent, OrderContextRecord orderContextRecord);

    /**
     * 处理订单支付事件
     *
     * @param orderMessage       订单消息
     * @param messageEvent       消息事件
     * @param orderContextRecord
     */
    void handleOrderPayment(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent, OrderContextRecord orderContextRecord);

    /**
     * 处理订单发货事件
     *
     * @param orderMessage       订单消息
     * @param messageEvent       消息事件
     * @param orderContextRecord
     */
    void handleOrderShipment(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent, OrderContextRecord orderContextRecord);

    /**
     * 处理订单确认收货事件
     *
     * @param orderMessage       订单消息
     * @param messageEvent       消息事件
     * @param orderContextRecord
     */
    void handleOrderConfirmation(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent, OrderContextRecord orderContextRecord);

    /**
     * 处理订单完成事件
     *
     * @param orderMessage       订单消息
     * @param messageEvent       消息事件
     * @param orderContextRecord
     */
    void handleOrderCompletion(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent, OrderContextRecord orderContextRecord);

    /**
     * 处理订单改价事件
     *
     * @param orderMessage       订单消息
     * @param messageEvent       消息事件
     * @param orderContextRecord
     */
    void handleOrderPriceModification(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent, OrderContextRecord orderContextRecord);

    /**
     * 处理批量订单支付事件
     *
     * @param orderMessage       订单消息
     * @param messageEvent       消息事件
     * @param orderContextRecord
     */
    void handleBatchOrderPayment(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent, OrderContextRecord orderContextRecord);

    /**
     * 处理部分发货事件
     *
     * @param orderMessage       订单消息
     * @param messageEvent       消息事件
     * @param orderContextRecord
     */
    void handlePartialShipment(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent, OrderContextRecord orderContextRecord);
}
