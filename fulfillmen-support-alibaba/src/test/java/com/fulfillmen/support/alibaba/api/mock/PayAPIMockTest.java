/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.mock;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import cn.hutool.json.JSONUtil;
import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.PayAPI;
import com.fulfillmen.support.alibaba.api.request.pay.AccountPeriodListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.AlipayUrlRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.CheckProtocolPayRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.CrossBorderPayUrlRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.PayWayQueryRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.PrepareProtocolPayRequestRecord;
import com.fulfillmen.support.alibaba.api.response.model.PayTypeInfo;
import com.fulfillmen.support.alibaba.api.response.pay.AccountPeriodListResponse;
import com.fulfillmen.support.alibaba.api.response.pay.AlipayUrlResponse;
import com.fulfillmen.support.alibaba.api.response.pay.CheckProtocolPayResponse;
import com.fulfillmen.support.alibaba.api.response.pay.CrossBorderPayUrlResponse;
import com.fulfillmen.support.alibaba.api.response.pay.PayWayQueryResponse;
import com.fulfillmen.support.alibaba.api.response.pay.PrepareProtocolPayResponse;
import com.fulfillmen.support.alibaba.service.IPayService;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import reactor.test.StepVerifier;

/**
 * 1688支付API Mock测试
 *
 * <AUTHOR>
 * @created 2025-01-10
 */
@Slf4j
class PayAPIMockTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private IPayService payService;

    @MockBean
    private PayAPI payAPI;

    @Test
    void shouldQueryPayWay() {
        // Given
        var request = PayWayQueryRequestRecord.of(2429989502226540788L);

        // Mock 成功响应
        PayWayQueryResponse successResponse = JSONUtil.toBean("""
          {"resultList":{
            "channels":[
                {"code":3,"name":"诚e赊"},{"code":20,"name":"跨境宝2.0"},{"code":1,"name":"支付宝"},{"code":18,"name":"其它"},{"code":34,"name":"其它"}
            ],
            "orderId":"2854285719055540788","payFee":5180,"timeout":"2025-08-04 02:08:03"},
           "success":"true"
          }
          """, PayWayQueryResponse.class);

        when(payAPI.queryPayWay(any(), any())).thenReturn(reactor.core.publisher.Mono.just(successResponse));

        // When
        var response = payService.getPayWayQuery(request);

        // Then
        StepVerifier.create(response).assertNext(queryResponse -> {
            assertThat(queryResponse).isNotNull();
            assertThat(queryResponse.getSuccess()).isTrue();
            assertThat(queryResponse.getResultList()).isNotNull();
            assertThat(queryResponse.getResultList().getChannels().stream().map(PayTypeInfo::getName)).contains("alipay", "creditBuy", "crossBorderPay");
        }).verifyComplete();
    }

    @Test
    void shouldHandleQueryPayWayError() {
        // Given
        var request = PayWayQueryRequestRecord.of(9999999999999999L);

        // Mock 错误响应
        PayWayQueryResponse errorResponse = JSONUtil.toBean("""
          {
              "success": false,
              "errorCode": "400_5",
              "errorMessage": "订单不存在或已关闭"
          }""", PayWayQueryResponse.class);

        // When
        when(payAPI.queryPayWay(any(), any())).thenReturn(reactor.core.publisher.Mono.just(errorResponse));

        // Then
        StepVerifier.create(payService.getPayWayQuery(request)).assertNext(queryResponse -> {
            assertThat(queryResponse).isNotNull();
            assertThat(queryResponse.getSuccess()).isFalse();
            assertThat(queryResponse.getErrorCode()).isEqualTo("400_5");
            assertThat(queryResponse.getErrorMessage()).isEqualTo("订单不存在或已关闭");
        }).verifyComplete();
    }

    @Test
    void shouldGetCrossBorderPayUrl() {
        // Given
        var request = CrossBorderPayUrlRequestRecord.of(Arrays.asList(2429989502226540788L));

        // Mock 成功响应
        CrossBorderPayUrlResponse successResponse = JSONUtil.toBean("""
          {
              "success": true,
              "payUrl": "https://pay.1688.com/crossborder/123456",
              "cantPayOrderList": []
          }""", CrossBorderPayUrlResponse.class);

        when(payAPI.getCrossBorderPayUrl(any(), any())).thenReturn(reactor.core.publisher.Mono.just(successResponse));

        // When
        var response = payService.getCrossBorderPayUrl(request);

        // Then
        StepVerifier.create(response).assertNext(payUrlResponse -> {
            assertThat(payUrlResponse).isNotNull();
            assertThat(payUrlResponse.getSuccess()).isTrue();
            assertThat(payUrlResponse.getPayUrl()).isNotEmpty();
            assertThat(payUrlResponse.getCantPayOrderList()).isEmpty();
        }).verifyComplete();
    }

    @Test
    void shouldCheckProtocolPay() {
        // Given
        var request = CheckProtocolPayRequestRecord.of();

        // Mock 成功响应
        CheckProtocolPayResponse successResponse = JSONUtil.toBean("""
          {
              "success": true,
              "result": {
                  "paymentAgreements": [
                      {
                          "payChannel": "ALIPAY",
                          "bindingStatus": "BINDING",
                          "signedStatus": "SIGNED",
                          "signUrl": ""
                      }
                  ]
              }
          }""", CheckProtocolPayResponse.class);

        when(payAPI.checkProtocolPay(any(), any())).thenReturn(reactor.core.publisher.Mono.just(successResponse));

        // When
        var response = payService.checkProtocolPay(request);

        // Then
        StepVerifier.create(response).assertNext(checkResponse -> {
            assertThat(checkResponse).isNotNull();
            assertThat(checkResponse.getSuccess()).isTrue();
            assertThat(checkResponse.getResult()).isNotNull();
            assertThat(checkResponse.getResult().getPaymentAgreements()).isNotEmpty();
        }).verifyComplete();
    }

    @Test
    void shouldGetAlipayUrl() {
        // Given
        var request = AlipayUrlRequestRecord.of(Arrays.asList(2429989502226540788L));

        // Mock 成功响应
        var successResponse = JSONUtil.toBean("""
          {
              "success": true,
              "payUrl": "https://pay.1688.com/alipay/123456",
              "payFailureOrderList": []
          }""", AlipayUrlResponse.class);

        when(payAPI.getAlipayUrl(any(), any())).thenReturn(reactor.core.publisher.Mono.just(successResponse));

        // When
        var response = payService.getAlipayUrl(request);

        // Then
        StepVerifier.create(response).assertNext(payUrlResponse -> {
            assertThat(payUrlResponse).isNotNull();
            assertThat(payUrlResponse.getSuccess()).isTrue();
            assertThat(payUrlResponse.getPayUrl()).isNotEmpty();
            assertThat(payUrlResponse.getPayFailureOrderList()).isEmpty();
        }).verifyComplete();
    }

    @Test
    void shouldGetAccountPeriodList() {
        // Given
        var request = AccountPeriodListRequestRecord.of(1L, "test");

        // Mock 成功响应
        var successResponse = JSONUtil.toBean("""
          {
              "success": true,
              "resultList": {
                  "totalCount": "10",
                  "accountPeriodList": [
                      {
                      "sellerLoginId": "seller123",
                      "sellerCompanyName": "测试公司",
                          "quota": 1000000,
                          "surplusQuota": 800000,
                          "statusStr": "正常",
                          "tapDateStr": "30天",
                          "tapOverdue": 0
                      }
                  ]
              }
          }""", AccountPeriodListResponse.class);

        when(payAPI.getAccountPeriodList(any(), any())).thenReturn(reactor.core.publisher.Mono.just(successResponse));

        // When
        var response = payService.getAccountPeriodList(request);

        // Then
        StepVerifier.create(response).assertNext(listResponse -> {
            assertThat(listResponse).isNotNull();
            assertThat(listResponse.getSuccess()).isTrue();
            assertThat(listResponse.getResultList().getTotalCount()).isEqualTo("10");
            assertThat(listResponse.getResultList().getAccountPeriodList()).isNotEmpty();
        }).verifyComplete();
    }

    @Test
    void shouldPrepareProtocolPay() {
        // Given
        var request = PrepareProtocolPayRequestRecord.of(2429989502226540788L, "kjpayV2", 10000L, "test_request_id");

        // Mock 成功响应
        PrepareProtocolPayResponse successResponse = JSONUtil.toBean("""
          {
              "success": true,
              "result": {
                  "payChannel": "kjpayV2",
                  "paySuccess": true
              }
          }""", PrepareProtocolPayResponse.class);

        when(payAPI.prepareProtocolPay(any(), any())).thenReturn(reactor.core.publisher.Mono.just(successResponse));

        // When
        var response = payService.prepareProtocolPay(request);

        // Then
        StepVerifier.create(response).assertNext(payResponse -> {
            assertThat(payResponse).isNotNull();
            assertThat(payResponse.getSuccess()).isTrue();
            assertThat(payResponse.getResult()).isNotNull();
            assertThat(payResponse.getResult().getPaySuccess()).isTrue();
        }).verifyComplete();
    }
}