/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration;

import static org.assertj.core.api.Assertions.assertThat;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.request.pay.AccountPeriodListRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.AlipayUrlRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.CheckProtocolPayRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.CrossBorderPayUrlRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.PayWayQueryRequestRecord;
import com.fulfillmen.support.alibaba.api.response.model.PayTypeInfo;
import com.fulfillmen.support.alibaba.service.IPayService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.test.StepVerifier;

/**
 * 1688支付API集成测试
 *
 * <AUTHOR>
 * @created 2025-01-10
 */
@Slf4j
class PayAPIIntegrationTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private IPayService payService;

    @Test
    void shouldQueryPayWay() {
        // Given
        var request = PayWayQueryRequestRecord.of(2854285719055540788L);

        log.info("{}开始查询支付方式测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}订单ID: {}", LOG_ITEM, request.orderId());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = payService.getPayWayQuery(request);

        // Then
        StepVerifier.create(response).assertNext(queryResponse -> {
            // 记录API调用指标
            recordMetrics("QueryPayWay", startTime, queryResponse != null && queryResponse.getSuccess());

            assertThat(queryResponse).isNotNull();
            // {"errorCode":"500_3","errorMsg":"订单不是待支付状态","success":"false"}
            if (!queryResponse.getSuccess()) {
                log.info("{}查询支付方式失败{}", LOG_SEPARATOR, LOG_SEPARATOR);
                log.info("{}错误码: {}", LOG_ITEM, queryResponse.getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, queryResponse.getErrorMsg());
                return;
            }
            assertThat(queryResponse.getResultList()).isNotNull();
            assertThat(queryResponse.getResultList().getChannels()).isNotEmpty();

            // 打印查询结果
            log.info("{}查询支付方式结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, queryResponse.getSuccess());
            log.info("{}可用支付方式: {}", LOG_ITEM, String.join(", ", queryResponse.getResultList().getChannels().stream().map(PayTypeInfo::getName).toArray(String[]::new)  ));
            log.info("{}订单金额: {} 元", LOG_ITEM, queryResponse.getResultList().getTotalAmount().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("QueryPayWay");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetCrossBorderPayUrl() {
        // Given
        var request = CrossBorderPayUrlRequestRecord.of(Arrays.asList(2429989502226540788L));

        log.info("{}开始获取跨境宝支付链接测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}订单列表: {}", LOG_ITEM, request.orderIdList());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = payService.getCrossBorderPayUrl(request);

        // Then
        StepVerifier.create(response).assertNext(payUrlResponse -> {
            // 记录API调用指标
            recordMetrics("GetCrossBorderPayUrl", startTime, payUrlResponse != null && payUrlResponse.getSuccess());

            assertThat(payUrlResponse).isNotNull();
            // {"errorCode":"500_3","errorMsg":"订单不是待支付状态","success":"false"}
            if (!payUrlResponse.getSuccess()) {
                log.info("{}获取跨境宝支付链接失败{}", LOG_SEPARATOR, LOG_SEPARATOR);
                log.info("{}错误码: {}", LOG_ITEM, payUrlResponse.getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, payUrlResponse.getErrorMsg());
                return;
            }
            assertThat(payUrlResponse.getPayUrl()).isNotEmpty();

            // 打印支付链接结果
            log.info("{}获取跨境宝支付链接结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, payUrlResponse.getSuccess());
            log.info("{}支付链接: {}", LOG_ITEM, payUrlResponse.getPayUrl());
            if (!payUrlResponse.getCantPayOrderList().isEmpty()) {
                log.info("{}不可支付订单: {}", LOG_ITEM, payUrlResponse.getCantPayOrderList());
            }
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetCrossBorderPayUrl");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldCheckProtocolPay() {
        // Given
        var request = CheckProtocolPayRequestRecord.of();

        log.info("{}开始查询免密支付状态测试{}", LOG_SEPARATOR, LOG_SEPARATOR);

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = payService.checkProtocolPay(request);

        // Then
        StepVerifier.create(response).assertNext(checkResponse -> {
            // 记录API调用指标
            recordMetrics("CheckProtocolPay", startTime, checkResponse != null && checkResponse.getSuccess());

            assertThat(checkResponse).isNotNull();
            // {"errorCode":"500_3","errorMsg":"订单不是待支付状态","success":"false"}
            if (!checkResponse.getSuccess()) {
                log.info("{}查询免密支付状态失败{}", LOG_SEPARATOR, LOG_SEPARATOR);
                log.info("{}错误码: {}", LOG_ITEM, checkResponse.getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, checkResponse.getErrorMsg());
                return;
            }
            assertThat(checkResponse.getResult()).isNotNull();
            assertThat(checkResponse.getResult().getPaymentAgreements()).isNotEmpty();

            // 打印查询结果
            log.info("{}查询免密支付状态结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, checkResponse.getSuccess());
            var agreement = checkResponse.getResult().getPaymentAgreements().get(0);
            log.info("{}支付通道: {}", LOG_ITEM, agreement.getPayChannel());
            log.info("{}绑定状态: {}", LOG_ITEM, agreement.getBindingStatus());
            log.info("{}签约状态: {}", LOG_ITEM, agreement.getSignedStatus());
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("CheckProtocolPay");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetAlipayUrl() {
        // Given
        var request = AlipayUrlRequestRecord.of(Arrays.asList(2429989502226540788L));

        log.info("{}开始获取支付宝支付链接测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}订单列表: {}", LOG_ITEM, request.orderIdList());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = payService.getAlipayUrl(request);

        // Then
        StepVerifier.create(response).assertNext(payUrlResponse -> {
            // 记录API调用指标
            recordMetrics("GetAlipayUrl", startTime, payUrlResponse != null && payUrlResponse.getSuccess());

            assertThat(payUrlResponse).isNotNull();
            // {"errorCode":"500_3","errorMsg":"订单不是待支付状态","success":"false"}
            if (!payUrlResponse.getSuccess()) {
                log.info("{}获取支付宝支付链接失败{}", LOG_SEPARATOR, LOG_SEPARATOR);
                log.info("{}错误码: {}", LOG_ITEM, payUrlResponse.getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, payUrlResponse.getErrorMsg());
                return;
            }
            assertThat(payUrlResponse.getPayUrl()).isNotEmpty();

            // 打印支付链接结果
            log.info("{}获取支付宝支付链接结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, payUrlResponse.getSuccess());
            log.info("{}支付链接: {}", LOG_ITEM, payUrlResponse.getPayUrl());
            if (!payUrlResponse.getPayFailureOrderList().isEmpty()) {
                log.info("{}支付失败订单: {}", LOG_ITEM, payUrlResponse.getPayFailureOrderList());
            }
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetAlipayUrl");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    void shouldGetAccountPeriodList() {
        // Given
        var request = AccountPeriodListRequestRecord.of(1L, null);

        log.info("{}开始查询账期信息测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}页码: {}", LOG_ITEM, request.pageIndex());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When
        var response = payService.getAccountPeriodList(request);

        // Then
        StepVerifier.create(response).assertNext(listResponse -> {
            // 记录API调用指标
            recordMetrics("GetAccountPeriodList", startTime, listResponse != null && listResponse.getSuccess());

            assertThat(listResponse).isNotNull();
            // {"errorCode":"500_3","errorMsg":"订单不是待支付状态","success":"false"}
            if (!listResponse.getSuccess()) {
                log.info("{}查询账期信息失败{}", LOG_SEPARATOR, LOG_SEPARATOR);
                log.info("{}错误码: {}", LOG_ITEM, listResponse.getErrorCode());
                log.info("{}错误信息: {}", LOG_ITEM, listResponse.getErrorMsg());
                return;
            }

            // 打印账期信息结果
            log.info("{}查询账期信息结果{}", LOG_SEPARATOR, LOG_SEPARATOR);
            log.info("{}是否成功: {}", LOG_ITEM, listResponse.getSuccess());
            log.info("{}总记录数: {}", LOG_ITEM, listResponse.getResultList().getTotalCount());
            if (listResponse.getResultList().getAccountPeriodList() == null || listResponse.getResultList()
                .getAccountPeriodList()
                .isEmpty()) {
                log.info("{}账期信息为空{}", LOG_SEPARATOR, LOG_SEPARATOR);
                return;
            }
            var periodInfo = listResponse.getResultList().getAccountPeriodList().get(0);
            log.info("{}卖家账号: {}", LOG_ITEM, periodInfo.getSellerLoginId());
            log.info("{}公司名称: {}", LOG_ITEM, periodInfo.getSellerCompanyName());
            log.info("{}授信额度: {} 分", LOG_ITEM, periodInfo.getQuota());
            log.info("{}可用额度: {} 分", LOG_ITEM, periodInfo.getSurplusQuota());
            log.info("{}账期状态: {}", LOG_ITEM, periodInfo.getStatusStr());
            log.info("{}账期时间: {}", LOG_ITEM, periodInfo.getTapDateStr());
            log.info("{}逾期次数: {}", LOG_ITEM, periodInfo.getTapOverdue());
        }).verifyComplete();

        // 打印性能指标
        log.info("{}性能指标{}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("GetAccountPeriodList");
        log.info("{}测试完成{}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}