/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.pay;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.PayTypeInfo;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查询订单可用支付方式响应
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.payWay.query-1">API文档</a>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PayWayQueryResponse extends BaseAlibabaResponse {

    /**
     * 查询结果
     */
    private PayWayQueryResult resultList;

    @Data
    @NoArgsConstructor
    public static class PayWayQueryResult {

        /**
         * 可用支付方式列表
         */
        private List<PayTypeInfo> channels;

        /**
         * 订单 id
         */
        private String orderId;

        /**
         * 支付超时时间
         */
        private String timeout;

        /**
         * 订单总金额（分）
         */
        @JsonProperty("payFee")
        private BigDecimal totalAmount;
    }

}