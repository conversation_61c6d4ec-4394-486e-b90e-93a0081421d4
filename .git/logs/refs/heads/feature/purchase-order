0000000000000000000000000000000000000000 16a9f7ac66162b82d470927fbe4e3a16877a8e9c james <<EMAIL>> 1753686845 +0800	branch: Created from HEAD
16a9f7ac66162b82d470927fbe4e3a16877a8e9c c7302155059f380238768a2238723df72501e03f james <<EMAIL>> 1753698789 +0800	commit: feat: 更新订单处理相关配置和逻辑
c7302155059f380238768a2238723df72501e03f 5cea7c791262e57ccc88ab6844da868a836e08e9 james <<EMAIL>> 1753786294 +0800	commit: 更新 pom.xml 版本至 1.2.1-SNAPSHOT；在 SpringTxConfig.java 中增加版权注释并优化注释内容；在 application-local.yml 中添加本地测试环境配置；在 fulfillmen-shop-common/pom.xml 中新增 Redisson 缓存依赖；重构 TzOrderPurchase 类，修改买家类型字段为枚举类型；新增 TzOrderPurchaseBuyerTypeEnums 枚举类以支持买家类型；在多个处理类中优化代码格式，提升可读性。这些更改旨在增强代码的可维护性和可读性，同时支持新的功能需求。
5cea7c791262e57ccc88ab6844da868a836e08e9 0df4180d968cba66a72b525f3e426aff5291f5d4 james <<EMAIL>> 1753929024 +0800	commit: 更新 pom.xml 中的 JDK 版本至 21，并优化编译配置；移除 TenantFilter.java 中的无用路径；在 TzOrderItemMapper.java 和 TzOrderPurchaseMapper.java 中更新日期注释；在 TzOrderSupplierMapper.java 中修改方法参数名称以提高可读性；在多个 XML 映射文件中新增字段以支持更多订单信息；新增多个 VO 类以支持前端数据交互。这些更改旨在提升代码的可维护性和可读性，同时支持新的功能需求。
