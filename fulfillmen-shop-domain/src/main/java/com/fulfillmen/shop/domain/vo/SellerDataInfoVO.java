/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 卖家数据信息VO
 *
 * <AUTHOR>
 * @date 2025/6/3 17:04
 * @description: 卖家相关的数据信息
 * @since 1.0.0
 */
@Data
@Schema(description = "卖家数据信息")
public class SellerDataInfoVO {

    @Schema(description = "贸易勋章等级")
    private Integer tradeMedalLevel;

    @Schema(description = "综合服务得分")
    private String compositeServiceScore;

    @Schema(description = "物流体验得分")
    private String logisticsExperienceScore;

    @Schema(description = "纠纷投诉得分")
    private String disputeComplaintScore;

    @Schema(description = "报价体验得分")
    private String offerExperienceScore;

    @Schema(description = "售后体验得分")
    private String afterSalesExperienceScore;

    @Schema(description = "咨询体验得分")
    private String consultingExperienceScore;

    @Schema(description = "复购率百分比")
    private String repeatPurchasePercent;
}
