package com.fulfillmen.shop.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fulfillmen.shop.domain.entity.enums.OrderSupplierSyncStatusEnums;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierMultipleOrdersEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 供应商订单表-按供应商拆分的履约订单
 *
 * <AUTHOR>
 * @date 2025/7/30 18:57
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "tz_order_supplier")
public class TzOrderSupplier implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 采购订单ID
     */
    @TableField(value = "purchase_order_id")
    private Long purchaseOrderId;

    /**
     * 供应商订单编号
     */
    @TableField(value = "supplier_order_no")
    private String supplierOrderNo;

    /**
     * 平台代码: 1688/TAOBAO/JD/PDD
     */
    @TableField(value = "platform_code")
    private PlatformCodeEnum platformCode;

    /**
     * 供应商ID
     */
    @TableField(value = "supplier_id")
    private String supplierId;

    /**
     * 供应商名称
     */
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 供应商店铺名称
     */
    @TableField(value = "supplier_shop_name")
    private String supplierShopName;

    /**
     * 平台下单后，返回的元数据。记录，用于还原和恢复。
     */
    @TableField(value = "metadata_json")
    private String metadataJson;

    /**
     * 是否产生了多个订单id,注：同一供应商，创建订单平台可能分配多个订单 ID。 默认 0 否，1 是
     */
    @TableField(value = "is_multiple_orders")
    private TzOrderSupplierMultipleOrdersEnum isMultipleOrders;

    /**
     * 外部平台系统同步状态, 0 未同步，1 已同步，2 同步失败
     */
    @TableField(value = "external_sync_status")
    private OrderSupplierSyncStatusEnums externalSyncStatus;

    /**
     * 同步创建外部平台失败原因。external_sync_status = 2 ，此处就是失败的原因
     */
    @TableField(value = "external_sync_failed_message")
    private String externalSyncFailedMessage;

    /**
     * 外部平台订单ID
     */
    @TableField(value = "platform_order_id")
    private String platformOrderId;

    /**
     * 外部平台订单号
     */
    @TableField(value = "platform_order_no")
    private String platformOrderNo;

    /**
     * 外部平台交易流水号
     */
    @TableField(value = "platform_trade_no")
    private String platformTradeNo;

    /**
     * 外部平台支付链接
     */
    @TableField(value = "platform_pay_url")
    private String platformPayUrl;

    /**
     * 交易类型 code 逗号分割(1,2,3,4)，1:担保交易 2:预存款交易 3:ETC境外收单交易 4:即时到帐交易 5:保障金安全交易 6:统一交易流程 7:分阶段付款 8.货到付款交易 9.信用凭证支付交易 10.账期支付交易，50060 交易4.0
     */
    @TableField(value = "platform_trade_type")
    private String platformTradeType;

    /**
     * 交易类型描述(以逗号分割 担保交易,预存款交易) ，1:担保交易 2:预存款交易 3:ETC境外收单交易 4:即时到帐交易 5:保障金安全交易 6:统一交易流程 7:分阶段付款 8.货到付款交易 9.信用凭证支付交易 10.账期支付交易，50060 交易4.0
     */
    @TableField(value = "platform_trade_type_desc")
    private String platformTradeTypeDesc;

    /**
     * 物流单号。
     */
    @TableField(value = "platform_tracking_no")
    private String platformTrackingNo;

    /**
     * wms 系统采购单号,如果 wms_purchase_status
     */
    @TableField(value = "wms_purchase_order_no")
    private String wmsPurchaseOrderNo;

    /**
     * 同步状态。0 未创建 wms 采购单，1 已创建 , 2 创建失败
     */
    @TableField(value = "wms_sync_status")
    private OrderSupplierSyncStatusEnums wmsSyncStatus;

    /**
     * wms 创建采购单失败原因，如果 wms_sync_status=2 ，同步失败原因描述。
     */
    @TableField(value = "wms_failed_message")
    private String wmsFailedMessage;

    /**
     * 履约状态: 0待支付/1已支付/4待发货/5已发货/6已收货/7已完成/8已取消/9申请退款中/10退款完成
     */
    @TableField(value = "`status`")
    private TzOrderSupplierStatusEnum status;

    /**
     * 下单时间
     */
    @TableField(value = "order_date")
    private LocalDateTime orderDate;

    /**
     * 支付时间
     */
    @TableField(value = "payment_date")
    private LocalDateTime paymentDate;

    /**
     * 采购时间
     */
    @TableField(value = "procurement_date")
    private LocalDateTime procurementDate;

    /**
     * 发货时间
     */
    @TableField(value = "shipped_date")
    private LocalDateTime shippedDate;

    /**
     * 交付时间
     */
    @TableField(value = "delivered_date")
    private LocalDateTime deliveredDate;

    /**
     * 完成时间
     */
    @TableField(value = "completed_date")
    private LocalDateTime completedDate;

    /**
     * 取消时间
     */
    @TableField(value = "cancelled_date")
    private LocalDateTime cancelledDate;

    /**
     * 客户支付商品总价
     */
    @TableField(value = "customer_goods_amount")
    private BigDecimal customerGoodsAmount;

    /**
     * 客户支付运费
     */
    @TableField(value = "customer_freight_amount")
    private BigDecimal customerFreightAmount;

    /**
     * 客户支付运费
     */
    @TableField(value = "customer_total_amount")
    private BigDecimal customerTotalAmount;

    /**
     * 服务费 - tenant_info.service_fee 租户设定服务费率。
     */
    @TableField(value = "service_fee")
    private BigDecimal serviceFee;

    /**
     * 应付商品总价
     */
    @TableField(value = "payable_goods_amount")
    private BigDecimal payableGoodsAmount;

    /**
     * 应付运费
     */
    @TableField(value = "payable_freight_amount")
    private BigDecimal payableFreightAmount;

    /**
     * 应付的金额 = payable_freight_amount + payable_goods_amount + service_fee
     */
    @TableField(value = "payable_amount_total")
    private BigDecimal payableAmountTotal;

    /**
     * 优惠金额（此字段不展示给用户看）
     */
    @TableField(value = "payable_discount_amount")
    private BigDecimal payableDiscountAmount;

    /**
     * 红包/优惠卷金额
     */
    @TableField(value = "payable_coupon_amount")
    private BigDecimal payableCouponAmount;

    /**
     * Plus会员折扣（此字段不展示给用户看）
     */
    @TableField(value = "payable_plus_discount_amount")
    private BigDecimal payablePlusDiscountAmount;

    /**
     * 最终实付金额，商家改价后金额。财务实际支付的金额（此字段不展示给用户看）
     */
    @TableField(value = "actual_payment_amount")
    private BigDecimal actualPaymentAmount;

    /**
     * 最终产品总价，商家改价后信息（此字段不展示给用户看）
     */
    @TableField(value = "actual_payment_goods_amount")
    private BigDecimal actualPaymentGoodsAmount;

    /**
     * 最终运费金额，一旦改价后显示在此（此字段不展示给用户看）
     */
    @TableField(value = "actual_payment_freight_amount")
    private BigDecimal actualPaymentFreightAmount;

    /**
     * 订单实际使用的优惠的金额
     */
    @TableField(value = "actual_payment_discount_amount")
    private BigDecimal actualPaymentDiscountAmount;

    /**
     * 订单实际使用红包或优惠卷金额
     */
    @TableField(value = "actual_payment_coupon_amount")
    private BigDecimal actualPaymentCouponAmount;

    /**
     * 订单实际plus 会员优惠金额
     */
    @TableField(value = "actual_payment_plus_amount")
    private BigDecimal actualPaymentPlusAmount;

    /**
     * 订单行数
     */
    @TableField(value = "line_item_count")
    private Integer lineItemCount;

    /**
     * 已完成的订单项数量
     */
    @TableField(value = "completed_item_count")
    private Integer completedItemCount;

    /**
     * 供应商备注
     */
    @TableField(value = "supplier_notes")
    private String supplierNotes;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 是否删除
     */
    @TableField(value = "is_deleted")
    private Long isDeleted;

    /**
     * 数据版本
     */
    @TableField(value = "revision")
    private Integer revision;

    /**
     * 创建用户id  tz_user.id
     */
    @TableField(value = "created_by")
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private LocalDateTime gmtModified;
}
