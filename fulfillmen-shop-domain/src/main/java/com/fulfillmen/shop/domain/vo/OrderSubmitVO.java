/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;

/**
 * 订单提交响应VO
 *
 * <AUTHOR>
 * @date 2025/6/23
 * @description 订单提交后的响应信息
 */
@Data
@Builder
@Schema(name = "OrderSubmitVO", description = "订单提交响应VO")
public class OrderSubmitVO {

    /**
     * 采购订单ID
     */
    @Schema(name = "purchaseOrderId", description = "采购订单ID")
    private Long purchaseOrderId;

    /**
     * 采购订单编号
     */
    @Schema(name = "purchaseOrderNo", description = "采购订单编号")
    private String purchaseOrderNo;

    /**
     * 提交概览
     */
    @Schema(name = "submitSummary", description = "提交概览")
    private OrderSubmitSummary submitSummary;

    /**
     * 价格详情
     */
    @Schema(name = "priceDetails", description = "价格详情")
    private OrderPriceDetails priceDetails;

    /**
     * 订单提交概览
     */
    @Data
    @Builder
    public static class OrderSubmitSummary {

        /**
         * 总商品数量
         */
        @Schema(name = "totalQuantity", description = "总商品数量")
        private BigDecimal totalQuantity;

        /**
         * 商品种类数
         */
        @Schema(name = "productTypeCount", description = "商品种类数")
        private Integer productTypeCount;

        /**
         * 提交时间
         */
        @Schema(name = "submitTime", description = "提交时间")
        private LocalDateTime submitTime;
    }

    /**
     * 订单项结果
     */
    @Data
    @Builder
    public static class OrderItemResult {

        /**
         * SKU ID
         */
        @Schema(name = "skuId", description = "SKU ID")
        private Long skuId;

        /**
         * 商品标题
         */
        @Schema(name = "productTitle", description = "商品标题")
        private String productTitle;

        /**
         * 商品图片
         */
        @Schema(name = "productImage", description = "商品图片")
        private String productImage;

        /**
         * SKU规格
         */
        @Schema(name = "skuSpecs", description = "SKU规格")
        private String skuSpecs;

        /**
         * 订购数量
         */
        @Schema(name = "quantity", description = "订购数量")
        private Integer quantity;

        /**
         * 单价
         */
        @Schema(name = "unitPrice", description = "单价")
        private BigDecimal unitPrice;

        /**
         * 行总金额
         */
        @Schema(name = "lineTotalAmount", description = "行总金额")
        private BigDecimal lineTotalAmount;

        /**
         * 是否成功
         */
        @Schema(name = "success", description = "是否成功")
        private Boolean success;

        /**
         * 错误信息
         */
        @Schema(name = "errorMessage", description = "错误信息")
        private String errorMessage;
    }

    /**
     * 订单价格详情
     */
    @Data
    @Builder
    public static class OrderPriceDetails {

        /**
         * 商品总金额（人民币）
         */
        @Schema(name = "merchandiseAmount", description = "商品总金额（人民币）")
        private BigDecimal merchandiseAmount;

        /**
         * 商品总金额（美元）
         */
        @Schema(name = "merchandiseAmountUsd", description = "商品总金额（美元）")
        private BigDecimal merchandiseAmountUsd;

        /**
         * 运费总金额（人民币）
         */
        @Schema(name = "shippingAmount", description = "运费总金额（人民币）")
        private BigDecimal shippingAmount;

        /**
         * 运费总金额（美元）
         */
        @Schema(name = "shippingAmountUsd", description = "运费总金额（美元）")
        private BigDecimal shippingAmountUsd;

        /**
         * 服务费（人民币）
         */
        @Schema(name = "serviceFee", description = "服务费（人民币）")
        private BigDecimal serviceFee;

        /**
         * 服务费（美元）
         */
        @Schema(name = "serviceFeeUsd", description = "服务费（美元）")
        private BigDecimal serviceFeeUsd;

        /**
         * 订单总金额（人民币）
         */
        @Schema(name = "totalAmount", description = "订单总金额（人民币）")
        private BigDecimal totalAmount;

        /**
         * 订单总金额（美元）
         */
        @Schema(name = "totalAmountUsd", description = "订单总金额（美元）")
        private BigDecimal totalAmountUsd;
    }
}
