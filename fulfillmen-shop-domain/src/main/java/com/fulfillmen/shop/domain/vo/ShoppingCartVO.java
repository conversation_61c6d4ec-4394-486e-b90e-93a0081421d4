/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.vo;

import com.fulfillmen.shop.domain.entity.json.AttrJson;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 购物车VO
 *
 * <AUTHOR>
 * @date 2025/4/29 16:51
 * @description: todo
 * @since 1.0.0
 */
@Data
@Schema(description = "购物车VO")
public class ShoppingCartVO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 卖家 open id
     */
    @Schema(description = "卖家 open id")
    private String sellerOpenId;

    /**
     * 卖家名称
     */
    @Schema(description = "卖家名称")
    private String sellerName;

    /**
     * 产品 id
     */
    @Schema(description = "产品id")
    private Long productId;

    /**
     * 平台产品 ID (用于产品详情查询)
     */
    @Schema(description = "平台产品ID，用于产品详情查询")
    private String platformProductId;

    /**
     * 产品标题
     */
    @Schema(description = "产品标题")
    private String title;

    /**
     * 产品英文标题
     */
    @Schema(description = "产品英文标题")
    private String titleTrans;

    /**
     * 产品主图
     */
    @Schema(description = "产品主图")
    private String mainImgUrl;

    /**
     * sku id
     */
    @Schema(description = "sku id")
    private Long skuId;

    /**
     * sku 规格属性 默认 中文，包含多语言示例：{attr: [{尺寸: x},{颜色: 红}],{attrTransEn: [{size: x},{color: red}], attrTransIndia:[{মাপ: x }, {রঙ: লাল}]}
     */
    @Schema(description = "sku 规格属性 默认 中文，包含多语言示例：{attr: [{尺寸: x},{颜色: 红}],{attrTransEn: [{size: x},{color: red}], attrTransIndia:[{মাপ: x }, {রঙ: লাল}]}")
    private List<AttrJson> specs;

    /**
     * 单价
     */
    @Schema(description = "单价")
    private BigDecimal price;

    /**
     * 美元单价
     */
    @Schema(description = "美元单价")
    private BigDecimal usdPrice;

    /**
     * 购买数量 qty
     */
    @Schema(description = "购买数量 qty")
    private Integer quantity;

    /**
     * 最小起订量
     */
    @Schema(description = "最小起订量")
    private Integer minOrderQuantity;

    /**
     * 是否选中 0 否，1 是
     */
    @Schema(description = "是否选中 0 否，1 是")
    private Integer isChecked;

    /**
     * 总价
     */
    @Schema(description = "总价")
    private BigDecimal totalPrice;

    /**
     * 美元总价
     */
    @Schema(description = "美元总价")
    private BigDecimal usdTotalPrice;

}
