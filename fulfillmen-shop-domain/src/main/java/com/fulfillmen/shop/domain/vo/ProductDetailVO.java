/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 前端商品详情VO
 *
 * <p>
 * 该VO用于商品详情页面展示，支持多语言动态切换：
 * <ul>
 * <li>中文字段：优先展示中文内容</li>
 * <li>英文字段：提供英文翻译内容，如果英文不存在则回退到中文</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/11 11:05
 * @description: 前端商品详情信息VO，支持多语言动态切换，适用于商品详情页展示
 * @since 1.0.0
 */
@Data
@Schema(description = "商品详情信息")
public class ProductDetailVO {

    @Schema(description = "商品ID")
    private Long id;

    @Schema(description = "商品标题（中文）")
    private String title;

    @Schema(description = "商品标题（英文）")
    private String titleEn;

    @Schema(description = "类目ID")
    private Long categoryId;

    @Schema(description = "类目名称（中文）")
    private String categoryName;

    @Schema(description = "类目名称（英文）")
    private String categoryNameEn;

    @Schema(description = "商品描述（中文）")
    private String description;

    @Schema(description = "商品描述（英文）")
    private String descriptionEn;

    @Schema(description = "商品图片列表")
    private List<String> images;

    @Schema(description = "白底图")
    private String whiteImage;

    @Schema(description = "主视频")
    private String mainVideo;

    @Schema(description = "详情视频")
    private String detailVideo;

    @Schema(description = "商品单位（中文）")
    private String unit;

    @Schema(description = "商品单位（英文）")
    private String unitEn;

    @Schema(description = "商品（人民币）价格")
    private BigDecimal price;

    @Schema(description = "商品（美元）价格")
    private BigDecimal usdPrice;

    @Schema(description = "最小订购数量")
    private Integer minOrderQuantity;

    @Schema(description = "卖家数据信息")
    private SellerDataInfoVO sellerDataInfo;

    @Schema(description = "商品SKU列表")
    private List<ProductSkuInfoVO> productSkuInfos;

    @Schema(description = "商品属性列表")
    private List<ProductAttributeVO> productAttributes;

    @Schema(description = "商品销售信息")
    private ProductSaleInfoVO productSaleInfo;

    @Schema(description = "商品物流包裹信息")
    private ProductShippingInfoVO shippingInfo;

    @Data
    @Schema(description = "商品SKU信息")
    public static class ProductSkuInfoVO {

        @Schema(description = "SKU ID")
        private Long skuId;

        @Schema(description = "规格ID")
        private String specId;

        @Schema(description = "SKU库存数量")
        private Integer amountOnSale;

        @Schema(description = "SKU价格（一件代发包邮价格）")
        private BigDecimal price;

        @Schema(description = "SKU（美元）价格")
        private BigDecimal usdPrice;

        @Schema(description = "SKU图片")
        private String image;

        @Schema(description = "SKU规格属性列表")
        private List<ProductSkuSpecVO> specs;
    }

    @Data
    @Schema(description = "SKU规格属性")
    public static class ProductSkuSpecVO {

        /**
         * 属性ID
         */
        @Schema(description = "属性ID")
        private String attributeId;

        @Schema(description = "规格属性名称（中文）")
        private String specName;

        @Schema(description = "规格属性名称（英文）")
        private String specNameEn;

        @Schema(description = "规格属性值（中文）")
        private String specValue;

        @Schema(description = "规格属性值（英文）")
        private String specValueEn;
    }

    @Data
    @Schema(description = "商品属性")
    public static class ProductAttributeVO {

        @Schema(description = "属性ID")
        private String attributeId;

        @Schema(description = "属性名称（中文）")
        private String attributeName;

        @Schema(description = "属性名称（英文）")
        private String attributeNameEn;

        @Schema(description = "属性值（中文）")
        private String value;

        @Schema(description = "属性值（英文）")
        private String valueEn;
    }

    @Data
    @Schema(description = "商品销售信息")
    public static class ProductSaleInfoVO {

        @Schema(description = "商品库存")
        private Integer amountOnSale;

        @Schema(description = "商品（人民币）价格")
        private BigDecimal price;

        @Schema(description = "商品（美元）价格")
        private BigDecimal usdPrice;

        @Schema(description = "报价类型：0-无SKU按商品数量报价，1-按SKU规格报价，2-有SKU按商品数量报价")
        private Integer quoteType;

        @Schema(description = "商品单位（中文）")
        private String unit;

        @Schema(description = "商品单位（英文）")
        private String unitEn;

        @Schema(description = "最小订购数量")
        private Integer minOrderQuantity;

        @Schema(description = "月销量")
        private Integer monthSold;

        @Schema(description = "复购率百分比")
        private String repurchaseRate;
    }

    @Data
    @Schema(description = "商品物流包裹信息")
    public static class ProductShippingInfoVO {

        @Schema(description = "包装长度（cm）")
        private BigDecimal packageLength;

        @Schema(description = "包装宽度（cm）")
        private BigDecimal packageWidth;

        @Schema(description = "包装高度（cm）")
        private BigDecimal packageHeight;

        @Schema(description = "包装重量（kg）")
        private BigDecimal packageWeight;

        @Schema(description = "运输方式（中文）")
        private String shippingMethod;

        @Schema(description = "运输方式（英文）")
        private String shippingMethodEn;

        @Schema(description = "预计发货时间（天）")
        private Integer estimatedDeliveryDays;
    }
}
