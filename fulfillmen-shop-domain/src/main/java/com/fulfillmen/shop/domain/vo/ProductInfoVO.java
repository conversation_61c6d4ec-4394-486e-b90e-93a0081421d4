/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * ERP 前端 产品列表信息VO
 *
 * <p>
 * 该VO用于产品列表页面展示，根据传入的language参数动态填充对应语言的内容：
 * <ul>
 * <li>language=zh：返回中文内容</li>
 * <li>language=en：返回英文内容，如果英文不存在则回退到中文</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/3 20:15
 * @description: 产品列表信息对外输出VO，支持多语言动态切换，适用于产品搜索和列表展示
 * @since 1.0.0
 */
@Data
@Schema(description = "产品列表信息")
public class ProductInfoVO {

    @Schema(description = "商品ID")
    private Long id;

    @Schema(description = "商品标题 中文标题")
    private String name;

    @Schema(description = "商品标题（翻译后的）")
    private String nameTrans;

    @Schema(description = "商品主图URL")
    private String imageUrl;

    @Schema(description = "白底图URL")
    private String whiteImageUrl;

    @Schema(description = "商品（人民币）价格")
    private BigDecimal price;

    @Schema(description = "商品（美元）价格")
    private BigDecimal usdPrice;

    @Schema(description = "月销量")
    private Integer monthSold;

    @Schema(description = "最小起订量")
    private Integer minOrderQuantity;

    @Schema(description = "复购率百分比")
    private String repurchaseRate;

    @Schema(description = "一级分类ID")
    private Long topCategoryId;

    @Schema(description = "二级分类ID")
    private Long secondCategoryId;

    @Schema(description = "三级分类ID")
    private Long thirdCategoryId;

    @Schema(description = "分类名称（根据language参数返回对应语言）")
    private String categoryName;

    @Schema(description = "分类名称（英文）")
    private String categoryNameEn;

    @Schema(description = "是否支持一件代发。0-不支持，1-支持")
    private Integer isOnePsale;

    @Schema(description = "卖家基础信息")
    private SellerDataInfoVO sellerInfo;

}
