/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import com.fulfillmen.shop.domain.entity.json.AttrJson;

/**
 * 订单预览VO
 *
 * <AUTHOR>
 * @date 2025/6/24
 * @description 订单预览响应对象
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "OrderPreviewVO", description = "订单预览响应")
public class OrderPreviewVO {

    /**
     * 订单预览概览信息
     */
    @Schema(description = "订单预览概览信息")
    private OrderPreviewSummary orderPreviewSummary;

    /**
     * 商品预览列表（按产品分组）
     */
    @Schema(description = "商品预览列表")
    private List<ProductItemPreview> productItems;

    /**
     * 价格详情信息
     */
    @Schema(description = "价格详情信息")
    private PriceDetails priceDetails;

    /**
     * 预览过程中的错误信息列表
     */
    @Schema(description = "预览过程中的错误信息列表")
    private List<OrderPreviewError> errors;

    /**
     * 幂等令牌
     */
    @Schema(description = "订单提交幂等令牌")
    private String idempotentToken;

    /**
     * 令牌过期时间
     */
    @Schema(description = "令牌过期时间")
    private LocalDateTime tokenExpiryTime;

    /**
     * 预览创建时间
     */
    @Schema(description = "预览创建时间")
    private LocalDateTime createTime;

    /**
     * 是否来自购物车 0: 否 , 1: 是
     *
     * <pre>
     * 如果来自购物车订单，则需要将提交订单的商品，从购物车里删除。
     * </pre>
     */
    @Schema(description = "是否来自购物车 1: 是 0: 否")
    private Integer isShoppingCart;

    /**
     * 购物车ID 多个ID用逗号分隔，需要删除的购物车 Id 列表。如果 null 则不删除。
     */
    @Schema(description = "购物车ID", example = "1,2,3")
    private String shoppingCartIds;

    /**
     * 订单预览概览信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "OrderPreviewSummary", description = "订单预览概览信息")
    public static class OrderPreviewSummary {

        /**
         * 商品总数量
         */
        @Schema(description = "商品总数量")
        private Integer totalQuantity;

        /**
         * 商品种类数量
         */
        @Schema(description = "商品种类数量")
        private Integer productTypeCount;

        /**
         * 预览状态（是否成功）
         */
        @Schema(description = "预览状态")
        private Boolean success;

        /**
         * 是否有错误
         */
        @Schema(description = "是否有错误")
        private Boolean hasErrors;
    }

    /**
     * 商品预览项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "ProductItemPreview", description = "商品预览项")
    public static class ProductItemPreview {

        /**
         * 商品ID SpuId
         */
        @Schema(description = "商品ID SpuId")
        private Long spuId;

        /**
         * 商品规格ID
         */
        @Schema(description = "商品规格ID")
        private String specId;

        /**
         * SKU ID
         */
        @Schema(description = "SKU ID")
        private Long skuId;

        /**
         * 商品标题
         */
        @Schema(description = "商品标题")
        private String productTitle;

        /**
         * 商品标题(英文)
         */
        @Schema(description = "商品标题(英文)")
        private String productTitleEn;

        /**
         * 商品图片URL
         */
        @Schema(description = "商品图片URL")
        private String productImageUrl;

        /**
         * sku图片
         */
        @Schema(description = "sku图片")
        private String skuImage;

        /**
         * 商品规格属性
         */
        @Schema(description = "商品规格属性")
        private List<AttrJson> skuSpecs;

        /**
         * 单价(元)
         */
        @Schema(description = "单价(元)")
        private BigDecimal unitPrice;

        /**
         * 单价(美元)
         */
        @Schema(description = "单价(美元)")
        private BigDecimal unitPriceUsd;

        /**
         * 订购数量
         */
        @Schema(description = "订购数量")
        private Integer orderedQuantity;

        /**
         * 行总金额(元)
         */
        @Schema(description = "行总金额(元)")
        private BigDecimal lineTotalAmount;

        /**
         * 行总金额(美元)
         */
        @Schema(description = "行总金额(美元)")
        private BigDecimal lineTotalAmountUsd;

        /**
         * 计量单位
         */
        @Schema(description = "计量单位")
        private String unitOfMeasure;

        /**
         * 预览状态
         */
        @Schema(description = "预览状态")
        private Boolean available;

        /**
         * 状态消息
         */
        @Schema(description = "状态消息")
        private String message;
    }

    /**
     * 价格详情信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "PriceDetails", description = "价格详情信息")
    public static class PriceDetails {

        /**
         * 商品总金额(元)
         */
        @Schema(description = "商品总金额(元)")
        private BigDecimal merchandiseAmount;

        /**
         * 商品总金额(美元)
         */
        @Schema(description = "商品总金额(美元)")
        private BigDecimal merchandiseAmountUsd;

        /**
         * 运费(元)
         */
        @Schema(description = "运费(元)")
        private BigDecimal shippingAmount;

        /**
         * 运费(美元)
         */
        @Schema(description = "运费(美元)")
        private BigDecimal shippingAmountUsd;

        /**
         * 服务费(元)
         */
        @Schema(description = "服务费(元)")
        private BigDecimal serviceFee;

        /**
         * 服务费(美元)
         */
        @Schema(description = "服务费(美元)")
        private BigDecimal serviceFeeUsd;

        /**
         * 服务费费率
         */
        @Schema(description = "服务费费率")
        private BigDecimal serviceFeeRate;

        /**
         * 订单总金额(元)
         */
        @Schema(description = "订单总金额(元)")
        private BigDecimal totalAmount;

        /**
         * 订单总金额(美元)
         */
        @Schema(description = "订单总金额(美元)")
        private BigDecimal totalAmountUsd;
    }

    /**
     * 订单预览错误信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "OrderPreviewError", description = "订单预览错误信息")
    public static class OrderPreviewError {

        /**
         * 错误码
         */
        @Schema(description = "错误码")
        private String errorCode;

        /**
         * 错误消息
         */
        @Schema(description = "错误消息")
        private String errorMessage;

        /**
         * 错误相关的商品ID（可选）
         */
        @Schema(description = "错误相关的商品ID")
        private Long offerId;

        /**
         * 错误相关的规格ID（可选）
         */
        @Schema(description = "错误相关的规格ID")
        private String specId;

        /**
         * 错误类型
         */
        @Schema(description = "错误类型")
        private String errorType;
    }
}
