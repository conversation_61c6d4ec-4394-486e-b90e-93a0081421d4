<?xml version="1.0" encoding="UTF-8"?>
<!--
    Maven 格式化插件配置示例
    用于集成 FulfillmenJavaStyle.xml 到 Maven 构建流程中
-->

<!-- 在 pom.xml 的 <build><plugins> 部分添加以下配置 -->

<!-- 1. 代码格式化插件 -->
<plugin>
    <groupId>net.revelc.code.formatter</groupId>
    <artifactId>formatter-maven-plugin</artifactId>
    <version>2.23.0</version>
    <configuration>
        <!-- 指定格式化配置文件路径 -->
        <configFile>${project.basedir}/build-tools/src/main/resources/config/FulfillmenJavaStyle.xml</configFile>
        <!-- 行结束符设置 -->
        <lineEnding>LF</lineEnding>
        <!-- 文件编码 -->
        <encoding>UTF-8</encoding>
        <!-- 包含的文件模式 -->
        <includes>
            <include>src/main/java/**/*.java</include>
            <include>src/test/java/**/*.java</include>
        </includes>
        <!-- 排除的文件模式 -->
        <excludes>
            <exclude>src/main/java/**/generated/**/*.java</exclude>
            <exclude>target/**</exclude>
        </excludes>
        <!-- 跳过格式化的标记 -->
        <skipFormatting>false</skipFormatting>
        <!-- 验证模式：如果代码格式不正确则构建失败 -->
        <failOnViolation>true</failOnViolation>
    </configuration>
    <executions>
        <!-- 在 validate 阶段自动检查格式 -->
        <execution>
            <id>validate-format</id>
            <phase>validate</phase>
            <goals>
                <goal>validate</goal>
            </goals>
        </execution>
        <!-- 在 process-sources 阶段自动格式化（可选） -->
        <!--
        <execution>
            <id>format-sources</id>
            <phase>process-sources</phase>
            <goals>
                <goal>format</goal>
            </goals>
        </execution>
        -->
    </executions>
</plugin>

<!-- 2. Checkstyle 插件（可选，用于更严格的代码风格检查） -->
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-checkstyle-plugin</artifactId>
    <version>3.3.0</version>
    <configuration>
        <!-- Checkstyle 配置文件 -->
        <configLocation>build-tools/src/main/resources/config/checkstyle.xml</configLocation>
        <!-- 编码设置 -->
        <encoding>UTF-8</encoding>
        <!-- 控制台输出 -->
        <consoleOutput>true</consoleOutput>
        <!-- 构建失败条件 -->
        <failsOnError>true</failsOnError>
        <!-- 链接到源码 -->
        <linkXRef>false</linkXRef>
        <!-- 包含的文件 -->
        <includeTestSourceDirectory>true</includeTestSourceDirectory>
        <!-- 排除的文件 -->
        <excludes>**/generated/**/*</excludes>
    </configuration>
    <executions>
        <execution>
            <id>validate</id>
            <phase>validate</phase>
            <goals>
                <goal>check</goal>
            </goals>
        </execution>
    </executions>
</plugin>

<!-- 3. SpotBugs 插件（可选，用于代码质量检查） -->
<plugin>
    <groupId>com.github.spotbugs</groupId>
    <artifactId>spotbugs-maven-plugin</artifactId>
    <version>4.7.3.6</version>
    <configuration>
        <!-- 努力级别 -->
        <effort>Max</effort>
        <!-- 阈值 -->
        <threshold>Low</threshold>
        <!-- XML 报告 -->
        <xmlOutput>true</xmlOutput>
        <!-- 排除文件 -->
        <excludeFilterFile>build-tools/src/main/resources/config/spotbugs-exclude.xml</excludeFilterFile>
    </configuration>
    <executions>
        <execution>
            <goals>
                <goal>check</goal>
            </goals>
        </execution>
    </executions>
</plugin>

<!-- 4. Maven Enforcer 插件（确保构建环境一致性） -->
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-enforcer-plugin</artifactId>
    <version>3.4.1</version>
    <executions>
        <execution>
            <id>enforce-versions</id>
            <goals>
                <goal>enforce</goal>
            </goals>
            <configuration>
                <rules>
                    <!-- 要求最低 Maven 版本 -->
                    <requireMavenVersion>
                        <version>3.6.0</version>
                    </requireMavenVersion>
                    <!-- 要求最低 Java 版本 -->
                    <requireJavaVersion>
                        <version>11</version>
                    </requireJavaVersion>
                    <!-- 禁止重复依赖 -->
                    <banDuplicatePomDependencyVersions/>
                    <!-- 要求插件版本 -->
                    <requirePluginVersions>
                        <message>Best Practice is to always define plugin versions!</message>
                        <banLatest>true</banLatest>
                        <banRelease>true</banRelease>
                    </requirePluginVersions>
                </rules>
            </configuration>
        </execution>
    </executions>
</plugin>

<!-- 
使用方法：

1. 检查代码格式：
   mvn formatter:validate

2. 自动格式化代码：
   mvn formatter:format

3. 运行完整的代码质量检查：
   mvn clean validate compile

4. 生成代码质量报告：
   mvn site

5. 跳过格式化检查（不推荐）：
   mvn compile -Dformatter.skip=true

6. 只格式化特定文件：
   mvn formatter:format -Dformatter.includes=src/main/java/com/example/MyClass.java

常用 Maven 命令组合：
- mvn clean formatter:format compile test
- mvn clean validate
- mvn formatter:validate checkstyle:check spotbugs:check
-->
