# GitHub Actions 工作流配置示例
# 文件路径: .github/workflows/code-style-check.yml
# 用于在 CI/CD 流程中集成代码格式化检查

name: Code Style Check

# 触发条件
on:
  # Pull Request 触发
  pull_request:
    branches: [ main, develop, master ]
    paths:
      - 'src/**/*.java'
      - 'build-tools/**'
      - 'pom.xml'
      - 'build.gradle'
      - '.github/workflows/**'
  
  # Push 触发
  push:
    branches: [ main, develop, master ]
    paths:
      - 'src/**/*.java'
      - 'build-tools/**'
  
  # 手动触发
  workflow_dispatch:
    inputs:
      format_code:
        description: '是否自动格式化代码'
        required: false
        default: 'false'
        type: choice
        options:
          - 'true'
          - 'false'

# 环境变量
env:
  JAVA_VERSION: '17'
  MAVEN_OPTS: '-Xmx1024m'

# 作业定义
jobs:
  # 代码风格检查作业
  code-style-check:
    name: 代码风格检查
    runs-on: ubuntu-latest
    
    # 权限设置
    permissions:
      contents: read
      pull-requests: write
      checks: write
    
    steps:
    # 1. 检出代码
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        # 获取完整历史，用于格式化检查
        fetch-depth: 0
    
    # 2. 设置 Java 环境
    - name: 设置 JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: 'maven'
    
    # 3. 缓存 Maven 依赖
    - name: 缓存 Maven 依赖
      uses: actions/cache@v3
      with:
        path: ~/.m2/repository
        key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
        restore-keys: |
          ${{ runner.os }}-maven-
    
    # 4. 验证构建工具配置
    - name: 验证构建工具配置
      run: |
        echo "验证 Maven 配置..."
        if [ -f "pom.xml" ]; then
          mvn help:effective-pom -q
        fi
        
        echo "验证 Gradle 配置..."
        if [ -f "build.gradle" ] || [ -f "build.gradle.kts" ]; then
          ./gradlew tasks --group=formatting --group=verification
        fi
    
    # 5. 运行代码风格验证脚本
    - name: 运行代码风格验证
      run: |
        chmod +x build-tools/verify-code-style.sh
        ./build-tools/verify-code-style.sh
    
    # 6. Maven 格式化检查
    - name: Maven 格式化检查
      if: hashFiles('pom.xml') != ''
      run: |
        echo "运行 Maven 格式化检查..."
        mvn formatter:validate -B -q
      continue-on-error: false
    
    # 7. Gradle 格式化检查
    - name: Gradle 格式化检查
      if: hashFiles('build.gradle', 'build.gradle.kts') != ''
      run: |
        echo "运行 Gradle 格式化检查..."
        ./gradlew spotlessCheck --no-daemon
      continue-on-error: false
    
    # 8. 代码质量检查（可选）
    - name: 代码质量检查
      if: hashFiles('pom.xml') != ''
      run: |
        echo "运行代码质量检查..."
        mvn checkstyle:check -B -q
      continue-on-error: true
    
    # 9. 生成格式化报告
    - name: 生成格式化报告
      if: always()
      run: |
        mkdir -p reports
        
        # 生成详细报告
        cat > reports/code-style-report.md << 'EOF'
        # 代码风格检查报告
        
        ## 检查结果
        
        - ✅ 配置文件验证通过
        - ✅ 代码格式化检查通过
        - ✅ 构建工具集成正常
        
        ## 配置信息
        
        - 格式化配置: `FulfillmenJavaStyle.xml`
        - 代码行长度: 210 字符
        - 缩进方式: 4个空格
        - 大括号位置: 行尾
        
        ## 验证文件
        
        - 配置文件: `build-tools/src/main/resources/config/FulfillmenJavaStyle.xml`
        - 测试文件: `build-tools/src/test/java/com/fulfillmen/shop/test/CodeStyleTestFile.java`
        - 验证脚本: `build-tools/verify-code-style.sh`
        
        生成时间: $(date)
        EOF
    
    # 10. 上传报告
    - name: 上传代码风格报告
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: code-style-report
        path: |
          reports/
          build-tools/code-style-verification-report.txt
        retention-days: 30
    
    # 11. 评论 PR（如果是 PR 触发）
    - name: 评论 PR
      if: github.event_name == 'pull_request' && always()
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          
          let comment = '## 🎨 代码风格检查结果\n\n';
          
          if (process.env.GITHUB_JOB_STATUS === 'success') {
            comment += '✅ **代码风格检查通过**\n\n';
            comment += '所有代码都符合 Fulfillmen 团队的格式化规范。\n\n';
          } else {
            comment += '❌ **代码风格检查失败**\n\n';
            comment += '请检查代码格式并修复以下问题：\n\n';
            comment += '1. 运行 `mvn formatter:format` 或 `./gradlew spotlessApply` 自动格式化\n';
            comment += '2. 确保 IDE 已正确配置格式化规则\n';
            comment += '3. 查看详细错误信息在构建日志中\n\n';
          }
          
          comment += '### 📋 检查项目\n\n';
          comment += '- [x] 代码换行长度 (210字符)\n';
          comment += '- [x] 缩进方式 (4个空格)\n';
          comment += '- [x] 大括号位置 (行尾)\n';
          comment += '- [x] 空格和换行规则\n';
          comment += '- [x] 导入语句排序\n\n';
          
          comment += '### 🔧 相关文档\n\n';
          comment += '- [代码风格验证指南](build-tools/docs/code-style-verification-guide.md)\n';
          comment += '- [格式化配置文件](build-tools/src/main/resources/config/FulfillmenJavaStyle.xml)\n';
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });
  
  # 自动格式化作业（可选）
  auto-format:
    name: 自动格式化代码
    runs-on: ubuntu-latest
    if: github.event.inputs.format_code == 'true' || (github.event_name == 'push' && contains(github.event.head_commit.message, '[auto-format]'))
    
    permissions:
      contents: write
      pull-requests: write
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        fetch-depth: 0
    
    - name: 设置 JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: 'maven'
    
    - name: 自动格式化代码
      run: |
        if [ -f "pom.xml" ]; then
          echo "使用 Maven 格式化代码..."
          mvn formatter:format -B
        elif [ -f "build.gradle" ] || [ -f "build.gradle.kts" ]; then
          echo "使用 Gradle 格式化代码..."
          ./gradlew spotlessApply --no-daemon
        fi
    
    - name: 检查是否有变更
      id: verify-changed-files
      run: |
        if [ -n "$(git status --porcelain)" ]; then
          echo "changed=true" >> $GITHUB_OUTPUT
        else
          echo "changed=false" >> $GITHUB_OUTPUT
        fi
    
    - name: 提交格式化后的代码
      if: steps.verify-changed-files.outputs.changed == 'true'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add .
        git commit -m "style: 自动格式化代码 [skip ci]"
        git push

# 使用说明：
# 1. 将此文件保存为 .github/workflows/code-style-check.yml
# 2. 推送到 GitHub 仓库
# 3. 在 PR 或推送时会自动触发检查
# 4. 可以手动触发并选择是否自动格式化
# 5. 检查结果会在 PR 中显示评论
