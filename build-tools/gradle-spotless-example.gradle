// Gradle Spotless 插件配置示例
// 用于集成 FulfillmenJavaStyle.xml 到 Gradle 构建流程中

plugins {
    id 'java'
    id 'com.diffplug.spotless' version '6.22.0'
    id 'checkstyle'
    id 'pmd'
    id 'com.github.spotbugs' version '5.0.14'
}

// Spotless 配置 - 代码格式化
spotless {
    // Java 代码格式化
    java {
        // 使用 Eclipse 格式化配置
        eclipse().configFile('build-tools/src/main/resources/config/FulfillmenJavaStyle.xml')
        
        // 文件编码
        encoding 'UTF-8'
        
        // 行结束符
        lineEndings 'UNIX'
        
        // 包含的文件
        target 'src/main/java/**/*.java', 'src/test/java/**/*.java'
        
        // 排除的文件
        targetExclude 'src/main/java/**/generated/**/*.java'
        
        // 移除未使用的导入
        removeUnusedImports()
        
        // 导入排序
        importOrder('java', 'javax', 'org', 'com', '')
        
        // 去除尾随空白
        trimTrailingWhitespace()
        
        // 文件末尾换行
        endWithNewline()
        
        // 自定义格式化步骤
        custom 'Remove empty lines', { content ->
            // 移除多余的空行（保留最多2个连续空行）
            content.replaceAll(/\n{4,}/, '\n\n\n')
        }
        
        // 许可证头部（可选）
        licenseHeader '''/*
 * Copyright (c) 2024 Fulfillmen Team
 * All rights reserved.
 */'''
    }
    
    // Kotlin 代码格式化（如果项目包含 Kotlin）
    kotlin {
        ktlint('0.50.0')
        target 'src/**/*.kt'
        targetExclude 'build/**/*.kt'
    }
    
    // XML 文件格式化
    format 'xml', {
        target 'src/**/*.xml', 'pom.xml', 'build.gradle'
        eclipseWtp('xml')
        trimTrailingWhitespace()
        endWithNewline()
    }
    
    // JSON 文件格式化
    format 'json', {
        target 'src/**/*.json'
        gson()
        trimTrailingWhitespace()
        endWithNewline()
    }
    
    // YAML 文件格式化
    format 'yaml', {
        target 'src/**/*.yml', 'src/**/*.yaml'
        prettier()
        trimTrailingWhitespace()
        endWithNewline()
    }
    
    // Markdown 文件格式化
    format 'markdown', {
        target '**/*.md'
        prettier()
        trimTrailingWhitespace()
        endWithNewline()
    }
}

// Checkstyle 配置
checkstyle {
    toolVersion = '10.12.4'
    configFile = file('build-tools/src/main/resources/config/checkstyle.xml')
    configProperties = [
        'checkstyle.cache.file': "${buildDir}/checkstyle.cache"
    ]
    ignoreFailures = false
    maxWarnings = 0
    maxErrors = 0
}

// PMD 配置
pmd {
    toolVersion = '6.55.0'
    ruleSetFiles = files('build-tools/src/main/resources/config/pmd-ruleset.xml')
    ignoreFailures = false
    consoleOutput = true
}

// SpotBugs 配置
spotbugs {
    toolVersion = '4.7.3'
    effort = 'max'
    reportLevel = 'low'
    excludeFilter = file('build-tools/src/main/resources/config/spotbugs-exclude.xml')
}

// 自定义任务：代码质量检查
task codeQualityCheck {
    group = 'verification'
    description = '运行所有代码质量检查'
    dependsOn 'spotlessCheck', 'checkstyleMain', 'checkstyleTest', 'pmdMain', 'pmdTest', 'spotbugsMain', 'spotbugsTest'
}

// 自定义任务：代码格式化
task formatCode {
    group = 'formatting'
    description = '格式化所有代码文件'
    dependsOn 'spotlessApply'
}

// 自定义任务：验证代码风格
task verifyCodeStyle {
    group = 'verification'
    description = '验证代码风格配置'
    doLast {
        // 执行验证脚本
        exec {
            commandLine 'bash', 'build-tools/verify-code-style.sh'
        }
    }
}

// 构建时自动检查代码格式
check.dependsOn spotlessCheck

// 编译前自动格式化（可选，谨慎使用）
// compileJava.dependsOn spotlessApply

// 配置报告输出
tasks.withType(Checkstyle) {
    reports {
        xml.required = true
        html.required = true
        html.outputLocation = file("$buildDir/reports/checkstyle/${name}.html")
    }
}

tasks.withType(Pmd) {
    reports {
        xml.required = true
        html.required = true
        html.outputLocation = file("$buildDir/reports/pmd/${name}.html")
    }
}

tasks.withType(com.github.spotbugs.snom.SpotBugsTask) {
    reports {
        xml.required = true
        html.required = true
        html.outputLocation = file("$buildDir/reports/spotbugs/${name}.html")
    }
}

// 自定义任务：生成代码质量报告
task generateQualityReport {
    group = 'reporting'
    description = '生成综合代码质量报告'
    dependsOn 'codeQualityCheck'
    
    doLast {
        def reportDir = file("$buildDir/reports/quality")
        reportDir.mkdirs()
        
        def reportFile = new File(reportDir, 'quality-summary.html')
        reportFile.text = """
<!DOCTYPE html>
<html>
<head>
    <title>代码质量报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .link { color: #0066cc; text-decoration: none; }
        .link:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Fulfillmen 项目代码质量报告</h1>
        <p>生成时间: ${new Date()}</p>
    </div>
    
    <div class="section">
        <h2>报告链接</h2>
        <ul>
            <li><a href="../checkstyle/main.html" class="link">Checkstyle 主代码检查报告</a></li>
            <li><a href="../checkstyle/test.html" class="link">Checkstyle 测试代码检查报告</a></li>
            <li><a href="../pmd/main.html" class="link">PMD 主代码分析报告</a></li>
            <li><a href="../pmd/test.html" class="link">PMD 测试代码分析报告</a></li>
            <li><a href="../spotbugs/main.html" class="link">SpotBugs 主代码检查报告</a></li>
            <li><a href="../spotbugs/test.html" class="link">SpotBugs 测试代码检查报告</a></li>
        </ul>
    </div>
    
    <div class="section">
        <h2>使用说明</h2>
        <p>请点击上方链接查看详细的代码质量分析报告。</p>
        <p>如发现问题，请及时修复并重新运行质量检查。</p>
    </div>
</body>
</html>
        """
        
        println "代码质量报告已生成: ${reportFile.absolutePath}"
    }
}

/*
使用方法：

1. 检查代码格式：
   ./gradlew spotlessCheck

2. 自动格式化代码：
   ./gradlew spotlessApply

3. 运行所有代码质量检查：
   ./gradlew codeQualityCheck

4. 格式化代码：
   ./gradlew formatCode

5. 验证代码风格配置：
   ./gradlew verifyCodeStyle

6. 生成质量报告：
   ./gradlew generateQualityReport

7. 运行完整构建（包含格式检查）：
   ./gradlew clean build

8. 只检查主代码的格式：
   ./gradlew spotlessJavaCheck

9. 只格式化特定类型的文件：
   ./gradlew spotlessXmlApply

常用命令组合：
- ./gradlew clean formatCode build
- ./gradlew spotlessCheck codeQualityCheck
- ./gradlew spotlessApply test
*/
