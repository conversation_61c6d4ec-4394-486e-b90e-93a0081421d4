#!/bin/bash

# FulfillmenJavaStyle.xml 验证脚本
# 用于验证代码格式化配置文件是否正确设置

set -e

echo "🚀 开始验证 FulfillmenJavaStyle.xml 配置文件..."
echo "=================================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_file_exists() {
    local file_path=$1
    local description=$2

    if [ -f "$file_path" ]; then
        echo -e "${GREEN}✅ $description 存在${NC}"
        return 0
    else
        echo -e "${RED}❌ $description 不存在: $file_path${NC}"
        return 1
    fi
}

check_config_setting() {
    local config_file=$1
    local pattern=$2
    local description=$3

    if grep -q "$pattern" "$config_file"; then
        echo -e "${GREEN}✅ $description${NC}"
        return 0
    else
        echo -e "${RED}❌ $description${NC}"
        return 1
    fi
}

# 文件路径定义
CONFIG_FILE="build-tools/src/main/resources/config/FulfillmenJavaStyle.xml"
TEST_FILE="build-tools/src/test/java/com/fulfillmen/shop/test/CodeStyleTestFile.java"
DOCS_FILE="build-tools/docs/code-style-verification-guide.md"

# 1. 检查文件是否存在
echo -e "${BLUE}📁 检查必要文件...${NC}"
check_file_exists "$CONFIG_FILE" "格式化配置文件"
check_file_exists "$TEST_FILE" "测试文件"
check_file_exists "$DOCS_FILE" "验证指南文档"

echo ""

# 2. 验证配置文件关键设置
echo -e "${BLUE}⚙️  验证配置文件关键设置...${NC}"

# 检查换行长度设置
check_config_setting "$CONFIG_FILE" 'lineSplit.*value="210"' "代码换行长度设置正确 (210字符)"

# 检查注释换行长度设置
check_config_setting "$CONFIG_FILE" 'comment\.line_length.*value="210"' "注释换行长度设置正确 (210字符)"

# 检查缩进设置
check_config_setting "$CONFIG_FILE" 'indentation\.size.*value="4"' "缩进大小设置正确 (4个空格)"

# 检查制表符设置
check_config_setting "$CONFIG_FILE" 'tabulation\.char.*value="space"' "制表符设置正确 (使用空格)"

# 检查大括号位置设置
check_config_setting "$CONFIG_FILE" 'brace_position_for_type_declaration.*value="end_of_line"' "类声明大括号位置正确 (行尾)"

# 检查方法大括号位置设置
check_config_setting "$CONFIG_FILE" 'brace_position_for_method_declaration.*value="end_of_line"' "方法声明大括号位置正确 (行尾)"

echo ""

# 3. 检查配置文件结构
echo -e "${BLUE}📋 检查配置文件结构...${NC}"

# 检查配置文件是否包含必要的注释
if grep -q "Fulfillmen Java代码格式化配置文件" "$CONFIG_FILE"; then
    echo -e "${GREEN}✅ 配置文件包含中文注释说明${NC}"
else
    echo -e "${YELLOW}⚠️  配置文件缺少中文注释说明${NC}"
fi

# 检查配置文件是否包含分组注释
if grep -q "========== 缩进和制表符配置 ==========" "$CONFIG_FILE"; then
    echo -e "${GREEN}✅ 配置文件包含分组注释${NC}"
else
    echo -e "${YELLOW}⚠️  配置文件缺少分组注释${NC}"
fi

# 检查配置文件格式
if grep -q 'profile.*name="FulfillmenGoogleStyle"' "$CONFIG_FILE"; then
    echo -e "${GREEN}✅ 配置文件名称正确 (FulfillmenGoogleStyle)${NC}"
else
    echo -e "${RED}❌ 配置文件名称错误${NC}"
fi

echo ""

# 4. 验证测试文件内容
echo -e "${BLUE}🧪 验证测试文件内容...${NC}"

# 检查测试文件是否包含各种Java语法结构
test_structures=(
    "enum Status" "枚举定义"
    "record OrderInfo" "Record定义"
    "public.*throws.*Exception" "异常声明"
    "stream()" "Stream API"
    "switch.*case" "Switch语句"
    "@Override" "注解使用"
    "try.*catch.*finally" "异常处理"
    "for.*:" "For-each循环"
    "while.*{" "While循环"
    "if.*else" "If-else语句"
)

for ((i=0; i<${#test_structures[@]}; i+=2)); do
    pattern="${test_structures[i]}"
    description="${test_structures[i+1]}"

    if grep -q "$pattern" "$TEST_FILE"; then
        echo -e "${GREEN}✅ 测试文件包含 $description${NC}"
    else
        echo -e "${YELLOW}⚠️  测试文件缺少 $description${NC}"
    fi
done

echo ""

# 5. 生成验证报告
echo -e "${BLUE}📊 生成验证报告...${NC}"

REPORT_FILE="build-tools/code-style-verification-report.txt"
{
    echo "FulfillmenJavaStyle.xml 验证报告"
    echo "生成时间: $(date)"
    echo "========================================"
    echo ""
    echo "配置文件路径: $CONFIG_FILE"
    echo "测试文件路径: $TEST_FILE"
    echo ""
    echo "关键配置验证:"
    echo "- 代码换行长度: 210字符"
    echo "- 注释换行长度: 210字符"
    echo "- 缩进大小: 4个空格"
    echo "- 制表符: 使用空格"
    echo "- 大括号位置: 行尾"
    echo ""
    echo "配置文件统计:"
    echo "- 总行数: $(wc -l < "$CONFIG_FILE")"
    echo "- 配置项数量: $(grep -c '<setting' "$CONFIG_FILE")"
    echo "- 注释行数: $(grep -c '<!--' "$CONFIG_FILE")"
    echo ""
    echo "验证完成时间: $(date)"
} > "$REPORT_FILE"

echo -e "${GREEN}✅ 验证报告已生成: $REPORT_FILE${NC}"

echo ""

# 6. 提供后续操作建议
echo -e "${BLUE}💡 后续操作建议:${NC}"
echo "1. 在IDE中导入配置文件并验证设置"
echo "2. 使用测试文件验证格式化效果"
echo "3. 配置Maven/Gradle插件进行自动化检查"
echo "4. 在CI/CD流程中集成格式化验证"
echo "5. 查看详细验证指南: $DOCS_FILE"

echo ""
echo -e "${GREEN}🎉 验证脚本执行完成！${NC}"
echo "=================================================="

# 返回适当的退出码
if [ $? -eq 0 ]; then
    exit 0
else
    exit 1
fi
