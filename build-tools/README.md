# Fulfillmen Java 代码风格配置

本目录包含 Fulfillmen 团队的 Java 代码格式化配置文件和相关工具，确保团队代码风格的一致性。

## 📁 目录结构

```
build-tools/
├── src/
│   ├── main/resources/config/
│   │   └── FulfillmenJavaStyle.xml          # 主要格式化配置文件
│   └── test/java/com/fulfillmen/shop/test/
│       └── CodeStyleTestFile.java           # 格式化测试文件
├── docs/
│   └── code-style-verification-guide.md     # 详细验证指南
├── verify-code-style.sh                     # 自动验证脚本
├── maven-formatter-plugin-example.xml       # Maven 插件配置示例
├── gradle-spotless-example.gradle           # Gradle 插件配置示例
├── github-actions-code-style.yml            # GitHub Actions 配置示例
└── README.md                                # 本文件
```

## 🚀 快速开始

### 1. 验证配置

运行自动验证脚本：

```bash
./build-tools/verify-code-style.sh
```

### 2. IDE 配置

#### Eclipse
1. `Window` → `Preferences` → `Java` → `Code Style` → `Formatter`
2. 点击 `Import...` 选择 `build-tools/src/main/resources/config/FulfillmenJavaStyle.xml`
3. 选择 `FulfillmenGoogleStyle` 配置

#### IntelliJ IDEA
1. `File` → `Settings` → `Editor` → `Code Style` → `Java`
2. 点击齿轮图标 → `Import Scheme` → `Eclipse XML Profile`
3. 选择 `build-tools/src/main/resources/config/FulfillmenJavaStyle.xml`

#### VS Code
在项目根目录创建 `.vscode/settings.json`：
```json
{
    "java.format.settings.url": "./build-tools/src/main/resources/config/FulfillmenJavaStyle.xml",
    "java.format.settings.profile": "FulfillmenGoogleStyle"
}
```

### 3. 构建工具集成

#### Maven
在 `pom.xml` 中添加：
```xml
<plugin>
    <groupId>net.revelc.code.formatter</groupId>
    <artifactId>formatter-maven-plugin</artifactId>
    <version>2.23.0</version>
    <configuration>
        <configFile>${project.basedir}/build-tools/src/main/resources/config/FulfillmenJavaStyle.xml</configFile>
    </configuration>
</plugin>
```

#### Gradle
在 `build.gradle` 中添加：
```gradle
plugins {
    id 'com.diffplug.spotless' version '6.22.0'
}

spotless {
    java {
        eclipse().configFile('build-tools/src/main/resources/config/FulfillmenJavaStyle.xml')
    }
}
```

## ⚙️ 配置详情

### 核心设置
- **代码行长度**: 210 字符
- **缩进方式**: 4 个空格（不使用制表符）
- **大括号位置**: 行尾
- **换行符**: Unix 风格 (LF)
- **编码**: UTF-8

### 格式化规则
- 操作符前后插入空格
- 逗号后插入空格
- 关键字后插入空格
- 方法调用括号前不插入空格
- 自动排序导入语句
- 移除未使用的导入

## 🧪 测试验证

### 使用测试文件
1. 打开 `build-tools/src/test/java/com/fulfillmen/shop/test/CodeStyleTestFile.java`
2. 故意破坏格式（添加不规范的空格、缩进）
3. 应用格式化快捷键验证效果

### 验证清单
- [ ] 缩进使用 4 个空格
- [ ] 长行在 210 字符处换行
- [ ] 大括号在行尾
- [ ] 操作符前后有空格
- [ ] 空行数量符合规范

## 🔧 常用命令

```bash
# 验证配置
./build-tools/verify-code-style.sh

# Maven 格式化检查
mvn formatter:validate

# Maven 自动格式化
mvn formatter:format

# Gradle 格式化检查
./gradlew spotlessCheck

# Gradle 自动格式化
./gradlew spotlessApply
```

## 📋 CI/CD 集成

### GitHub Actions
将 `build-tools/github-actions-code-style.yml` 复制到 `.github/workflows/` 目录。

### GitLab CI
```yaml
code-style-check:
  stage: test
  script:
    - ./build-tools/verify-code-style.sh
    - mvn formatter:validate
```

## 🛠️ 故障排除

| 问题 | 解决方案 |
|------|----------|
| 格式化不生效 | 重启 IDE，重新导入配置 |
| 换行长度错误 | 检查 IDE 的换行设置 |
| 缩进不一致 | 全选代码重新格式化 |
| 团队格式不统一 | 同步最新配置文件 |

## 📚 相关文档

- [详细验证指南](docs/code-style-verification-guide.md)
- [Maven 插件配置示例](maven-formatter-plugin-example.xml)
- [Gradle 插件配置示例](gradle-spotless-example.gradle)
- [GitHub Actions 配置示例](github-actions-code-style.yml)

## 🤝 贡献指南

1. 修改配置文件后运行验证脚本
2. 更新相关文档
3. 通知团队成员同步配置
4. 在 PR 中包含格式化检查

## 📞 支持

如有问题或建议，请联系 Fulfillmen 开发团队。

---

**注意**: 请确保所有团队成员使用相同的配置文件版本，以保持代码风格的一致性。
