# 构建阶段
FROM crpi-irb3zn66qvxjqsx0.cn-shenzhen.personal.cr.aliyuncs.com/fulfillmen/maven:3.9-eclipse-temurin-21 AS builder
WORKDIR /workspace

# 复制整个项目目录（除了.dockerignore中排除的文件）
COPY . .

# 提取分层
ARG JAR_FILE=fulfillmen-shop-bootstrap/target/fulfillmen-shop.jar
RUN if [ -f "${JAR_FILE}" ]; then \
        mkdir -p target && java -Djarmode=layertools -jar ${JAR_FILE} extract; \
    else \
        echo "错误: 无法找到JAR文件: ${JAR_FILE}" && \
        echo "目录内容:" && \
        find fulfillmen-shop-bootstrap -type f -name "*.jar" || echo "未找到JAR文件" && \
        exit 1; \
    fi

# 运行阶段
FROM crpi-irb3zn66qvxjqsx0.cn-shenzhen.personal.cr.aliyuncs.com/fulfillmen/eclipse-temurin:21-jdk
WORKDIR /app

# 创建非root用户
RUN addgroup --system --gid 1000 appuser && \
    adduser --system --uid 1000 --ingroup appuser appuser && \
    mkdir -p /app/logs /app/heapdump /app/config && \
    chown -R appuser:appuser /app

# 复制分层的应用到/app目录
COPY --from=builder /workspace/dependencies/ ./
COPY --from=builder /workspace/spring-boot-loader/ ./
COPY --from=builder /workspace/snapshot-dependencies/ ./
COPY --from=builder /workspace/application/ ./

# 复制配置文件
COPY --from=builder /workspace/fulfillmen-shop-bootstrap/src/main/resources/application*.yml /app/config/

# 创建并复制启动脚本
COPY --from=builder /workspace/fulfillmen-shop-bootstrap/src/main/docker/entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh && \
    chown appuser:appuser /app/entrypoint.sh

# 设置环境变量
ENV TZ=Asia/Shanghai \
    # default g1 gc
    JAVA_OPTS="-XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+ParallelRefProcEnabled -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/heapdump" \
    ACTIVE=dev \
    SERVER_PORT=8080

# 切换到非root用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=5s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:${SERVER_PORT}/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# 使用tini作为入口点，通过entrypoint.sh启动应用
ENTRYPOINT ["/usr/bin/tini", "--", "/app/entrypoint.sh"]
