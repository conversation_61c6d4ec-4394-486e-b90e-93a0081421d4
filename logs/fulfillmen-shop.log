2025-07-30 15:47:50 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 15:47:51 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Starting BootstrapApplication using Java 21.0.5 with PID 96107 (/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes started by yzsama in /Users/<USER>/work/fulfillmen/fulfillmen-workspace)
2025-07-30 15:47:51 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Running with Spring Boot v3.3.11, Spring v6.1.19
2025-07-30 15:47:51 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - The following 1 profile is active: "local"
2025-07-30 15:47:52 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 15:47:52 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 15:47:52 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2025-07-30 15:47:52 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.a.dao.SaTokenDaoRedissionConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken-Dao-Redis' completed initialization.
2025-07-30 15:47:52 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-30 15:47:52 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-30 15:47:52 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.MybatisPlusAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus' completed initialization.
2025-07-30 15:47:52 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 多租户拦截器已配置，忽略表: [tenant_commission_config, tenants, regions, tenant_plan_relation, sys_alibaba_category, subregions, sys_users, tenant_domains, sys_option, openapi_account, pdc_product_mapping, tenant_plans, tenants_info, tenant_files, sys_config, openapi_account_permission, openapi_interface, tenant_locales]
2025-07-30 15:47:52 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.i.MyBatisPlusIdGeneratorConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus-IdGenerator-CosId' completed initialization.
2025-07-30 15:47:53 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - Default locale initialized to: en_US
2025-07-30 15:47:53 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - MessageSource configured with basenames: [i18n/messages, i18n/openapi-message]
2025-07-30 15:47:53 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-30 15:47:53 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-30 15:47:53 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2520 ms
2025-07-30 15:47:53 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - === 过滤器配置信息 ===
2025-07-30 15:47:53 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器: enabled=true, order=-2147483638
2025-07-30 15:47:53 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器: enabled=true, order=-2147483628
2025-07-30 15:47:53 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 过滤器优先级说明:
过滤器执行顺序（数值越小优先级越高）：

1. TRACE_FILTER     (-2147483648) - 链路跟踪过滤器，生成 TraceId
2. TENANT_FILTER    (-2147483638) - 租户过滤器，设置租户上下文
3. MDC_FILTER       (-2147483628) - MDC 过滤器，设置日志上下文
4. XSS_FILTER       (-2147483548) - XSS 过滤器，安全防护
5. CORS_FILTER      (-2147483538) - CORS 过滤器，跨域处理
6. LOG_FILTER       (2147483637) - 日志过滤器，记录请求响应

注意：
- 链路跟踪过滤器优先级最高，确保 TraceId 在整个请求生命周期中可用
- 租户过滤器在链路跟踪之后，为后续过滤器提供租户上下文
- MDC 过滤器在租户过滤器之后，可以获取到租户信息并设置到日志上下文
- 安全相关过滤器（XSS、CORS）在业务过滤器之前执行
- 日志过滤器优先级最低，记录完整的请求响应信息

2025-07-30 15:47:53 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - ===================
2025-07-30 15:47:53 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器已注册 [enabled=true, order=-2147483638, urlPatterns=[/*], excludePatterns=20]
2025-07-30 15:47:53 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器已注册 [enabled=true, order=-2147483628, urlPatterns=[/*], features=IP标准化,浏览器信息,操作系统信息]
2025-07-30 15:47:53 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.trace.TraceAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Trace' completed initialization.
2025-07-30 15:47:53 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.l.i.autoconfigure.LogAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Log-interceptor' completed initialization.
2025-07-30 15:47:53 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?nayasource\.com
2025-07-30 15:47:53 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?nayasource\.com
2025-07-30 15:47:53 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http://localhost:[0-9]+
2025-07-30 15:47:53 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http://localhost:[0-9]+
2025-07-30 15:47:53 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-30 15:47:53 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-30 15:47:53 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-30 15:47:53 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-30 15:47:53 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 检测到 allowCredentials=true 且使用通配符配置，这可能导致CORS错误
2025-07-30 15:47:53 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 建议：1) 设置 allowCredentials=false，或 2) 使用具体的域名列表替换通配符
2025-07-30 15:47:53 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 当前策略：保持 allowCredentials=true，但建议检查配置
2025-07-30 15:47:53 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 跨域配置初始化完成 [常规域名: 0, 正则域名: 4, 允许凭证: true, 缓存时间: 3600s]
2025-07-30 15:47:53 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.w.a.cors.CorsConfigurationValidator - 
🔍 CORS配置分析报告:
==================================================
 1. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的HTTP方法
 2. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的请求头
 3. 🔒 安全建议: 生产环境建议明确指定允许的HTTP方法，避免使用 '*'
 4. ✅ 最佳实践: 正在使用正则表达式匹配，这是推荐的域名配置方式
==================================================
📖 更多信息请参考: fulfillmen-starter-web/CORS-CONFIG-EXAMPLE.md

2025-07-30 15:47:53 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-30 15:47:53 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-30 15:47:54 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.r.autoconfigure.RedissonAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Redisson' completed initialization.
2025-07-30 15:47:54 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-30 15:47:54 INFO  [redisson-netty-1-5] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-30 15:47:54 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-30 15:47:54 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-30 15:47:54 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 货币汇率缓存初始化完成
2025-07-30 15:47:54 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: fixed_window
2025-07-30 15:47:54 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: sliding_window
2025-07-30 15:47:54 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: token_bucket
2025-07-30 15:47:54 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: leaky_bucket
2025-07-30 15:47:54 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已启用
2025-07-30 15:47:54 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.g.a.GraphicCaptchaAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Captcha-Graphic' completed initialization.
2025-07-30 15:47:54 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ThreadPool' completed initialization.
2025-07-30 15:47:54 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - ThreadPool extension configuration applied: coreSize=12, maxSize=24, queueCapacity=2147483647, threadNamePrefix=naya-task-pool, rejectedPolicy=CALLER_RUNS
2025-07-30 15:47:54 INFO  [main] [tid::uId::ip::os::browser:] c.f.support.wms.autoconfigure.WmsAutoConfiguration - 初始化WMS WebClient: baseUrl=[http://*************:8888]
2025-07-30 15:47:54 INFO  [main] [tid::uId::ip::os::browser:] c.f.support.wms.autoconfigure.WmsAutoConfiguration - 初始化WMS声明式HTTP接口
2025-07-30 15:47:54 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 🎯 自动同步功能初始化: 禁用
2025-07-30 15:47:54 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.manager.strategy.SyncStrategyFactory - 同步策略工厂初始化完成，可用策略: [AUTO, DISABLED, MANUAL]
2025-07-30 15:47:54 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - 支持的消息类型: [ORDER_BUYER_VIEW_BUYER_MAKE, ORDER_BUYER_VIEW_ORDER_PAY, ORDER_BATCH_PAY, ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS, ORDER_BUYER_VIEW_PART_PART_SENDGOODS, ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS, ORDER_BUYER_VIEW_ORDER_SUCCESS, ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY]
2025-07-30 15:47:55 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 缓存配置详情: ProductDetailVO[本地:8min,远程:25min,容量:300]
2025-07-30 15:47:55 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 🎯 ProductService优化缓存配置完成:
2025-07-30 15:47:55 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── ProductDetailVO缓存: 本地8min/远程25min, 容量300, 键前缀: frontend:product:vo:
2025-07-30 15:47:55 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 双层缓存策略: 本地内存 + Redis远程
2025-07-30 15:47:55 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 空值缓存: 已启用，防止缓存穿透
2025-07-30 15:47:55 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   └── 同步机制: 已启用，保证多实例一致性
2025-07-30 15:47:55 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.autoconfigure.ValidatorAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Validator' completed initialization.
2025-07-30 15:47:55 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized CompositeLocaleResolver with default locale: en_US and supported locales: [en_US, zh_CN, zh_TW]
2025-07-30 15:47:55 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized LocaleChangeInterceptor with param name: lang
2025-07-30 15:47:55 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.autoconfigure.SpringDocAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ApiDoc' completed initialization.
2025-07-30 15:47:55 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.autoconfigure.SaTokenAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken' completed initialization.
2025-07-30 15:47:55 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.mvc.WebMvcAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web MVC' completed initialization.
2025-07-30 15:47:55 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-30 15:47:55 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-07-30 15:47:55 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-07-30 15:47:55 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.a.response.GlobalResponseAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Global Response' completed initialization.
2025-07-30 15:47:55 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@69e49a81
2025-07-30 15:47:55 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.j.autoconfigure.JetCacheAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'JetCache' completed initialization.
2025-07-30 15:47:55 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:96107, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-30 15:47:55 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=7, lastTimeStamp=1753861675681}] - instanceId:[InstanceId{instanceId=**************:96107, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-30 15:47:55 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-07-30 15:47:55 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-30 15:47:55 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-30 15:47:55 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-07-30 15:47:55 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-07-30 15:47:55 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-07-30 15:47:55 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-07-30 15:47:55 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-07-30 15:47:56 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - TaskScheduler extension configuration applied: poolSize=12, threadNamePrefix=scheduling-, rejectedPolicy=CALLER_RUNS
2025-07-30 15:47:56 DEBUG [main] [tid::uId::ip::os::browser:] c.f.starter.log.interceptor.handler.LogFilter - Filter 'logFilter' configured for use
2025-07-30 15:47:56 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-30 15:47:56 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-07-30 15:47:56 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-30 15:47:56 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-30 15:47:56 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-30 15:47:56 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-30 15:47:56 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 55 ms
2025-07-30 15:47:56 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-07-30 15:47:56 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-07-30 15:47:56 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-07-30 15:47:56 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-30 15:47:56 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-07-30 15:47:56 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Started BootstrapApplication in 6.068 seconds (process running for 6.792)
2025-07-30 15:47:56 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始初始化汇率缓存...
2025-07-30 15:47:56 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始定时刷新汇率缓存...
2025-07-30 15:47:56 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> USD
2025-07-30 15:47:56 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> USD = 0.13
2025-07-30 15:47:56 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-USD = 0.13 (原始: 0.13)
2025-07-30 15:47:56 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> USD
2025-07-30 15:47:56 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> USD = 0.13
2025-07-30 15:47:56 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-USD = 0.13 (原始: 0.13)
2025-07-30 15:47:56 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> EUR
2025-07-30 15:47:56 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> EUR
2025-07-30 15:47:56 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> EUR = 0.11
2025-07-30 15:47:56 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> EUR = 0.11
2025-07-30 15:47:56 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-EUR = 0.11 (原始: 0.11)
2025-07-30 15:47:56 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-EUR = 0.11 (原始: 0.11)
2025-07-30 15:47:57 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> JPY
2025-07-30 15:47:57 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> JPY = 20.68
2025-07-30 15:47:57 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-JPY = 20.68 (原始: 20.68)
2025-07-30 15:47:57 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> JPY
2025-07-30 15:47:57 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> JPY = 20.68
2025-07-30 15:47:57 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-JPY = 20.68 (原始: 20.68)
2025-07-30 15:47:57 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> KRW
2025-07-30 15:47:57 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> KRW
2025-07-30 15:47:57 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> KRW = 193.26
2025-07-30 15:47:57 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> KRW = 193.26
2025-07-30 15:47:57 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-KRW = 193.26 (原始: 193.26)
2025-07-30 15:47:57 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-KRW = 193.26 (原始: 193.26)
2025-07-30 15:47:57 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> INR
2025-07-30 15:47:57 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> INR = 12.07
2025-07-30 15:47:57 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-INR = 12.07 (原始: 12.07)
2025-07-30 15:47:57 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> INR
2025-07-30 15:47:57 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> INR = 12.07
2025-07-30 15:47:57 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-INR = 12.07 (原始: 12.07)
2025-07-30 15:47:57 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-07-30 15:47:57 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-07-30 15:47:57 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 汇率缓存初始化完成，缓存条目数: 5
2025-07-30 15:47:57 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-07-30 15:47:57 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-07-30 15:47:57 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 定时刷新汇率缓存完成，缓存条目数: 5
2025-07-30 15:47:57 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-07-30 15:47:57 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Fulfillmen Shop service started successfully.
2025-07-30 15:47:57 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API地址：http://127.0.0.1:8080
2025-07-30 15:47:57 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API文档：http://127.0.0.1:8080/doc.html
2025-07-30 15:47:57 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-07-30 15:47:57 INFO  [RMI TCP Connection(1)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 15:47:57 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 15:47:57 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 15:47:57 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-30 15:47:57 INFO  [RMI TCP Connection(1)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@3254e73b
2025-07-30 15:47:58 INFO  [RMI TCP Connection(1)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 15:51:35 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1102623362288492544]:[0] 忽略租户处理的请求: /alibaba/callback
2025-07-30 15:51:35 INFO  [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1102623362288492544]:[0] [POST] /alibaba/callback
2025-07-30 15:51:35 DEBUG [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1102623362288492544]:[0] 方法 WebhookApi.callBack 有 @RateLimitIgnore 注解，忽略限流
2025-07-30 15:51:35 DEBUG [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1102623362288492544]:[0] 收到阿里巴巴webhook回调，消息内容: {"data":{"buyerMemberId":"b2b-665170100","orderId":2854362723888540788,"currentStatus":"waitbuyerpay","sellerMemberId":"b2b-1676547900b7bb3","msgSendTime":"2018-05-30 19:24:18"},"gmtBorn":1753861895100,"msgId":142683171677,"type":"ORDER_BUYER_VIEW_BUYER_MAKE","userInfo":"b2b-2207416548807a4d12"}，签名: A15AD3F9B2ADCF5628A26EE63AA6250E6A849E3F
2025-07-30 15:51:35 INFO  [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1102623362288492544]:[0] 收到阿里巴巴webhook回调，消息长度: 296
2025-07-30 15:51:35 INFO  [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1102623362288492544]:[0] 开始处理webhook消息, 消息长度: 296
2025-07-30 15:51:35 INFO  [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1102623362288492544]:[0] 解析到 1 个消息事件
2025-07-30 15:51:35 INFO  [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - [1102623362288492544]:[0] 接收到订单webhook消息: msgId=142683171677, type=ORDER_BUYER_VIEW_BUYER_MAKE, orderId=2854362723888540788, status=waitbuyerpay
2025-07-30 15:51:35 INFO  [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1102623362288492544]:[0] 开始处理订单webhook消息: orderId=2854362723888540788, msgId=142683171677, messageType=ORDER_BUYER_VIEW_BUYER_MAKE, currentStatus=waitbuyerpay
2025-07-30 15:51:35 INFO  [naya-task-pool1] [tid::uId::ip::os::browser:] c.f.shop.manager.support.alibaba.impl.OrderManager - [1102623362288492544]:[0] 获取订单详情请求: OrderDetailRequestRecord[webSite=1688, orderId=2854362723888540788, needBuyerAddressAndPhone=null, needMemoInfo=null, needInvoiceInfo=null]
2025-07-30 15:51:35 INFO  [naya-task-pool1] [tid::uId::ip::os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1102623362288492544]:[0] 签名因子: param2/1/com.alibaba.trade/alibaba.trade.get.buyerView/8390330_aop_timestamp1753861895429access_token5fd99355-518b-47a6-b83e-0503223e0665orderId2854362723888540788webSite1688 签名: 1FE88C7EB1976A68AC6F77FA6A05869B9A0655A4
2025-07-30 15:51:43 DEBUG [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1102623362288492544]:[0] 订单数据获取完成: orderId=2854362723888540788, orderDetail=OrderDetailResponse.OrderDetail(baseInfo=TradeBaseInfo(id=2854362723888540788, idOfStr=2854362723888540788, businessType=cn, buyerId=b2b-2207416548807a4d12, completeTime=null, createTime=2025-07-30T08:22:52, modifyTime=2025-07-30T13:41:50, refund=0, sellerId=b2b-*************e3c18, shippingFee=0, status=waitsellersend, totalAmount=131, discount=0, buyerContact=Contact(phone=86-752-2313067, fax=null, email=null, name=汤维政, imInPlatform=惠州中田贸易, companyName=惠州市中田贸易有限公司, mobile=***********, address=null), sellerContact=TradeSellContact(super=Contact(phone=, fax=null, email=null, name=安丽侠, imInPlatform=瑞岚箱包厂, companyName=白沟新城瑞岚箱包厂(个体工商户), mobile=***********, address=null), shopName=白沟新城瑞岚箱包厂, wgSenderPhone=null, wgSenderName=null), tradeType=50060, refundPayment=0, allDeliveredTime=null, payTime=2025-07-30T13:41:50, receivingTime=null, alipayTradeId=2025073022001846301453597055, sumProductPayment=132, flowTemplateCode=assureTrade, sellerOrder=false, buyerLoginId=惠州中田贸易, sellerLoginId=瑞岚箱包厂, closeOperateType=, couponFee=0, receiverInfo=TradeReceiverInfo(toFullName=中田 13622, toDivisionCode=441302, toPost=516000, toArea=广东省 惠州市 惠城区 江北 金泽物流园二期一号楼四楼-13622, toProvince=null, toCity=null, toCounty=null, toAddress=null, toMobile=18124000751, toPhone=null, toEmail=null, toTownCode=null), tradeTypeDesc=担保交易, payChannelList=[支付宝], tradeTypeCode=assureTrade, payTimeout=432000, payTimeoutType=0, payChannelCodeList=[alipay], outOrderId=13622, stepPayAll=false, stepOrderList=null, newStepOrderList=[TradeBaseInfo.NewStepOrder(gmtStart=2025-07-30T08:22:53, gmtPay=2025-07-30T13:41:51, gmtEnd=null, stepNo=1, lastStep=true, stepName=全款交易, activeStatus=1, payStatus=2, logisticsStatus=1, payFee=131, paidFee=131, goodsFee=null, adjustFee=0, discountFee=1, postFee=0, paidPostFee=0)], overSeaOrder=false, sellerCreditLevel=null, buyerFeedback=null, subBuyerLoginId=null, closeReason=null, sellerAlipayId=null, buyerUserId=null, buyerMemo=null, buyerRemarkIcon=null, refundStatus=null, remark=null, preOrderId=null, confirmedTime=null, closeRemark=null, stepAgreementPath=null, refundStatusForAs=null, sellerUserId=null, buyerAlipayId=null, refundId=null, inventoryMode=null), orderBizInfo=TradeOrderBizInfo(odsCyd=false, creditOrderDetail=null, preOrderInfo=null, lstOrderInfo=null, accountPeriodTime=null, creditOrder=false, dropShipping=false, erpBuyerUserId=null, erpOrderId=null, erpBuyerOrgId=null, isCz=null, isDz=null, dz=null, dropshipping=false, shippingInsurance=givenByAnXinGou, hyperLinkCangFaOrder=null, hyperLinkOrder=null, hyperLinkSecondStepOrder=null, hyperLinkShipType=null, lightningWarehouse=null, aeDoorPickUp=null), tradeTerms=[TradeTermsInfo(payStatus=2, payTime=2025-07-30T13:41:51, payWay=13, phasAmount=131, phase=*****************, phaseCondition=null, phaseDate=null, cardPay=false, expressPay=false, payWayDesc=支付平台)], productItems=[TradeProductItem(cargoNumber=9064金色, description=null, itemAmount=65.5, name=古灵精怪箱包 跨境亚克力包包女斜跨盒子包石头包宴会夏天简约晚, price=33, productId=************, productImgUrl=[http://cbu01.alicdn.com/img/ibank/O1CN01ajfWE31Bs33WVsijG_!!0-0-cib.80x80.jpg, http://cbu01.alicdn.com/img/ibank/O1CN01ajfWE31Bs33WVsijG_!!0-0-cib.jpg], productSnapshotUrl=https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2854362723889540788, quantity=2, refund=0, skuId=*************, sort=null, status=waitsellersend, subItemId=2854362723889540788, type=common, unit=个, weight=null, weightUnit=null, productCargoNumber=9064, skuInfos=[TradeProductItem.TradeSkuInfo(name=颜色, value=金色)], entryDiscount=0, specId=0061b739ae60e11d22170378ac121c70, quantityFactor=1, statusStr=等待卖家发货, refundStatus=null, refundId=null, refundIdForAs=null, subItemIdString=2854362723889540788, closeReason=null, categoryId=null, unitPrice=null, logisticsStatus=1, gmtCreate=2025-07-30T08:22:53, gmtModified=2025-07-30T13:41:50, gmtCompleted=null, gmtPayExpireTime=2025-07-30T13:56:18, sharePostage=null, guaranteesTerms=[TradeGuaranteeTermsInfo(assuranceInfo=卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。, assuranceType=qtwlybt, qualityAssuranceType=7天无理由退货, value=null), TradeGuaranteeTermsInfo(assuranceInfo=“满足相应条件时，用户在退货寄出后，享受极速退款到账。, assuranceType=lsjst_s, qualityAssuranceType=极速退款, value=null), TradeGuaranteeTermsInfo(assuranceInfo=卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。, assuranceType=ssbxsfh, qualityAssuranceType=48小时发货, value=null)]), TradeProductItem(cargoNumber=9064银色, description=null, itemAmount=65.5, name=古灵精怪箱包 跨境亚克力包包女斜跨盒子包石头包宴会夏天简约晚, price=33, productId=************, productImgUrl=[http://cbu01.alicdn.com/img/ibank/O1CN01vZrpWM1NGHnGoRxTr_!!*************-0-cib.80x80.jpg, http://cbu01.alicdn.com/img/ibank/O1CN01vZrpWM1NGHnGoRxTr_!!*************-0-cib.jpg], productSnapshotUrl=https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2854362723890540788, quantity=2, refund=0, skuId=*************, sort=null, status=waitsellersend, subItemId=2854362723890540788, type=common, unit=个, weight=null, weightUnit=null, productCargoNumber=9064, skuInfos=[TradeProductItem.TradeSkuInfo(name=颜色, value=银色)], entryDiscount=0, specId=4872dc478000b9bf5120596432ec71fa, quantityFactor=1, statusStr=等待卖家发货, refundStatus=null, refundId=null, refundIdForAs=null, subItemIdString=2854362723890540788, closeReason=null, categoryId=null, unitPrice=null, logisticsStatus=1, gmtCreate=2025-07-30T08:22:53, gmtModified=2025-07-30T13:41:50, gmtCompleted=null, gmtPayExpireTime=2025-07-30T13:56:18, sharePostage=null, guaranteesTerms=[TradeGuaranteeTermsInfo(assuranceInfo=卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。, assuranceType=qtwlybt, qualityAssuranceType=7天无理由退货, value=null), TradeGuaranteeTermsInfo(assuranceInfo=“满足相应条件时，用户在退货寄出后，享受极速退款到账。, assuranceType=lsjst_s, qualityAssuranceType=极速退款, value=null), TradeGuaranteeTermsInfo(assuranceInfo=卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。, assuranceType=ssbxsfh, qualityAssuranceType=48小时发货, value=null)])], nativeLogistics=TradeNativeLogisticsInfo(address=江北 金泽物流园二期一号楼四楼-13622, area=惠城区, areaCode=441302, city=惠州市, contactPerson=中田 13622, fax=null, mobile=18124000751, province=广东省, telephone=null, zip=516000, logisticsItems=null, townCode=null, town=null), orderInvoiceInfo=null, guaranteesTerms=null, orderRateInfo=TradeOrderRateInfo(buyerRateStatus=5, sellerRateStatus=5, buyerRateList=null, sellerRateList=null), overseasExtraAddress=null, customs=null, quoteList=null, extAttributes=[], fromEncryptOrder=false, encryptOutOrderInfo=null, overseaLogisticsInfo=null), wmsOrderDetails=[WmsPurchaseOrderDetailsRes(nayaPurchaseNo=, purchaseNo=C136222025073008225100001, shopOrderId=, sellerOpenId=BBBTdyY64-w_8VsDzSjoEFOSQ, cusCode=13622, storeId=536, outTradeNo=, orderId=2854362723888540788, customerId=null, createUser=13622, createTime=2025-07-30T08:22:51.250, remark=Alibaba.1688.com, platform=, payType=BALANCE, payUrl=https://trade.1688.com/order/cashier.htm?orderId=2854362723888540788, payBody=, platformPayType=null, shippingFee=0.000, originalShippingFee=0.000, productOriginalTotalAmount=132.000, total=132.000, serviceFee=6.600, productSalesTotalAmount=132.000, productFinalTotalAmount=132.000, alibabaTotalAmount=131.000, alibabaFinalAmount=131.000, finalShoppingFee=null, originalTotalPrice=138.600, discount=0.000, plusDiscount=0.000, couponDiscount=0.000, totalAmount=138.600, status=PURCHASED_PENDING_SHIPMENT, isRequestQuote=false, paymentTime=1900-01-01T00:00, shippingTime=1900-01-01T00:00, completeTime=1900-01-01T00:00, link=, trackingNo=, platformStatus=, platformRemark=, orderDetailsItemRes=[WmsPurchaseOrderDetailsItemRes(skuId=null, enName=Quirky Luggage Cross-Border Acrylic Bag Women's Cross-Body Box Bag Stone Bag Banquet Summer Simple Evening, cnName=古灵精怪箱包 跨境亚克力包包女斜跨盒子包石头包宴会夏天简约晚, weight=0.000, quantity=2, variantId=0061b739ae60e11d22170378ac121c70, productId=************, unitPrice=33.000, originUnitPrice=33.000, finalUnitPrice=33.000, subTotal=66.000, originSubTotalAmount=66.000, finalSubTotalAmount=66.000, imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01Dp2HDg1y6nJbSN2Vc_!!*************-0-cib.jpg, productUrl=null, attributes=null, skuAttrib=颜色:金色;, skuAttribEn=Color:Gold;), WmsPurchaseOrderDetailsItemRes(skuId=null, enName=Quirky Luggage Cross-Border Acrylic Bag Women's Cross-Body Box Bag Stone Bag Banquet Summer Simple Evening, cnName=古灵精怪箱包 跨境亚克力包包女斜跨盒子包石头包宴会夏天简约晚, weight=0.000, quantity=2, variantId=4872dc478000b9bf5120596432ec71fa, productId=************, unitPrice=33.000, originUnitPrice=33.000, finalUnitPrice=33.000, subTotal=66.000, originSubTotalAmount=66.000, finalSubTotalAmount=66.000, imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01kiME8x1y6nJcrATuI_!!*************-0-cib.jpg, productUrl=null, attributes=null, skuAttrib=颜色:银色;, skuAttribEn=Color:Silver;)])]
2025-07-30 15:51:46 DEBUG [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1102623362288492544]:[0] 开始检查订单数据完整性: orderId=2854362723888540788
2025-07-30 15:51:49 INFO  [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1102623362288492544]:[0] 订单数据完整性检查结果: orderId=2854362723888540788, isComplete=false, isNewVersion=false, missing=采购订单, 供应商订单, 订单项
2025-07-30 15:51:49 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1102623423823126528]:[0] 忽略租户处理的请求: /alibaba/callback
2025-07-30 15:51:55 INFO  [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1102623423823126528]:[0] [POST] /alibaba/callback
2025-07-30 15:51:55 DEBUG [XNIO-1 task-4] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1102623446866632704]:[0] 忽略租户处理的请求: /alibaba/callback
2025-07-30 15:51:55 INFO  [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1102623362288492544]:[0] 开始同步和补齐订单数据: orderId=2854362723888540788, needsSync=true
2025-07-30 15:51:55 DEBUG [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1102623423823126528]:[0] 方法 WebhookApi.callBack 有 @RateLimitIgnore 注解，忽略限流
2025-07-30 15:51:55 INFO  [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1102623446866632704]:[0] [POST] /alibaba/callback
2025-07-30 15:51:55 DEBUG [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1102623423823126528]:[0] 收到阿里巴巴webhook回调，消息内容: {"bizKey":"2835191931563540788","data":{"buyerMemberId":"b2b-2207416548807a4d12","currentStatus":"confirm_goods_and_has_subsidy","orderId":2835191931563540788,"sellerMemberId":"b2b-*************76e59","msgSendTime":"2025-07-30 15:21:48"},"gmtBorn":1753860109000,"msgId":142678008946,"type":"ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS","userInfo":"b2b-2207416548807a4d12"}，签名: F1F96DB0A2C54E9A951BDDF552DCD025BBF28E42
2025-07-30 15:51:55 INFO  [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1102623423823126528]:[0] 收到阿里巴巴webhook回调，消息长度: 372
2025-07-30 15:51:55 INFO  [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1102623423823126528]:[0] 开始处理webhook消息, 消息长度: 372
2025-07-30 15:51:55 DEBUG [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1102623446866632704]:[0] 方法 WebhookApi.callBack 有 @RateLimitIgnore 注解，忽略限流
2025-07-30 15:51:55 INFO  [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1102623362288492544]:[0] 开始补齐缺失的订单数据: orderId=2854362723888540788, missing=采购订单, 供应商订单, 订单项
2025-07-30 15:51:55 INFO  [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1102623423823126528]:[0] 解析到 1 个消息事件
2025-07-30 15:51:55 DEBUG [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1102623446866632704]:[0] 收到阿里巴巴webhook回调，消息内容: {"bizKey":"2835191931563540788","data":{"buyerMemberId":"b2b-2207416548807a4d12","currentStatus":"success","orderId":2835191931563540788,"sellerMemberId":"b2b-*************76e59","msgSendTime":"2025-07-30 15:21:50"},"gmtBorn":1753860110000,"msgId":142678015805,"type":"ORDER_BUYER_VIEW_ORDER_SUCCESS","userInfo":"b2b-2207416548807a4d12"}，签名: A1C80A0D1C2CB5B324930F6CED8F4DA5AC6B52BA
2025-07-30 15:51:55 INFO  [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1102623446866632704]:[0] 收到阿里巴巴webhook回调，消息长度: 337
2025-07-30 15:51:55 INFO  [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1102623446866632704]:[0] 开始处理webhook消息, 消息长度: 337
2025-07-30 15:51:55 INFO  [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1102623446866632704]:[0] 解析到 1 个消息事件
2025-07-30 15:51:55 INFO  [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - [1102623446866632704]:[0] 接收到订单webhook消息: msgId=142678015805, type=ORDER_BUYER_VIEW_ORDER_SUCCESS, orderId=2835191931563540788, status=success
2025-07-30 15:51:55 INFO  [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1102623446866632704]:[0] 开始处理订单webhook消息: orderId=2835191931563540788, msgId=142678015805, messageType=ORDER_BUYER_VIEW_ORDER_SUCCESS, currentStatus=success
2025-07-30 15:51:55 INFO  [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - [1102623423823126528]:[0] 接收到订单webhook消息: msgId=142678008946, type=ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS, orderId=2835191931563540788, status=confirm_goods_and_has_subsidy
2025-07-30 15:51:55 INFO  [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1102623423823126528]:[0] 开始处理订单webhook消息: orderId=2835191931563540788, msgId=142678008946, messageType=ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS, currentStatus=confirm_goods_and_has_subsidy
2025-07-30 15:51:55 INFO  [naya-task-pool3] [tid::uId::ip::os::browser:] c.f.shop.manager.support.alibaba.impl.OrderManager - [1102623446866632704]:[0] 获取订单详情请求: OrderDetailRequestRecord[webSite=1688, orderId=2835191931563540788, needBuyerAddressAndPhone=null, needMemoInfo=null, needInvoiceInfo=null]
2025-07-30 15:51:55 INFO  [naya-task-pool3] [tid::uId::ip::os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1102623446866632704]:[0] 签名因子: param2/1/com.alibaba.trade/alibaba.trade.get.buyerView/8390330_aop_timestamp1753861915493access_token5fd99355-518b-47a6-b83e-0503223e0665orderId2835191931563540788webSite1688 签名: 4BCDB77F376CEA43432175B1CD0F6C78284BB52B
2025-07-30 15:51:55 INFO  [naya-task-pool5] [tid::uId::ip::os::browser:] c.f.shop.manager.support.alibaba.impl.OrderManager - [1102623423823126528]:[0] 获取订单详情请求: OrderDetailRequestRecord[webSite=1688, orderId=2835191931563540788, needBuyerAddressAndPhone=null, needMemoInfo=null, needInvoiceInfo=null]
2025-07-30 15:51:55 INFO  [naya-task-pool5] [tid::uId::ip::os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1102623423823126528]:[0] 签名因子: param2/1/com.alibaba.trade/alibaba.trade.get.buyerView/8390330_aop_timestamp1753861915493access_token5fd99355-518b-47a6-b83e-0503223e0665orderId2835191931563540788webSite1688 签名: 4BCDB77F376CEA43432175B1CD0F6C78284BB52B
2025-07-30 15:51:55 WARN  [naya-task-pool5] [tid::uId::ip::os::browser:] io.micrometer.core.instrument.MeterRegistry - [1102623423823126528]:[0] This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-07-30 15:51:55 DEBUG [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1102623446866632704]:[0] 订单数据获取完成: orderId=2835191931563540788, orderDetail=OrderDetailResponse.OrderDetail(baseInfo=TradeBaseInfo(id=2835191931563540788, idOfStr=2835191931563540788, businessType=cb, buyerId=b2b-2207416548807a4d12, completeTime=2025-07-30T15:21:50, createTime=2025-07-18T11:34:19, modifyTime=2025-07-30T15:21:50, refund=0, sellerId=b2b-*************76e59, shippingFee=0, status=success, totalAmount=650, discount=-16700, buyerContact=Contact(phone=86-752-2313067, fax=null, email=null, name=汤维政, imInPlatform=惠州中田贸易, companyName=惠州市中田贸易有限公司, mobile=***********, address=null), sellerContact=TradeSellContact(super=Contact(phone=86-, fax=null, email=null, name=杨红玉, imInPlatform=大唐彩印包装, companyName=临沂精诚纸制品有限公司, mobile=***********, address=null), shopName=临沂精诚纸制品有限公司, wgSenderPhone=null, wgSenderName=null), tradeType=50060, refundPayment=0, allDeliveredTime=2025-07-20T15:21:37, payTime=2025-07-18T16:23:20, receivingTime=2025-07-30T15:21:48.816, alipayTradeId=2025071822001846301400514153, sumProductPayment=860, flowTemplateCode=assureTrade, sellerOrder=false, buyerLoginId=惠州中田贸易, sellerLoginId=大唐彩印包装, closeOperateType=, couponFee=0, receiverInfo=TradeReceiverInfo(toFullName=崔磊, toDivisionCode=330113, toPost=516000, toArea=浙江省 杭州市 钱塘区 下沙街道 益丰路55号3栋3楼杭州速麦科技有限公司, toProvince=null, toCity=null, toCounty=null, toAddress=null, toMobile=15867157301, toPhone=, toEmail=null, toTownCode=*********), tradeTypeDesc=担保交易, payChannelList=[支付宝], tradeTypeCode=assureTrade, payTimeout=432000, payTimeoutType=0, payChannelCodeList=[alipay], outOrderId=10808, stepPayAll=false, stepOrderList=null, newStepOrderList=[TradeBaseInfo.NewStepOrder(gmtStart=2025-07-18T11:34:20, gmtPay=2025-07-18T16:23:20, gmtEnd=2025-07-30T15:21:50, stepNo=1, lastStep=true, stepName=全款交易, activeStatus=1, payStatus=6, logisticsStatus=3, payFee=650, paidFee=650, goodsFee=null, adjustFee=-167, discountFee=43, postFee=0, paidPostFee=0)], overSeaOrder=false, sellerCreditLevel=null, buyerFeedback=null, subBuyerLoginId=null, closeReason=null, sellerAlipayId=null, buyerUserId=null, buyerMemo=null, buyerRemarkIcon=null, refundStatus=null, remark=null, preOrderId=null, confirmedTime=null, closeRemark=null, stepAgreementPath=null, refundStatusForAs=null, sellerUserId=null, buyerAlipayId=null, refundId=null, inventoryMode=null), orderBizInfo=TradeOrderBizInfo(odsCyd=false, creditOrderDetail=null, preOrderInfo=null, lstOrderInfo=null, accountPeriodTime=null, creditOrder=false, dropShipping=false, erpBuyerUserId=null, erpOrderId=null, erpBuyerOrgId=null, isCz=null, isDz=null, dz=null, dropshipping=false, shippingInsurance=givenByAnXinGou, hyperLinkCangFaOrder=null, hyperLinkOrder=null, hyperLinkSecondStepOrder=null, hyperLinkShipType=null, lightningWarehouse=null, aeDoorPickUp=null), tradeTerms=[TradeTermsInfo(payStatus=6, payTime=2025-07-18T16:23:20, payWay=13, phasAmount=650, phase=*****************, phaseCondition=null, phaseDate=null, cardPay=false, expressPay=false, payWayDesc=支付平台)], productItems=[TradeProductItem(cargoNumber=********, description=null, itemAmount=650, name=外卖餐盒腰封定制厂家销售免费设计加厚材质印刷打包盒腰封定制, price=0.4, productId=************, productImgUrl=[http://cbu01.alicdn.com/img/ibank/O1CN01LWVGm21Bs32WmLLOc_!!0-0-cib.80x80.jpg, http://cbu01.alicdn.com/img/ibank/O1CN01LWVGm21Bs32WmLLOc_!!0-0-cib.jpg], productSnapshotUrl=https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2835191931563540788, quantity=2150, refund=0, skuId=*************, sort=null, status=success, subItemId=2835191931563540788, type=common, unit=个, weight=null, weightUnit=null, productCargoNumber=002腰封, skuInfos=[TradeProductItem.TradeSkuInfo(name=规格, value=客户自选)], entryDiscount=-16700, specId=0e6bebc8816dc96a6407ef35916793eb, quantityFactor=1, statusStr=交易成功, refundStatus=null, refundId=null, refundIdForAs=null, subItemIdString=2835191931563540788, closeReason=null, categoryId=null, unitPrice=null, logisticsStatus=3, gmtCreate=2025-07-18T11:34:19, gmtModified=2025-07-30T15:21:50, gmtCompleted=2025-07-30T15:21:50, gmtPayExpireTime=null, sharePostage=null, guaranteesTerms=[TradeGuaranteeTermsInfo(assuranceInfo=“满足相应条件时，用户在退货寄出后，享受极速退款到账。, assuranceType=lsjst_s, qualityAssuranceType=极速退款, value=null), TradeGuaranteeTermsInfo(assuranceInfo=卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付, assuranceType=jqbz, qualityAssuranceType=交期保障, value=7)])], nativeLogistics=TradeNativeLogisticsInfo(address=益丰路55号3栋3楼杭州速麦科技有限公司, area=钱塘区, areaCode=330113, city=杭州市, contactPerson=崔磊, fax=null, mobile=15867157301, province=浙江省, telephone=, zip=516000, logisticsItems=[TradeNativeLogisticsInfo.LogisticsItem(id=235362218991462, deliveredTime=2025-07-20T15:21:37, logisticsCode=LP00749032886378, type=0, status=alreadysend, gmtModified=2025-07-22T02:54:35, gmtCreate=2025-07-20T15:21:36, carriage=null, fromProvince=山东省, fromCity=临沂市, fromArea=兰山区, fromAddress=兰山街道 宏达路与大山路交汇兰田国际, fromPhone=86-, fromMobile=***********, fromPost=000000, logisticsCompanyId=3, logisticsCompanyNo=ZTO, logisticsCompanyName=中通快递(ZTO), logisticsBillNo=78925093885449, subItemIds=2835191931563540788, toProvince=null, toCity=杭州市, toArea=钱塘区, toAddress=下沙街道 益丰路55号3栋3楼杭州速麦科技有限公司, toPhone=null, toMobile=null, toPost=516000, noLogisticsName=, noLogisticsTel=, noLogisticsBillNo=, noLogisticsCondition=-1)], townCode=*********, town=下沙街道), orderInvoiceInfo=null, guaranteesTerms=null, orderRateInfo=TradeOrderRateInfo(buyerRateStatus=5, sellerRateStatus=5, buyerRateList=null, sellerRateList=null), overseasExtraAddress=null, customs=null, quoteList=null, extAttributes=[], fromEncryptOrder=false, encryptOutOrderInfo=null, overseaLogisticsInfo=null), wmsOrderDetails=[WmsPurchaseOrderDetailsRes(nayaPurchaseNo=, purchaseNo=C108082025071811341800008, shopOrderId=, sellerOpenId=BBBbozC9TtTIwXge8zWeJzR1A, cusCode=10808, storeId=536, outTradeNo=, orderId=2835191931563540788, customerId=null, createUser=10808, createTime=2025-07-18T11:34:18.377, remark=Alibaba.1688.com, platform=, payType=BALANCE, payUrl=https://trade.1688.com/order/cashier.htm?orderId=2835191931563540788, payBody=, platformPayType=诚e赊,跨境宝2.0,支付宝,其它,其它, shippingFee=0.000, originalShippingFee=0.000, productOriginalTotalAmount=860.000, total=860.000, serviceFee=8.600, productSalesTotalAmount=860.000, productFinalTotalAmount=650.000, alibabaTotalAmount=817.000, alibabaFinalAmount=650.000, finalShoppingFee=null, originalTotalPrice=868.600, discount=0.000, plusDiscount=43.000, couponDiscount=0.000, totalAmount=868.600, status=PENDING_SIGNATURE, isRequestQuote=true, paymentTime=2025-07-18T16:23:32.750, shippingTime=2025-07-21T08:03:44.977, completeTime=1900-01-01T00:00, link=, trackingNo=78925093885449, platformStatus=, platformRemark=, orderDetailsItemRes=[WmsPurchaseOrderDetailsItemRes(skuId=null, enName=Takeaway lunch box waist seal customization factory sales free design thick material printing packing box waist seal customization, cnName=外卖餐盒腰封定制厂家销售免费设计加厚材质印刷打包盒腰封定制, weight=0.000, quantity=2150, variantId=0e6bebc8816dc96a6407ef35916793eb, productId=************, unitPrice=0.400, originUnitPrice=0.400, finalUnitPrice=0.302, subTotal=860.000, originSubTotalAmount=860.000, finalSubTotalAmount=650.000, imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN011saB8g2AmitT79kkz_!!*************-0-cib.jpg, productUrl=null, attributes=null, skuAttrib=规格:客户自选;, skuAttribEn=Specifications:Customer Choice;)])]
2025-07-30 15:51:55 DEBUG [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1102623446866632704]:[0] 开始检查订单数据完整性: orderId=2835191931563540788
2025-07-30 15:51:55 INFO  [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1102623446866632704]:[0] 订单数据完整性检查结果: orderId=2835191931563540788, isComplete=false, isNewVersion=false, missing=采购订单, 供应商订单, 订单项
2025-07-30 15:51:55 INFO  [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1102623446866632704]:[0] 开始同步和补齐订单数据: orderId=2835191931563540788, needsSync=true
2025-07-30 15:51:55 INFO  [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1102623446866632704]:[0] 开始补齐缺失的订单数据: orderId=2835191931563540788, missing=采购订单, 供应商订单, 订单项
2025-07-30 15:51:55 ERROR [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1102623446866632704]:[0] 事务执行过程中发生异常: orderId=2835191931563540788
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
### The error may exist in com/fulfillmen/shop/dao/mapper/TzOrderPurchaseMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO tz_order_purchase (id, purchase_order_no, buyer_id, buyer_type, order_status, order_date, service_fee, exchange_rate_snapshot, customer_goods_amount, customer_total_freight, customer_total_amount, payable_coupon_amount, payable_discount_amount, payable_goods_amount, payable_freight_total, payable_amount_total, delivery_address, postal_code, province, city, district, consignee_name, consignee_phone, supplier_count, line_item_count, completed_supplier_count, total_quantity, tenant_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, null)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
; Column 'tenant_id' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:97)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy132.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy165.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.TzOrderPurchaseRepositoryImpl$$SpringCGLIB$$0.save(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSavePurchaseOrder(OrderDataSyncServiceImpl.java:256)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:185)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:174)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:158)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:93)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:118)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:39)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:86)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy228.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy227.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 132 common frames omitted
2025-07-30 15:51:55 ERROR [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1102623446866632704]:[0] 订单数据补齐失败: orderId=2835191931563540788
java.lang.RuntimeException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:225)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:174)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:158)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:93)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:118)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:39)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:86)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
### The error may exist in com/fulfillmen/shop/dao/mapper/TzOrderPurchaseMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO tz_order_purchase (id, purchase_order_no, buyer_id, buyer_type, order_status, order_date, service_fee, exchange_rate_snapshot, customer_goods_amount, customer_total_freight, customer_total_amount, payable_coupon_amount, payable_discount_amount, payable_goods_amount, payable_freight_total, payable_amount_total, delivery_address, postal_code, province, city, district, consignee_name, consignee_phone, supplier_count, line_item_count, completed_supplier_count, total_quantity, tenant_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, null)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
; Column 'tenant_id' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:97)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy132.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy165.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.TzOrderPurchaseRepositoryImpl$$SpringCGLIB$$0.save(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSavePurchaseOrder(OrderDataSyncServiceImpl.java:256)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:185)
	... 112 common frames omitted
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy228.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy227.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 132 common frames omitted
2025-07-30 15:51:55 ERROR [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1102623446866632704]:[0] 订单webhook消息处理失败: orderId=2835191931563540788, msgId=142678015805, messageType=ORDER_BUYER_VIEW_ORDER_SUCCESS, error=订单数据补齐失败
java.lang.RuntimeException: 订单数据补齐失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:230)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:158)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:93)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:118)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:39)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:86)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:225)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:174)
	... 110 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
### The error may exist in com/fulfillmen/shop/dao/mapper/TzOrderPurchaseMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO tz_order_purchase (id, purchase_order_no, buyer_id, buyer_type, order_status, order_date, service_fee, exchange_rate_snapshot, customer_goods_amount, customer_total_freight, customer_total_amount, payable_coupon_amount, payable_discount_amount, payable_goods_amount, payable_freight_total, payable_amount_total, delivery_address, postal_code, province, city, district, consignee_name, consignee_phone, supplier_count, line_item_count, completed_supplier_count, total_quantity, tenant_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, null)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
; Column 'tenant_id' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:97)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy132.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy165.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.TzOrderPurchaseRepositoryImpl$$SpringCGLIB$$0.save(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSavePurchaseOrder(OrderDataSyncServiceImpl.java:256)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:185)
	... 112 common frames omitted
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy228.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy227.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 132 common frames omitted
2025-07-30 15:51:55 ERROR [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1102623446866632704]:[0] 发布订单处理失败事件: orderId=2835191931563540788, error=订单数据补齐失败
2025-07-30 15:51:55 ERROR [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - [1102623446866632704]:[0] 订单webhook消息处理失败: msgId=142678015805, type=ORDER_BUYER_VIEW_ORDER_SUCCESS, orderId=2835191931563540788, error=订单数据补齐失败
java.lang.RuntimeException: 订单数据补齐失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:230)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:158)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:93)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:118)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:39)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:86)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:225)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:174)
	... 110 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
### The error may exist in com/fulfillmen/shop/dao/mapper/TzOrderPurchaseMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO tz_order_purchase (id, purchase_order_no, buyer_id, buyer_type, order_status, order_date, service_fee, exchange_rate_snapshot, customer_goods_amount, customer_total_freight, customer_total_amount, payable_coupon_amount, payable_discount_amount, payable_goods_amount, payable_freight_total, payable_amount_total, delivery_address, postal_code, province, city, district, consignee_name, consignee_phone, supplier_count, line_item_count, completed_supplier_count, total_quantity, tenant_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, null)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
; Column 'tenant_id' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:97)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy132.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy165.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.TzOrderPurchaseRepositoryImpl$$SpringCGLIB$$0.save(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSavePurchaseOrder(OrderDataSyncServiceImpl.java:256)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:185)
	... 112 common frames omitted
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy228.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy227.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 132 common frames omitted
2025-07-30 15:51:55 ERROR [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.s.alibaba.webhook.AbstractTypedMessageHandler - [1102623446866632704]:[0] 消息处理异常: msgId=142678015805, type=ORDER_BUYER_VIEW_ORDER_SUCCESS
java.lang.RuntimeException: 订单数据补齐失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:230)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:158)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:93)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:118)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:39)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:86)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:225)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:174)
	... 110 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
### The error may exist in com/fulfillmen/shop/dao/mapper/TzOrderPurchaseMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO tz_order_purchase (id, purchase_order_no, buyer_id, buyer_type, order_status, order_date, service_fee, exchange_rate_snapshot, customer_goods_amount, customer_total_freight, customer_total_amount, payable_coupon_amount, payable_discount_amount, payable_goods_amount, payable_freight_total, payable_amount_total, delivery_address, postal_code, province, city, district, consignee_name, consignee_phone, supplier_count, line_item_count, completed_supplier_count, total_quantity, tenant_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, null)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
; Column 'tenant_id' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:97)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy132.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy165.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.TzOrderPurchaseRepositoryImpl$$SpringCGLIB$$0.save(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSavePurchaseOrder(OrderDataSyncServiceImpl.java:256)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:185)
	... 112 common frames omitted
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy228.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy227.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 132 common frames omitted
2025-07-30 15:51:55 INFO  [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1102623446866632704]:[0] Webhook消息处理完成，处理时间: 364ms，处理结果数量: 1
2025-07-30 15:51:55 INFO  [XNIO-1 task-4] [tid::uId::ip:**********:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1102623446866632704]:[0] [POST] /alibaba/callback 200 373ms
2025-07-30 15:51:55 ERROR [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1102623362288492544]:[0] 事务执行过程中发生异常: orderId=2854362723888540788
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
### The error may exist in com/fulfillmen/shop/dao/mapper/TzOrderPurchaseMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO tz_order_purchase (id, purchase_order_no, buyer_id, buyer_type, order_status, order_date, service_fee, exchange_rate_snapshot, customer_goods_amount, customer_total_freight, customer_total_amount, payable_coupon_amount, payable_discount_amount, payable_goods_amount, payable_freight_total, payable_amount_total, delivery_address, postal_code, province, city, district, consignee_name, consignee_phone, supplier_count, line_item_count, completed_supplier_count, total_quantity, tenant_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, null)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
; Column 'tenant_id' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:97)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy132.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy165.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.TzOrderPurchaseRepositoryImpl$$SpringCGLIB$$0.save(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSavePurchaseOrder(OrderDataSyncServiceImpl.java:256)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:185)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:174)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:158)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:93)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:118)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:39)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:86)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy228.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy227.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 132 common frames omitted
2025-07-30 15:51:55 ERROR [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1102623362288492544]:[0] 订单数据补齐失败: orderId=2854362723888540788
java.lang.RuntimeException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:225)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:174)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:158)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:93)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:118)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:39)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:86)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
### The error may exist in com/fulfillmen/shop/dao/mapper/TzOrderPurchaseMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO tz_order_purchase (id, purchase_order_no, buyer_id, buyer_type, order_status, order_date, service_fee, exchange_rate_snapshot, customer_goods_amount, customer_total_freight, customer_total_amount, payable_coupon_amount, payable_discount_amount, payable_goods_amount, payable_freight_total, payable_amount_total, delivery_address, postal_code, province, city, district, consignee_name, consignee_phone, supplier_count, line_item_count, completed_supplier_count, total_quantity, tenant_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, null)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
; Column 'tenant_id' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:97)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy132.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy165.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.TzOrderPurchaseRepositoryImpl$$SpringCGLIB$$0.save(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSavePurchaseOrder(OrderDataSyncServiceImpl.java:256)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:185)
	... 112 common frames omitted
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy228.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy227.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 132 common frames omitted
2025-07-30 15:52:05 DEBUG [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1102623423823126528]:[0] 订单数据获取完成: orderId=2835191931563540788, orderDetail=OrderDetailResponse.OrderDetail(baseInfo=TradeBaseInfo(id=2835191931563540788, idOfStr=2835191931563540788, businessType=cb, buyerId=b2b-2207416548807a4d12, completeTime=2025-07-30T15:21:50, createTime=2025-07-18T11:34:19, modifyTime=2025-07-30T15:21:50, refund=0, sellerId=b2b-*************76e59, shippingFee=0, status=success, totalAmount=650, discount=-16700, buyerContact=Contact(phone=86-752-2313067, fax=null, email=null, name=汤维政, imInPlatform=惠州中田贸易, companyName=惠州市中田贸易有限公司, mobile=***********, address=null), sellerContact=TradeSellContact(super=Contact(phone=86-, fax=null, email=null, name=杨红玉, imInPlatform=大唐彩印包装, companyName=临沂精诚纸制品有限公司, mobile=***********, address=null), shopName=临沂精诚纸制品有限公司, wgSenderPhone=null, wgSenderName=null), tradeType=50060, refundPayment=0, allDeliveredTime=2025-07-20T15:21:37, payTime=2025-07-18T16:23:20, receivingTime=2025-07-30T15:21:48.816, alipayTradeId=2025071822001846301400514153, sumProductPayment=860, flowTemplateCode=assureTrade, sellerOrder=false, buyerLoginId=惠州中田贸易, sellerLoginId=大唐彩印包装, closeOperateType=, couponFee=0, receiverInfo=TradeReceiverInfo(toFullName=崔磊, toDivisionCode=330113, toPost=516000, toArea=浙江省 杭州市 钱塘区 下沙街道 益丰路55号3栋3楼杭州速麦科技有限公司, toProvince=null, toCity=null, toCounty=null, toAddress=null, toMobile=15867157301, toPhone=, toEmail=null, toTownCode=*********), tradeTypeDesc=担保交易, payChannelList=[支付宝], tradeTypeCode=assureTrade, payTimeout=432000, payTimeoutType=0, payChannelCodeList=[alipay], outOrderId=10808, stepPayAll=false, stepOrderList=null, newStepOrderList=[TradeBaseInfo.NewStepOrder(gmtStart=2025-07-18T11:34:20, gmtPay=2025-07-18T16:23:20, gmtEnd=2025-07-30T15:21:50, stepNo=1, lastStep=true, stepName=全款交易, activeStatus=1, payStatus=6, logisticsStatus=3, payFee=650, paidFee=650, goodsFee=null, adjustFee=-167, discountFee=43, postFee=0, paidPostFee=0)], overSeaOrder=false, sellerCreditLevel=null, buyerFeedback=null, subBuyerLoginId=null, closeReason=null, sellerAlipayId=null, buyerUserId=null, buyerMemo=null, buyerRemarkIcon=null, refundStatus=null, remark=null, preOrderId=null, confirmedTime=null, closeRemark=null, stepAgreementPath=null, refundStatusForAs=null, sellerUserId=null, buyerAlipayId=null, refundId=null, inventoryMode=null), orderBizInfo=TradeOrderBizInfo(odsCyd=false, creditOrderDetail=null, preOrderInfo=null, lstOrderInfo=null, accountPeriodTime=null, creditOrder=false, dropShipping=false, erpBuyerUserId=null, erpOrderId=null, erpBuyerOrgId=null, isCz=null, isDz=null, dz=null, dropshipping=false, shippingInsurance=givenByAnXinGou, hyperLinkCangFaOrder=null, hyperLinkOrder=null, hyperLinkSecondStepOrder=null, hyperLinkShipType=null, lightningWarehouse=null, aeDoorPickUp=null), tradeTerms=[TradeTermsInfo(payStatus=6, payTime=2025-07-18T16:23:20, payWay=13, phasAmount=650, phase=*****************, phaseCondition=null, phaseDate=null, cardPay=false, expressPay=false, payWayDesc=支付平台)], productItems=[TradeProductItem(cargoNumber=********, description=null, itemAmount=650, name=外卖餐盒腰封定制厂家销售免费设计加厚材质印刷打包盒腰封定制, price=0.4, productId=************, productImgUrl=[http://cbu01.alicdn.com/img/ibank/O1CN01LWVGm21Bs32WmLLOc_!!0-0-cib.80x80.jpg, http://cbu01.alicdn.com/img/ibank/O1CN01LWVGm21Bs32WmLLOc_!!0-0-cib.jpg], productSnapshotUrl=https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2835191931563540788, quantity=2150, refund=0, skuId=*************, sort=null, status=success, subItemId=2835191931563540788, type=common, unit=个, weight=null, weightUnit=null, productCargoNumber=002腰封, skuInfos=[TradeProductItem.TradeSkuInfo(name=规格, value=客户自选)], entryDiscount=-16700, specId=0e6bebc8816dc96a6407ef35916793eb, quantityFactor=1, statusStr=交易成功, refundStatus=null, refundId=null, refundIdForAs=null, subItemIdString=2835191931563540788, closeReason=null, categoryId=null, unitPrice=null, logisticsStatus=3, gmtCreate=2025-07-18T11:34:19, gmtModified=2025-07-30T15:21:50, gmtCompleted=2025-07-30T15:21:50, gmtPayExpireTime=null, sharePostage=null, guaranteesTerms=[TradeGuaranteeTermsInfo(assuranceInfo=“满足相应条件时，用户在退货寄出后，享受极速退款到账。, assuranceType=lsjst_s, qualityAssuranceType=极速退款, value=null), TradeGuaranteeTermsInfo(assuranceInfo=卖家在承诺买家保障服务的基础上，自愿选择向买家提供“交期保障”服务。卖家向买家承诺，买家通过在线交易方式成功下单付款后，卖家将在双方确定的交期内（不可抗力因素除外），发送买家所购实物商品的服务，否则向买家进行赔付, assuranceType=jqbz, qualityAssuranceType=交期保障, value=7)])], nativeLogistics=TradeNativeLogisticsInfo(address=益丰路55号3栋3楼杭州速麦科技有限公司, area=钱塘区, areaCode=330113, city=杭州市, contactPerson=崔磊, fax=null, mobile=15867157301, province=浙江省, telephone=, zip=516000, logisticsItems=[TradeNativeLogisticsInfo.LogisticsItem(id=235362218991462, deliveredTime=2025-07-20T15:21:37, logisticsCode=LP00749032886378, type=0, status=alreadysend, gmtModified=2025-07-22T02:54:35, gmtCreate=2025-07-20T15:21:36, carriage=null, fromProvince=山东省, fromCity=临沂市, fromArea=兰山区, fromAddress=兰山街道 宏达路与大山路交汇兰田国际, fromPhone=86-, fromMobile=***********, fromPost=000000, logisticsCompanyId=3, logisticsCompanyNo=ZTO, logisticsCompanyName=中通快递(ZTO), logisticsBillNo=78925093885449, subItemIds=2835191931563540788, toProvince=null, toCity=杭州市, toArea=钱塘区, toAddress=下沙街道 益丰路55号3栋3楼杭州速麦科技有限公司, toPhone=null, toMobile=null, toPost=516000, noLogisticsName=, noLogisticsTel=, noLogisticsBillNo=, noLogisticsCondition=-1)], townCode=*********, town=下沙街道), orderInvoiceInfo=null, guaranteesTerms=null, orderRateInfo=TradeOrderRateInfo(buyerRateStatus=5, sellerRateStatus=5, buyerRateList=null, sellerRateList=null), overseasExtraAddress=null, customs=null, quoteList=null, extAttributes=[], fromEncryptOrder=false, encryptOutOrderInfo=null, overseaLogisticsInfo=null), wmsOrderDetails=[WmsPurchaseOrderDetailsRes(nayaPurchaseNo=, purchaseNo=C108082025071811341800008, shopOrderId=, sellerOpenId=BBBbozC9TtTIwXge8zWeJzR1A, cusCode=10808, storeId=536, outTradeNo=, orderId=2835191931563540788, customerId=null, createUser=10808, createTime=2025-07-18T11:34:18.377, remark=Alibaba.1688.com, platform=, payType=BALANCE, payUrl=https://trade.1688.com/order/cashier.htm?orderId=2835191931563540788, payBody=, platformPayType=诚e赊,跨境宝2.0,支付宝,其它,其它, shippingFee=0.000, originalShippingFee=0.000, productOriginalTotalAmount=860.000, total=860.000, serviceFee=8.600, productSalesTotalAmount=860.000, productFinalTotalAmount=650.000, alibabaTotalAmount=817.000, alibabaFinalAmount=650.000, finalShoppingFee=null, originalTotalPrice=868.600, discount=0.000, plusDiscount=43.000, couponDiscount=0.000, totalAmount=868.600, status=PENDING_SIGNATURE, isRequestQuote=true, paymentTime=2025-07-18T16:23:32.750, shippingTime=2025-07-21T08:03:44.977, completeTime=1900-01-01T00:00, link=, trackingNo=78925093885449, platformStatus=, platformRemark=, orderDetailsItemRes=[WmsPurchaseOrderDetailsItemRes(skuId=null, enName=Takeaway lunch box waist seal customization factory sales free design thick material printing packing box waist seal customization, cnName=外卖餐盒腰封定制厂家销售免费设计加厚材质印刷打包盒腰封定制, weight=0.000, quantity=2150, variantId=0e6bebc8816dc96a6407ef35916793eb, productId=************, unitPrice=0.400, originUnitPrice=0.400, finalUnitPrice=0.302, subTotal=860.000, originSubTotalAmount=860.000, finalSubTotalAmount=650.000, imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN011saB8g2AmitT79kkz_!!*************-0-cib.jpg, productUrl=null, attributes=null, skuAttrib=规格:客户自选;, skuAttribEn=Specifications:Customer Choice;)])]
2025-07-30 15:52:05 DEBUG [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1102623423823126528]:[0] 开始检查订单数据完整性: orderId=2835191931563540788
2025-07-30 15:52:05 INFO  [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1102623423823126528]:[0] 订单数据完整性检查结果: orderId=2835191931563540788, isComplete=false, isNewVersion=false, missing=采购订单, 供应商订单, 订单项
2025-07-30 15:52:05 INFO  [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1102623423823126528]:[0] 开始同步和补齐订单数据: orderId=2835191931563540788, needsSync=true
2025-07-30 15:52:05 INFO  [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1102623423823126528]:[0] 开始补齐缺失的订单数据: orderId=2835191931563540788, missing=采购订单, 供应商订单, 订单项
2025-07-30 15:52:05 ERROR [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1102623423823126528]:[0] 事务执行过程中发生异常: orderId=2835191931563540788
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
### The error may exist in com/fulfillmen/shop/dao/mapper/TzOrderPurchaseMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO tz_order_purchase (id, purchase_order_no, buyer_id, buyer_type, order_status, order_date, service_fee, exchange_rate_snapshot, customer_goods_amount, customer_total_freight, customer_total_amount, payable_coupon_amount, payable_discount_amount, payable_goods_amount, payable_freight_total, payable_amount_total, delivery_address, postal_code, province, city, district, consignee_name, consignee_phone, supplier_count, line_item_count, completed_supplier_count, total_quantity, tenant_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, null)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
; Column 'tenant_id' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:97)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy132.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy165.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.TzOrderPurchaseRepositoryImpl$$SpringCGLIB$$0.save(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSavePurchaseOrder(OrderDataSyncServiceImpl.java:256)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:185)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:174)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:158)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:93)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:118)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:39)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:86)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy228.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy227.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 132 common frames omitted
2025-07-30 15:52:05 ERROR [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1102623423823126528]:[0] 订单数据补齐失败: orderId=2835191931563540788
java.lang.RuntimeException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:225)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:174)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:158)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:93)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:118)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:39)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:86)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
### The error may exist in com/fulfillmen/shop/dao/mapper/TzOrderPurchaseMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO tz_order_purchase (id, purchase_order_no, buyer_id, buyer_type, order_status, order_date, service_fee, exchange_rate_snapshot, customer_goods_amount, customer_total_freight, customer_total_amount, payable_coupon_amount, payable_discount_amount, payable_goods_amount, payable_freight_total, payable_amount_total, delivery_address, postal_code, province, city, district, consignee_name, consignee_phone, supplier_count, line_item_count, completed_supplier_count, total_quantity, tenant_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, null)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
; Column 'tenant_id' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:97)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy132.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy165.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.TzOrderPurchaseRepositoryImpl$$SpringCGLIB$$0.save(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSavePurchaseOrder(OrderDataSyncServiceImpl.java:256)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:185)
	... 112 common frames omitted
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy228.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy227.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 132 common frames omitted
2025-07-30 15:52:05 ERROR [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1102623423823126528]:[0] 订单webhook消息处理失败: orderId=2835191931563540788, msgId=142678008946, messageType=ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS, error=订单数据补齐失败
java.lang.RuntimeException: 订单数据补齐失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:230)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:158)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:93)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:118)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:39)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:86)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:225)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:174)
	... 110 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
### The error may exist in com/fulfillmen/shop/dao/mapper/TzOrderPurchaseMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO tz_order_purchase (id, purchase_order_no, buyer_id, buyer_type, order_status, order_date, service_fee, exchange_rate_snapshot, customer_goods_amount, customer_total_freight, customer_total_amount, payable_coupon_amount, payable_discount_amount, payable_goods_amount, payable_freight_total, payable_amount_total, delivery_address, postal_code, province, city, district, consignee_name, consignee_phone, supplier_count, line_item_count, completed_supplier_count, total_quantity, tenant_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, null)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
; Column 'tenant_id' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:97)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy132.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy165.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.TzOrderPurchaseRepositoryImpl$$SpringCGLIB$$0.save(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSavePurchaseOrder(OrderDataSyncServiceImpl.java:256)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:185)
	... 112 common frames omitted
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy228.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy227.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 132 common frames omitted
2025-07-30 15:52:05 ERROR [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1102623423823126528]:[0] 发布订单处理失败事件: orderId=2835191931563540788, error=订单数据补齐失败
2025-07-30 15:52:05 ERROR [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - [1102623423823126528]:[0] 订单webhook消息处理失败: msgId=142678008946, type=ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS, orderId=2835191931563540788, error=订单数据补齐失败
java.lang.RuntimeException: 订单数据补齐失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:230)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:158)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:93)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:118)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:39)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:86)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:225)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:174)
	... 110 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
### The error may exist in com/fulfillmen/shop/dao/mapper/TzOrderPurchaseMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO tz_order_purchase (id, purchase_order_no, buyer_id, buyer_type, order_status, order_date, service_fee, exchange_rate_snapshot, customer_goods_amount, customer_total_freight, customer_total_amount, payable_coupon_amount, payable_discount_amount, payable_goods_amount, payable_freight_total, payable_amount_total, delivery_address, postal_code, province, city, district, consignee_name, consignee_phone, supplier_count, line_item_count, completed_supplier_count, total_quantity, tenant_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, null)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
; Column 'tenant_id' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:97)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy132.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy165.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.TzOrderPurchaseRepositoryImpl$$SpringCGLIB$$0.save(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSavePurchaseOrder(OrderDataSyncServiceImpl.java:256)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:185)
	... 112 common frames omitted
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy228.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy227.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 132 common frames omitted
2025-07-30 15:52:05 ERROR [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.s.alibaba.webhook.AbstractTypedMessageHandler - [1102623423823126528]:[0] 消息处理异常: msgId=142678008946, type=ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS
java.lang.RuntimeException: 订单数据补齐失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:230)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:158)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:93)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:118)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:39)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:86)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:225)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:174)
	... 110 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
### The error may exist in com/fulfillmen/shop/dao/mapper/TzOrderPurchaseMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO tz_order_purchase (id, purchase_order_no, buyer_id, buyer_type, order_status, order_date, service_fee, exchange_rate_snapshot, customer_goods_amount, customer_total_freight, customer_total_amount, payable_coupon_amount, payable_discount_amount, payable_goods_amount, payable_freight_total, payable_amount_total, delivery_address, postal_code, province, city, district, consignee_name, consignee_phone, supplier_count, line_item_count, completed_supplier_count, total_quantity, tenant_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, null)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
; Column 'tenant_id' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:97)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy132.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy165.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.TzOrderPurchaseRepositoryImpl$$SpringCGLIB$$0.save(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSavePurchaseOrder(OrderDataSyncServiceImpl.java:256)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:185)
	... 112 common frames omitted
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy228.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy227.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 132 common frames omitted
2025-07-30 15:52:05 INFO  [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1102623423823126528]:[0] Webhook消息处理完成，处理时间: 9991ms，处理结果数量: 1
2025-07-30 15:52:05 INFO  [XNIO-1 task-3] [tid::uId::ip:************:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1102623423823126528]:[0] [POST] /alibaba/callback 200 9994ms
2025-07-30 15:52:03 ERROR [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1102623362288492544]:[0] 订单webhook消息处理失败: orderId=2854362723888540788, msgId=142683171677, messageType=ORDER_BUYER_VIEW_BUYER_MAKE, error=订单数据补齐失败
java.lang.RuntimeException: 订单数据补齐失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:230)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:158)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:93)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:118)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:39)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:86)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:225)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:174)
	... 110 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
### The error may exist in com/fulfillmen/shop/dao/mapper/TzOrderPurchaseMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO tz_order_purchase (id, purchase_order_no, buyer_id, buyer_type, order_status, order_date, service_fee, exchange_rate_snapshot, customer_goods_amount, customer_total_freight, customer_total_amount, payable_coupon_amount, payable_discount_amount, payable_goods_amount, payable_freight_total, payable_amount_total, delivery_address, postal_code, province, city, district, consignee_name, consignee_phone, supplier_count, line_item_count, completed_supplier_count, total_quantity, tenant_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, null)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
; Column 'tenant_id' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:97)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy132.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy165.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.TzOrderPurchaseRepositoryImpl$$SpringCGLIB$$0.save(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSavePurchaseOrder(OrderDataSyncServiceImpl.java:256)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:185)
	... 112 common frames omitted
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy228.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy227.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 132 common frames omitted
2025-07-30 15:52:05 ERROR [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1102623362288492544]:[0] 发布订单处理失败事件: orderId=2854362723888540788, error=订单数据补齐失败
2025-07-30 15:52:05 ERROR [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - [1102623362288492544]:[0] 订单webhook消息处理失败: msgId=142683171677, type=ORDER_BUYER_VIEW_BUYER_MAKE, orderId=2854362723888540788, error=订单数据补齐失败
java.lang.RuntimeException: 订单数据补齐失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:230)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:158)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:93)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:118)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:39)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:86)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:225)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:174)
	... 110 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
### The error may exist in com/fulfillmen/shop/dao/mapper/TzOrderPurchaseMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO tz_order_purchase (id, purchase_order_no, buyer_id, buyer_type, order_status, order_date, service_fee, exchange_rate_snapshot, customer_goods_amount, customer_total_freight, customer_total_amount, payable_coupon_amount, payable_discount_amount, payable_goods_amount, payable_freight_total, payable_amount_total, delivery_address, postal_code, province, city, district, consignee_name, consignee_phone, supplier_count, line_item_count, completed_supplier_count, total_quantity, tenant_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, null)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
; Column 'tenant_id' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:97)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy132.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy165.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.TzOrderPurchaseRepositoryImpl$$SpringCGLIB$$0.save(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSavePurchaseOrder(OrderDataSyncServiceImpl.java:256)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:185)
	... 112 common frames omitted
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy228.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy227.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 132 common frames omitted
2025-07-30 15:52:05 ERROR [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.s.alibaba.webhook.AbstractTypedMessageHandler - [1102623362288492544]:[0] 消息处理异常: msgId=142683171677, type=ORDER_BUYER_VIEW_BUYER_MAKE
java.lang.RuntimeException: 订单数据补齐失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:230)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:158)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:93)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:118)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:39)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:86)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:225)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:174)
	... 110 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
### The error may exist in com/fulfillmen/shop/dao/mapper/TzOrderPurchaseMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO tz_order_purchase (id, purchase_order_no, buyer_id, buyer_type, order_status, order_date, service_fee, exchange_rate_snapshot, customer_goods_amount, customer_total_freight, customer_total_amount, payable_coupon_amount, payable_discount_amount, payable_goods_amount, payable_freight_total, payable_amount_total, delivery_address, postal_code, province, city, district, consignee_name, consignee_phone, supplier_count, line_item_count, completed_supplier_count, total_quantity, tenant_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, null)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
; Column 'tenant_id' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:97)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy132.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy165.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.TzOrderPurchaseRepositoryImpl$$SpringCGLIB$$0.save(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSavePurchaseOrder(OrderDataSyncServiceImpl.java:256)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$0(OrderDataSyncServiceImpl.java:185)
	... 112 common frames omitted
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'tenant_id' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy228.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy227.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 132 common frames omitted
2025-07-30 15:52:05 INFO  [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1102623362288492544]:[0] Webhook消息处理完成，处理时间: 30113ms，处理结果数量: 1
2025-07-30 15:52:05 INFO  [XNIO-1 task-2] [tid::uId::ip:***********:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1102623362288492544]:[0] [POST] /alibaba/callback 200 30131ms
2025-07-30 15:55:56 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-30 15:55:56 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-30 15:55:56 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-30 15:55:56 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-30 15:55:56 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-30 15:55:56 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-30 15:55:56 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-30 15:55:56 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-30 15:55:56 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-30 15:55:56 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-30 15:55:56 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-30 15:55:56 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-30 15:55:56 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-30 15:55:56 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-30 15:55:56 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-30 15:55:56 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-30 15:55:56 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-30 15:55:56 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-30 15:55:56 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-30 15:55:56 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=7, lastTimeStamp=1753862156187}] instanceId:[InstanceId{instanceId=**************:96107, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-30 15:55:56 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-30 15:55:56 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-30 15:55:56 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-30 15:55:56 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-30 15:55:56 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-30 15:55:58 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-30 15:55:58 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-30 15:55:58 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-30 15:55:58 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
