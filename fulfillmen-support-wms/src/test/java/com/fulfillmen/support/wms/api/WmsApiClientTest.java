/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.api;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fulfillmen.support.wms.dto.common.WmsApiResponse;
import com.fulfillmen.support.wms.dto.common.WmsPageDTO;
import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.enums.WmsPayTypeEnum;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateGoodsReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateOrderReq;
import com.fulfillmen.support.wms.dto.request.WmsOrderQueryReq;
import com.fulfillmen.support.wms.dto.request.WmsProductInfoReq;
import com.fulfillmen.support.wms.dto.request.WmsProductQueryReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderReq;
import com.fulfillmen.support.wms.dto.response.WmsAccountInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsCreatePurchaseOrderRes;
import com.fulfillmen.support.wms.dto.response.WmsProductRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderInfoRes;
import com.fulfillmen.support.wms.exception.WmsApiException;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;

/**
 * WmsApiClient Mock测试类
 * 
 * <p>使用Mockito进行单元测试，测试WmsApiClient的所有方法，
 * 包括正常流程、异常情况、边界条件等各种场景。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@ExtendWith(MockitoExtension.class)
class WmsApiClientTest {

    @Mock
    private WmsFulfillmenAPI wmsFulfillmenAPI;

    private WmsApiClient wmsApiClient;

    @BeforeEach
    void setUp() {
        wmsApiClient = new WmsApiClient(wmsFulfillmenAPI);
    }

    // ==================== 账户管理相关API测试 ====================

    @Test
    void testGetAccountInfoByAuthCode_Success() {
        // 准备测试数据
        String authCode = "dGVzdC1hdXRoLWNvZGU=";
        WmsAccountInfoRes expectedData = WmsAccountInfoRes.builder()
            .customerId(10002)
            .customerName("测试客户")
            .customerCode("TEST001")
            .accountMoney(BigDecimal.valueOf(1000.0))
            .email("<EMAIL>")
            .country("中国")
            .province("广东省")
            .city("深圳市")
            .address("测试地址")
            .mobile("***********")
            .phone("0755-********")
            .status("1")
            .serviceFeeRate(BigDecimal.valueOf(0.05))
            .creditLimit(BigDecimal.valueOf(5000.0))
            .createTime(LocalDateTime.now())
            .build();

        WmsApiResponse<WmsAccountInfoRes> mockResponse = createSuccessResponse(expectedData);

        // Mock API调用和响应处理
        when(wmsFulfillmenAPI.getAccountInfoByAuthCode(authCode))
            .thenReturn(Mono.just(mockResponse));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            // 执行测试
            WmsAccountInfoRes result = wmsApiClient.getAccountInfoByAuthCode(authCode);

            // 验证结果
            assertNotNull(result);
            assertEquals(expectedData.getCustomerId(), result.getCustomerId());
            assertEquals(expectedData.getCustomerName(), result.getCustomerName());
            assertEquals(expectedData.getCustomerCode(), result.getCustomerCode());
            assertEquals(expectedData.getAccountMoney(), result.getAccountMoney());

            // 验证Mock调用
            verify(wmsFulfillmenAPI).getAccountInfoByAuthCode(authCode);
            mockedHandler.verify(() -> WmsResponseHandler.handleWmsResponse(any(), eq("WMS获取账户信息API"), eq(authCode)));
        }
    }

    @Test
    void testGetAccountInfoByAuthCode_WmsApiException() {
        String authCode = "invalid-auth-code";
        WmsApiException expectedException = new WmsApiException("WMS获取账户信息API调用失败");

        when(wmsFulfillmenAPI.getAccountInfoByAuthCode(authCode))
            .thenReturn(Mono.just(createFailureResponse()));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenThrow(expectedException);

            // 执行测试并验证异常
            WmsApiException exception = assertThrows(WmsApiException.class,
                () -> wmsApiClient.getAccountInfoByAuthCode(authCode));

            assertEquals(expectedException.getMessage(), exception.getMessage());
            verify(wmsFulfillmenAPI).getAccountInfoByAuthCode(authCode);
        }
    }

    @Test
    void testGetAccountInfoByAuthCode_UnexpectedException() {
        String authCode = "test-auth-code";
        RuntimeException unexpectedException = new RuntimeException("网络异常");

        when(wmsFulfillmenAPI.getAccountInfoByAuthCode(authCode))
            .thenReturn(Mono.error(unexpectedException));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenThrow(new WmsApiException("WMS获取账户信息API调用失败", unexpectedException));

            // 执行测试并验证异常
            WmsApiException exception = assertThrows(WmsApiException.class,
                () -> wmsApiClient.getAccountInfoByAuthCode(authCode));

            assertEquals("WMS获取账户信息API调用失败", exception.getMessage());
            assertEquals(unexpectedException, exception.getCause());
        }
    }

    @Test
    void testGetAccountInfoByCustomerCode_Success() {
        String customerCode = "TEST001";
        WmsAccountInfoRes expectedData = WmsAccountInfoRes.builder()
            .customerCode(customerCode)
            .customerName("测试客户")
            .accountMoney(BigDecimal.valueOf(1500.0))
            .build();

        when(wmsFulfillmenAPI.getAccountInfoByCustomerCode(customerCode))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            WmsAccountInfoRes result = wmsApiClient.getAccountInfoByCustomerCode(customerCode);

            assertNotNull(result);
            assertEquals(customerCode, result.getCustomerCode());
            assertEquals("测试客户", result.getCustomerName());
            assertEquals(BigDecimal.valueOf(1500.0), result.getAccountMoney());

            verify(wmsFulfillmenAPI).getAccountInfoByCustomerCode(customerCode);
        }
    }

    // ==================== 采购订单相关API测试 ====================
//
//    @Test
//    void testDeductAccountInfoByPurchase_Success() {
//        DeductAccountReq request = DeductAccountReq.builder()
//            .cusCode("TEST001")
//            .orderNo("ORDER001")
//            .totalAmount(100.00)
//            .productAmount(80.00)
//            .serviceFee(20.00)
//            .payType(WmsPayTypeEnum.BALANCE)
//            .build();
//
//        WmsAccountChargeInfoRes expectedData = new WmsAccountChargeInfoRes();
//        expectedData.setCusCode("TEST001");
//        expectedData.setOrderNo("ORDER001");
//        expectedData.setPaymentNumber("PAY001");
//        expectedData.setBalanceAfterCharge(900.00);
////        expectedData.setChargedAmount(100.00);
//        expectedData.setPaymentDetailId(12345);
//
//        when(wmsFulfillmenAPI.deductAccountInfoByPurchase(request))
//            .thenReturn(Mono.just(createSuccessResponse(expectedData)));
//
//        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
//            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
//                .thenReturn(expectedData);
//
//            WmsAccountChargeInfoRes result = wmsApiClient.deductAccountInfoByPurchase(request);
//
//            assertNotNull(result);
//            assertEquals("TEST001", result.getCusCode());
//            assertEquals("ORDER001", result.getOrderNo());
//            assertEquals("PAY001", result.getPaymentNumber());
//            assertEquals(900.00, result.getBalanceAfterCharge());
//            assertEquals(100.00, result.getChargedAmount());
//            assertEquals(Integer.valueOf(12345), result.getPaymentDetailId());
//
//            verify(wmsFulfillmenAPI).deductAccountInfoByPurchase(request);
//        }
//    }
//
//    @Test
//    void testCreatePurchaseOrder_Deprecated_Success() {
//        WmsCreateOrderReq request = WmsCreateOrderReq.builder()
//            .customerCode("TEST001")
//            .orders(Arrays.asList(
//                createMockPurchaseOrderReq("PO001"),
//                createMockPurchaseOrderReq("PO002")
//            ))
//            .build();
//
//        WmsPurchaseDataRes expectedData = new WmsPurchaseDataRes();
//        // 设置预期的响应数据...
//
//        when(wmsFulfillmenAPI.createPurchaseOrder(request))
//            .thenReturn(Mono.just(createSuccessResponse(expectedData)));
//
//        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
//            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
//                .thenReturn(expectedData);
//
//            WmsPurchaseDataRes result = wmsApiClient.createPurchaseOrder(request);
//
//            assertNotNull(result);
//            verify(wmsFulfillmenAPI).createPurchaseOrder(request);
//        }
//    }

    @Test
    void testCreatePurchaseOrderNew_Success() {
        WmsCreateOrderReq request = WmsCreateOrderReq.builder()
            .customerCode("TEST001")
            .orders(Arrays.asList(
                createMockPurchaseOrderReq("PO001"),
                createMockPurchaseOrderReq("PO002")
            ))
            .build();

        List<WmsCreatePurchaseOrderRes> expectedData = Arrays.asList(
            createMockPurchaseOrderRes("PO001", "SHOP001"),
            createMockPurchaseOrderRes("PO002", "SHOP002")
        );

        when(wmsFulfillmenAPI.createPurchaseOrderNew(request))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            List<WmsCreatePurchaseOrderRes> result = wmsApiClient.createPurchaseOrderNew(request);

            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals("PO001", result.get(0).getPurchaseNo());
            assertEquals("SHOP001", result.get(0).getShopOrderId());

            verify(wmsFulfillmenAPI).createPurchaseOrderNew(request);
        }
    }

    @Test
    void testUpdatePurchaseOrder_Success() {
        List<WmsPurchaseOrderDetailReq> request = Arrays.asList(
            WmsPurchaseOrderDetailReq.builder()
                .purchaseNo("PO001")
                .status(WmsOrderStatusEnum.PURCHASED_PENDING_SHIPMENT)
                .build(),
            WmsPurchaseOrderDetailReq.builder()
                .purchaseNo("PO002")
                .status(WmsOrderStatusEnum.SHIPPED_PENDING_RECEIPT)
                .build()
        );

        when(wmsFulfillmenAPI.updatePurchaseOrderDetails(request))
            .thenReturn(Mono.just(createSuccessVoidResponse()));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleVoidWmsResponse(any(), anyString(), any()))
                .thenAnswer(invocation -> null); // void method

            // 执行测试
            assertDoesNotThrow(() -> wmsApiClient.updatePurchaseOrder(request));

            verify(wmsFulfillmenAPI).updatePurchaseOrderDetails(request);
            mockedHandler.verify(() -> WmsResponseHandler.handleVoidWmsResponse(any(), eq("WMS更新采购订单API"), eq(request)));
        }
    }

    @Test
    void testQueryOrder_Success() {
        WmsOrderQueryReq request = WmsOrderQueryReq.builder()
            .page(1)
            .pageSize(10)
            .includeDetails(true)
            .build();

        List<WmsPurchaseOrderInfoRes> orderList = Arrays.asList(
            createMockPurchaseOrderInfoRes("PO001"),
            createMockPurchaseOrderInfoRes("PO002")
        );

        WmsPageDTO<WmsPurchaseOrderInfoRes> expectedData = WmsPageDTO.<WmsPurchaseOrderInfoRes>builder()
            .total(2)
            .pageSize(10)
            .pageIndex(1)
            .totalPages(1)
            .records(orderList)
            .build();

        when(wmsFulfillmenAPI.queryOrder(request))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            WmsPageDTO<WmsPurchaseOrderInfoRes> result = wmsApiClient.queryOrder(request);

            assertNotNull(result);
            assertEquals(2, result.getTotal());
            assertEquals(10, result.getPageSize());
            assertEquals(1, result.getPageIndex());
            assertEquals(2, result.getRecords().size());

            verify(wmsFulfillmenAPI).queryOrder(request);
        }
    }

    @Test
    void testQueryOrderDetail_Success() {
        PurchaseOrderDetailReq request = PurchaseOrderDetailReq.builder()
            .purchaseNo("PO001,PO002")
            .orderId("1688001,1688002")
            .nayaPurchaseNo("WMS001,WMS002")
            .build();

        List<WmsPurchaseOrderDetailsRes> expectedData = Arrays.asList(
            createMockPurchaseOrderDetailsRes("PO001"),
            createMockPurchaseOrderDetailsRes("PO002")
        );

        when(wmsFulfillmenAPI.queryOrderDetails("PO001,PO002", "1688001,1688002", "WMS001,WMS002"))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            List<WmsPurchaseOrderDetailsRes> result = wmsApiClient.queryOrderDetail(request);

            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals("PO001", result.get(0).getPurchaseNo());

            verify(wmsFulfillmenAPI).queryOrderDetails("PO001,PO002", "1688001,1688002", "WMS001,WMS002");
        }
    }

    // ==================== 产品管理相关API测试 ====================

    @Test
    void testCreateProduct_Success() {
        WmsCreateGoodsReq request = WmsCreateGoodsReq.builder()
            .cusCode("TEST001")
            .sku("TEST-SKU-001")
            .cnName("测试产品")
            .enName("Test Product")
            .price(99.99)
            .weight(1.5)
            .build();

        when(wmsFulfillmenAPI.createProduct(request))
            .thenReturn(Mono.just(createSuccessVoidResponse()));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleVoidWmsResponse(any(), anyString(), any()))
                .thenAnswer(invocation -> null); // void method

            assertDoesNotThrow(() -> wmsApiClient.createProduct(request));

            verify(wmsFulfillmenAPI).createProduct(request);
            mockedHandler.verify(() -> WmsResponseHandler.handleVoidWmsResponse(any(), eq("WMS创建产品API"), eq(request)));
        }
    }

    @Test
    void testGetProductList_Success() {
        Integer page = 1;
        String barcode = "********9";

        WmsPageDTO<WmsProductRes> expectedData = WmsPageDTO.<WmsProductRes>builder()
            .total(2)
            .pageSize(10)
            .pageIndex(1)
            .totalPages(1)
            .records(Arrays.asList(
                createMockProduct("SKU001", "产品1"),
                createMockProduct("SKU002", "产品2")
            ))
            .build();

        WmsApiResponse<WmsPageDTO<WmsProductRes>> mockResponse = createSuccessResponse(expectedData);

        // 根据实际接口定义，这个方法返回Mono<WmsApiResponse<List<WmsProduct>>>
        when(wmsFulfillmenAPI.getProductByPage(any(WmsProductQueryReq.class)))
            .thenReturn(Mono.just(mockResponse));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            WmsPageDTO<WmsProductRes> result = wmsApiClient.getProductList(page, barcode);

            assertNotNull(result);
            assertEquals(2, result.getRecords().size());
            assertEquals("SKU001", result.getRecords().get(0).getSku());
            assertEquals("产品1", result.getRecords().get(0).getCnName());

            verify(wmsFulfillmenAPI).getProductByPage(argThat(req -> req.getPage().equals(page) && req.getBarcode().equals(barcode)));
        }
    }

    @Test
    void testGetProductInfo_Success() {
        String cusCode = "TEST001";
        String offerId = "OFFER001";
        String sku = "SKU001";

        List<WmsProductRes> expectedData = Lists.newArrayList(createMockProduct(sku, "测试产品"));

        when(wmsFulfillmenAPI.getProductInfoList(cusCode, offerId, sku))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            List<WmsProductRes> result = wmsApiClient.getProductInfoList(WmsProductInfoReq.builder()
                .cusCode(cusCode)
                .offerId(offerId)
                .sku(sku)
                .build());

            assertNotNull(result);
            assertEquals(sku, result.get(0).getSku());
            assertEquals("测试产品", result.get(0).getCnName());

            verify(wmsFulfillmenAPI).getProductInfoList(cusCode, offerId, sku);
        }
    }

    // ==================== 边界条件和异常测试 ====================

    @Test
    void testGetAccountInfoByAuthCode_NullAuthCode() {
        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenThrow(new WmsApiException("授权码不能为空"));

            assertThrows(WmsApiException.class, () -> wmsApiClient.getAccountInfoByAuthCode(null));
        }
    }

    @Test
    void testGetAccountInfoByAuthCode_EmptyAuthCode() {
        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenThrow(new WmsApiException("授权码不能为空"));

            assertThrows(WmsApiException.class, () -> wmsApiClient.getAccountInfoByAuthCode(""));
        }
    }

    @Test
    void testUpdatePurchaseOrder_EmptyList() {
        List<WmsPurchaseOrderDetailReq> emptyList = new ArrayList<>();

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleVoidWmsResponse(any(), anyString(), any()))
                .thenAnswer(invocation -> null);

            assertDoesNotThrow(() -> wmsApiClient.updatePurchaseOrder(emptyList));
        }
    }

    @Test
    void testQueryOrder_InvalidPageParams() {
        WmsOrderQueryReq request = WmsOrderQueryReq.builder()
            .page(-1)  // 无效页码
            .pageSize(0)  // 无效页大小
            .build();

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenThrow(new WmsApiException("分页参数无效"));

            assertThrows(WmsApiException.class, () -> wmsApiClient.queryOrder(request));
        }
    }

    // ==================== 辅助方法 ====================

    private <T> WmsApiResponse<T> createSuccessResponse(T data) {
        WmsApiResponse<T> response = new WmsApiResponse<>();
        response.setSuccess("success");
        response.setCode(0);
        response.setMessage("成功");
        response.setData(data);
        return response;
    }

    private <T> WmsApiResponse<T> createFailureResponse() {
        WmsApiResponse<T> response = new WmsApiResponse<>();
        response.setSuccess("false");
        response.setCode(1);
        response.setMessage("API调用失败");
        return response;
    }

    private WmsApiResponse<Void> createSuccessVoidResponse() {
        WmsApiResponse<Void> response = new WmsApiResponse<>();
        response.setSuccess("success");
        response.setCode(0);
        response.setMessage("成功");
        return response;
    }

    private WmsPurchaseOrderReq createMockPurchaseOrderReq(String purchaseNo) {
        return WmsPurchaseOrderReq.builder()
            .purchaseNo(purchaseNo)
            .shopOrderId("SHOP" + purchaseNo.substring(2))
            .orderId(Long.valueOf(1688000 + Integer.parseInt(purchaseNo.substring(2))))
            .sellerOpenId("SELLER001")
            .storeId("STORE001")
            .total(BigDecimal.valueOf(100.0))
            .status(WmsOrderStatusEnum.PAID_PENDING_REVIEW)
            .payType(WmsPayTypeEnum.BALANCE)
            .createUser("testUser")
            .createTime(LocalDateTime.now())
            .build();
    }

    private WmsCreatePurchaseOrderRes createMockPurchaseOrderRes(String purchaseNo, String shopOrderId) {
        WmsCreatePurchaseOrderRes response = new WmsCreatePurchaseOrderRes();
        response.setPurchaseNo(purchaseNo);
        response.setShopOrderId(shopOrderId);
        return response;
    }

    private WmsPurchaseOrderInfoRes createMockPurchaseOrderInfoRes(String purchaseNo) {
        WmsPurchaseOrderInfoRes order = new WmsPurchaseOrderInfoRes();
        order.setPurchaseNo(purchaseNo);
        order.setShopOrderId("SHOP" + purchaseNo.substring(2));
        order.setOrderId(Long.valueOf(1688000 + Integer.parseInt(purchaseNo.substring(2))));
        order.setTotal(BigDecimal.valueOf(100.0));
        order.setStatus(WmsOrderStatusEnum.PAID_PENDING_REVIEW);
        order.setCreateTime(LocalDateTime.now());
        return order;
    }

    private WmsPurchaseOrderDetailsRes createMockPurchaseOrderDetailsRes(String purchaseNo) {
        WmsPurchaseOrderDetailsRes details = new WmsPurchaseOrderDetailsRes();
        details.setPurchaseNo(purchaseNo);
        details.setShopOrderId("SHOP" + purchaseNo.substring(2));
//        details.setSkuId("SKU" + purchaseNo.substring(2));
//        details.setCnName("测试产品" + purchaseNo.substring(2));
//        details.setEnName("Test Product " + purchaseNo.substring(2));
//        details.setQuantity(10);
//        details.setUnitPrice(BigDecimal.valueOf(10.0));
//        details.setSubTotal(BigDecimal.valueOf(100.0));
        return details;
    }

    private WmsProductRes createMockProduct(String sku, String cnName) {
        return WmsProductRes.builder()
            .sku(sku)
            .cnName(cnName)
            .enName("Test Product")
            .price(BigDecimal.valueOf(99.99))
            .weight(BigDecimal.valueOf(1.5))
            .barcode("********9" + sku.substring(3))
            .status(1)
            .createTime(LocalDateTime.now().toString())
            .build();
    }
}
