/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.api;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fulfillmen.support.wms.dto.common.WmsApiResponse;
import com.fulfillmen.support.wms.dto.common.WmsPageDTO;
import com.fulfillmen.support.wms.dto.enums.WmsOperationTypeEnum;
import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.enums.WmsPayTypeEnum;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsAccountChargeV2Req;
import com.fulfillmen.support.wms.dto.request.WmsCreateGoodsReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateInboundOrderReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateOrderReq;
import com.fulfillmen.support.wms.dto.request.WmsInboundInfoReq;
import com.fulfillmen.support.wms.dto.request.WmsInboundOrderUpdateReq;
import com.fulfillmen.support.wms.dto.request.WmsInboundQueryOrderPageReq;
import com.fulfillmen.support.wms.dto.request.WmsOrderQueryReq;
import com.fulfillmen.support.wms.dto.request.WmsProductInfoReq;
import com.fulfillmen.support.wms.dto.request.WmsProductQueryReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderReq;
import com.fulfillmen.support.wms.dto.response.WmsAccountChargeInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsAccountInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsCreateInboundOrderRes;
import com.fulfillmen.support.wms.dto.response.WmsCreatePurchaseOrderRes;
import com.fulfillmen.support.wms.dto.response.WmsInboundInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsInboundQueryPageRes;
import com.fulfillmen.support.wms.dto.response.WmsInboundUpdateRes;
import com.fulfillmen.support.wms.dto.response.WmsProductRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderInfoRes;
import com.fulfillmen.support.wms.exception.WmsApiException;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;

/**
 * WmsApiClient Mock测试类
 * 
 * <p>使用Mockito进行单元测试，测试WmsApiClient的所有方法，
 * 包括正常流程、异常情况、边界条件等各种场景。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@ExtendWith(MockitoExtension.class)
class WmsApiClientTest {

    @Mock
    private WmsFulfillmenAPI wmsFulfillmenAPI;

    private WmsApiClient wmsApiClient;

    @BeforeEach
    void setUp() {
        wmsApiClient = new WmsApiClient(wmsFulfillmenAPI);
    }

    // ==================== 账户管理相关API测试 ====================

    @Test
    void testGetAccountInfoByAuthCode_Success() {
        // 准备测试数据
        String authCode = "dGVzdC1hdXRoLWNvZGU=";
        WmsAccountInfoRes expectedData = WmsAccountInfoRes.builder()
            .customerId(10002)
            .customerName("测试客户")
            .customerCode("TEST001")
            .accountMoney(BigDecimal.valueOf(1000.0))
            .email("<EMAIL>")
            .country("中国")
            .province("广东省")
            .city("深圳市")
            .address("测试地址")
            .mobile("***********")
            .phone("0755-********")
            .status("1")
            .serviceFeeRate(BigDecimal.valueOf(0.05))
            .creditLimit(BigDecimal.valueOf(5000.0))
            .createTime(LocalDateTime.now())
            .build();

        WmsApiResponse<WmsAccountInfoRes> mockResponse = createSuccessResponse(expectedData);

        // Mock API调用和响应处理
        when(wmsFulfillmenAPI.getAccountInfoByAuthCode(authCode))
            .thenReturn(Mono.just(mockResponse));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            // 执行测试
            WmsAccountInfoRes result = wmsApiClient.getAccountInfoByAuthCode(authCode);

            // 验证结果
            assertNotNull(result);
            assertEquals(expectedData.getCustomerId(), result.getCustomerId());
            assertEquals(expectedData.getCustomerName(), result.getCustomerName());
            assertEquals(expectedData.getCustomerCode(), result.getCustomerCode());
            assertEquals(expectedData.getAccountMoney(), result.getAccountMoney());

            // 验证Mock调用
            verify(wmsFulfillmenAPI).getAccountInfoByAuthCode(authCode);
            mockedHandler.verify(() -> WmsResponseHandler.handleWmsResponse(any(), eq("WMS获取账户信息API"), eq(authCode)));
        }
    }

    @Test
    void testGetAccountInfoByAuthCode_WmsApiException() {
        String authCode = "invalid-auth-code";
        WmsApiException expectedException = new WmsApiException("WMS获取账户信息API调用失败");

        when(wmsFulfillmenAPI.getAccountInfoByAuthCode(authCode))
            .thenReturn(Mono.just(createFailureResponse()));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenThrow(expectedException);

            // 执行测试并验证异常
            WmsApiException exception = assertThrows(WmsApiException.class,
                () -> wmsApiClient.getAccountInfoByAuthCode(authCode));

            assertEquals(expectedException.getMessage(), exception.getMessage());
            verify(wmsFulfillmenAPI).getAccountInfoByAuthCode(authCode);
        }
    }

    @Test
    void testGetAccountInfoByAuthCode_UnexpectedException() {
        String authCode = "test-auth-code";
        RuntimeException unexpectedException = new RuntimeException("网络异常");

        when(wmsFulfillmenAPI.getAccountInfoByAuthCode(authCode))
            .thenReturn(Mono.error(unexpectedException));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenThrow(new WmsApiException("WMS获取账户信息API调用失败", unexpectedException));

            // 执行测试并验证异常
            WmsApiException exception = assertThrows(WmsApiException.class,
                () -> wmsApiClient.getAccountInfoByAuthCode(authCode));

            assertEquals("WMS获取账户信息API调用失败", exception.getMessage());
            assertEquals(unexpectedException, exception.getCause());
        }
    }

    @Test
    void testGetAccountInfoByCustomerCode_Success() {
        String customerCode = "TEST001";
        WmsAccountInfoRes expectedData = WmsAccountInfoRes.builder()
            .customerCode(customerCode)
            .customerName("测试客户")
            .accountMoney(BigDecimal.valueOf(1500.0))
            .build();

        when(wmsFulfillmenAPI.getAccountInfoByCustomerCode(customerCode))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            WmsAccountInfoRes result = wmsApiClient.getAccountInfoByCustomerCode(customerCode);

            assertNotNull(result);
            assertEquals(customerCode, result.getCustomerCode());
            assertEquals("测试客户", result.getCustomerName());
            assertEquals(BigDecimal.valueOf(1500.0), result.getAccountMoney());

            verify(wmsFulfillmenAPI).getAccountInfoByCustomerCode(customerCode);
        }
    }

    @Test
    void testDeductAccountInfoByPurchase_Success() {
        // 准备测试数据
        WmsAccountChargeV2Req request = WmsAccountChargeV2Req.builder()
            .cusCode("TEST001")
            .orderNo("ORDER20250131001")
            .operationType(WmsOperationTypeEnum.CHARGE)
            .productAmount(120.00)
            .serviceFee(30.00)
            .shippingFee(15.00)
            .originalTotalPrice(165.00)
            .createUser("testUser")
            .remark("单元测试扣减")
            .build();

        WmsAccountChargeInfoRes expectedData = new WmsAccountChargeInfoRes();
        expectedData.setCusCode("TEST001");
        expectedData.setOrderNo("ORDER20250131001");
        expectedData.setPaymentNumber("PAY20250131001");
        expectedData.setBalanceAfterCharge(BigDecimal.valueOf(835.00));
        expectedData.setPaymentDetailId(12345);

        when(wmsFulfillmenAPI.accountChargeByPurchase(request))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            // 执行测试
            WmsAccountChargeInfoRes result = wmsApiClient.deductAccountInfoByPurchase(request);

            // 验证结果
            assertNotNull(result);
            assertEquals("TEST001", result.getCusCode());
            assertEquals("ORDER20250131001", result.getOrderNo());
            assertEquals("PAY20250131001", result.getPaymentNumber());
            assertEquals(BigDecimal.valueOf(835.00), result.getBalanceAfterCharge());
            assertEquals(Integer.valueOf(12345), result.getPaymentDetailId());

            verify(wmsFulfillmenAPI).accountChargeByPurchase(request);
            mockedHandler.verify(() -> WmsResponseHandler.handleWmsResponse(any(), eq("WMS扣减账户余额API"), eq(request)));
        }
    }

    @Test
    void testRefundAccountInfoByPurchase_Success() {
        // 准备测试数据
        WmsAccountChargeV2Req request = WmsAccountChargeV2Req.builder()
            .cusCode("TEST001")
            .orderNo("ORDER20250131001")
            .operationType(WmsOperationTypeEnum.REFUND)
            .refundAmount(165.00)
            .refundReason("测试退款")
            .createUser("testUser")
            .remark("单元测试退款")
            .build();

        WmsAccountChargeInfoRes expectedData = new WmsAccountChargeInfoRes();
        expectedData.setCusCode("TEST001");
        expectedData.setOrderNo("ORDER20250131001");
        expectedData.setPaymentNumber("REF20250131001");
        expectedData.setBalanceAfterCharge(BigDecimal.valueOf(1165.00));
        expectedData.setPaymentDetailId(12346);

        when(wmsFulfillmenAPI.accountChargeByPurchase(request))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            // 执行测试
            WmsAccountChargeInfoRes result = wmsApiClient.refundAccountInfoByPurchase(request);

            // 验证结果
            assertNotNull(result);
            assertEquals("TEST001", result.getCusCode());
            assertEquals("ORDER20250131001", result.getOrderNo());
            assertEquals("REF20250131001", result.getPaymentNumber());
            assertEquals(BigDecimal.valueOf(1165.00), result.getBalanceAfterCharge());
            assertEquals(Integer.valueOf(12346), result.getPaymentDetailId());

            verify(wmsFulfillmenAPI).accountChargeByPurchase(request);
            mockedHandler.verify(() -> WmsResponseHandler.handleWmsResponse(any(), eq("WMS返还账户余额API"), eq(request)));
        }
    }

    @Test
    void testRefundAccountInfoByPurchase_WmsApiException() {
        WmsAccountChargeV2Req request = WmsAccountChargeV2Req.builder()
            .cusCode("INVALID001")
            .orderNo("INVALID_ORDER")
            .operationType(WmsOperationTypeEnum.REFUND)
            .refundAmount(100.00)
            .build();

        WmsApiException expectedException = new WmsApiException("WMS返还账户余额API调用失败");

        when(wmsFulfillmenAPI.accountChargeByPurchase(request))
            .thenReturn(Mono.just(createFailureResponse()));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenThrow(expectedException);

            // 执行测试并验证异常
            WmsApiException exception = assertThrows(WmsApiException.class,
                () -> wmsApiClient.refundAccountInfoByPurchase(request));

            assertEquals(expectedException.getMessage(), exception.getMessage());
            verify(wmsFulfillmenAPI).accountChargeByPurchase(request);
        }
    }

    // ==================== 采购订单相关API测试 ====================
//
//    @Test
//    void testDeductAccountInfoByPurchase_Success() {
//        DeductAccountReq request = DeductAccountReq.builder()
//            .cusCode("TEST001")
//            .orderNo("ORDER001")
//            .totalAmount(100.00)
//            .productAmount(80.00)
//            .serviceFee(20.00)
//            .payType(WmsPayTypeEnum.BALANCE)
//            .build();
//
//        WmsAccountChargeInfoRes expectedData = new WmsAccountChargeInfoRes();
//        expectedData.setCusCode("TEST001");
//        expectedData.setOrderNo("ORDER001");
//        expectedData.setPaymentNumber("PAY001");
//        expectedData.setBalanceAfterCharge(900.00);
////        expectedData.setChargedAmount(100.00);
//        expectedData.setPaymentDetailId(12345);
//
//        when(wmsFulfillmenAPI.deductAccountInfoByPurchase(request))
//            .thenReturn(Mono.just(createSuccessResponse(expectedData)));
//
//        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
//            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
//                .thenReturn(expectedData);
//
//            WmsAccountChargeInfoRes result = wmsApiClient.deductAccountInfoByPurchase(request);
//
//            assertNotNull(result);
//            assertEquals("TEST001", result.getCusCode());
//            assertEquals("ORDER001", result.getOrderNo());
//            assertEquals("PAY001", result.getPaymentNumber());
//            assertEquals(900.00, result.getBalanceAfterCharge());
//            assertEquals(100.00, result.getChargedAmount());
//            assertEquals(Integer.valueOf(12345), result.getPaymentDetailId());
//
//            verify(wmsFulfillmenAPI).deductAccountInfoByPurchase(request);
//        }
//    }
//
//    @Test
//    void testCreatePurchaseOrder_Deprecated_Success() {
//        WmsCreateOrderReq request = WmsCreateOrderReq.builder()
//            .customerCode("TEST001")
//            .orders(Arrays.asList(
//                createMockPurchaseOrderReq("PO001"),
//                createMockPurchaseOrderReq("PO002")
//            ))
//            .build();
//
//        WmsPurchaseDataRes expectedData = new WmsPurchaseDataRes();
//        // 设置预期的响应数据...
//
//        when(wmsFulfillmenAPI.createPurchaseOrder(request))
//            .thenReturn(Mono.just(createSuccessResponse(expectedData)));
//
//        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
//            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
//                .thenReturn(expectedData);
//
//            WmsPurchaseDataRes result = wmsApiClient.createPurchaseOrder(request);
//
//            assertNotNull(result);
//            verify(wmsFulfillmenAPI).createPurchaseOrder(request);
//        }
//    }

    @Test
    void testCreatePurchaseOrderNew_Success() {
        WmsCreateOrderReq request = WmsCreateOrderReq.builder()
            .customerCode("TEST001")
            .orders(Arrays.asList(
                createMockPurchaseOrderReq("PO001"),
                createMockPurchaseOrderReq("PO002")
            ))
            .build();

        List<WmsCreatePurchaseOrderRes> expectedData = Arrays.asList(
            createMockPurchaseOrderRes("PO001", "SHOP001"),
            createMockPurchaseOrderRes("PO002", "SHOP002")
        );

        when(wmsFulfillmenAPI.createPurchaseOrderNew(request))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            List<WmsCreatePurchaseOrderRes> result = wmsApiClient.createPurchaseOrderNew(request);

            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals("PO001", result.get(0).getPurchaseNo());
            assertEquals("SHOP001", result.get(0).getShopOrderId());

            verify(wmsFulfillmenAPI).createPurchaseOrderNew(request);
        }
    }

    @Test
    void testUpdatePurchaseOrder_Success() {
        List<WmsPurchaseOrderDetailReq> request = Arrays.asList(
            WmsPurchaseOrderDetailReq.builder()
                .purchaseNo("PO001")
                .status(WmsOrderStatusEnum.PURCHASED_PENDING_SHIPMENT)
                .build(),
            WmsPurchaseOrderDetailReq.builder()
                .purchaseNo("PO002")
                .status(WmsOrderStatusEnum.SHIPPED_PENDING_RECEIPT)
                .build()
        );

        when(wmsFulfillmenAPI.updatePurchaseOrderDetails(request))
            .thenReturn(Mono.just(createSuccessVoidResponse()));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleVoidWmsResponse(any(), anyString(), any()))
                .thenAnswer(invocation -> null); // void method

            // 执行测试
            assertDoesNotThrow(() -> wmsApiClient.updatePurchaseOrder(request));

            verify(wmsFulfillmenAPI).updatePurchaseOrderDetails(request);
            mockedHandler.verify(() -> WmsResponseHandler.handleVoidWmsResponse(any(), eq("WMS更新采购订单API"), eq(request)));
        }
    }

    @Test
    void testQueryOrder_Success() {
        WmsOrderQueryReq request = WmsOrderQueryReq.builder()
            .page(1)
            .pageSize(10)
            .includeDetails(true)
            .build();

        List<WmsPurchaseOrderInfoRes> orderList = Arrays.asList(
            createMockPurchaseOrderInfoRes("PO001"),
            createMockPurchaseOrderInfoRes("PO002")
        );

        WmsPageDTO<WmsPurchaseOrderInfoRes> expectedData = WmsPageDTO.<WmsPurchaseOrderInfoRes>builder()
            .total(2)
            .pageSize(10)
            .pageIndex(1)
            .totalPages(1)
            .records(orderList)
            .build();

        when(wmsFulfillmenAPI.queryOrder(request))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            WmsPageDTO<WmsPurchaseOrderInfoRes> result = wmsApiClient.queryOrder(request);

            assertNotNull(result);
            assertEquals(2, result.getTotal());
            assertEquals(10, result.getPageSize());
            assertEquals(1, result.getPageIndex());
            assertEquals(2, result.getRecords().size());

            verify(wmsFulfillmenAPI).queryOrder(request);
        }
    }

    @Test
    void testQueryOrderDetail_Success() {
        PurchaseOrderDetailReq request = PurchaseOrderDetailReq.builder()
            .purchaseNo("PO001,PO002")
            .orderId("1688001,1688002")
            .nayaPurchaseNo("WMS001,WMS002")
            .build();

        List<WmsPurchaseOrderDetailsRes> expectedData = Arrays.asList(
            createMockPurchaseOrderDetailsRes("PO001"),
            createMockPurchaseOrderDetailsRes("PO002")
        );

        when(wmsFulfillmenAPI.queryOrderDetails("PO001,PO002", "1688001,1688002", "WMS001,WMS002"))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            List<WmsPurchaseOrderDetailsRes> result = wmsApiClient.queryOrderDetail(request);

            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals("PO001", result.get(0).getPurchaseNo());

            verify(wmsFulfillmenAPI).queryOrderDetails("PO001,PO002", "1688001,1688002", "WMS001,WMS002");
        }
    }

    // ==================== 产品管理相关API测试 ====================

    @Test
    void testCreateProduct_Success() {
        WmsCreateGoodsReq request = WmsCreateGoodsReq.builder()
            .cusCode("TEST001")
            .sku("TEST-SKU-001")
            .cnName("测试产品")
            .enName("Test Product")
            .price(99.99)
            .weight(1.5)
            .build();

        when(wmsFulfillmenAPI.createProduct(request))
            .thenReturn(Mono.just(createSuccessVoidResponse()));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleVoidWmsResponse(any(), anyString(), any()))
                .thenAnswer(invocation -> null); // void method

            assertDoesNotThrow(() -> wmsApiClient.createProduct(request));

            verify(wmsFulfillmenAPI).createProduct(request);
            mockedHandler.verify(() -> WmsResponseHandler.handleVoidWmsResponse(any(), eq("WMS创建产品API"), eq(request)));
        }
    }

    @Test
    void testGetProductList_Success() {
        Integer page = 1;
        String barcode = "********9";

        WmsPageDTO<WmsProductRes> expectedData = WmsPageDTO.<WmsProductRes>builder()
            .total(2)
            .pageSize(10)
            .pageIndex(1)
            .totalPages(1)
            .records(Arrays.asList(
                createMockProduct("SKU001", "产品1"),
                createMockProduct("SKU002", "产品2")
            ))
            .build();

        WmsApiResponse<WmsPageDTO<WmsProductRes>> mockResponse = createSuccessResponse(expectedData);

        // 根据实际接口定义，这个方法返回Mono<WmsApiResponse<List<WmsProduct>>>
        when(wmsFulfillmenAPI.getProductByPage(any(WmsProductQueryReq.class)))
            .thenReturn(Mono.just(mockResponse));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            WmsPageDTO<WmsProductRes> result = wmsApiClient.getProductList(page, barcode);

            assertNotNull(result);
            assertEquals(2, result.getRecords().size());
            assertEquals("SKU001", result.getRecords().get(0).getSku());
            assertEquals("产品1", result.getRecords().get(0).getCnName());

            verify(wmsFulfillmenAPI).getProductByPage(argThat(req -> req.getPage().equals(page) && req.getBarcode().equals(barcode)));
        }
    }

    @Test
    void testGetProductInfo_Success() {
        String cusCode = "TEST001";
        String offerId = "OFFER001";
        String sku = "SKU001";

        List<WmsProductRes> expectedData = Lists.newArrayList(createMockProduct(sku, "测试产品"));

        when(wmsFulfillmenAPI.getProductInfoList(cusCode, offerId, sku))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            List<WmsProductRes> result = wmsApiClient.getProductInfoList(WmsProductInfoReq.builder()
                .cusCode(cusCode)
                .offerId(offerId)
                .sku(sku)
                .build());

            assertNotNull(result);
            assertEquals(sku, result.get(0).getSku());
            assertEquals("测试产品", result.get(0).getCnName());

            verify(wmsFulfillmenAPI).getProductInfoList(cusCode, offerId, sku);
        }
    }

    // ==================== 边界条件和异常测试 ====================

    @Test
    void testGetAccountInfoByAuthCode_NullAuthCode() {
        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenThrow(new WmsApiException("授权码不能为空"));

            assertThrows(WmsApiException.class, () -> wmsApiClient.getAccountInfoByAuthCode(null));
        }
    }

    @Test
    void testGetAccountInfoByAuthCode_EmptyAuthCode() {
        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenThrow(new WmsApiException("授权码不能为空"));

            assertThrows(WmsApiException.class, () -> wmsApiClient.getAccountInfoByAuthCode(""));
        }
    }

    @Test
    void testUpdatePurchaseOrder_EmptyList() {
        List<WmsPurchaseOrderDetailReq> emptyList = new ArrayList<>();

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleVoidWmsResponse(any(), anyString(), any()))
                .thenAnswer(invocation -> null);

            assertDoesNotThrow(() -> wmsApiClient.updatePurchaseOrder(emptyList));
        }
    }

    @Test
    void testQueryOrder_InvalidPageParams() {
        WmsOrderQueryReq request = WmsOrderQueryReq.builder()
            .page(-1)  // 无效页码
            .pageSize(0)  // 无效页大小
            .build();

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenThrow(new WmsApiException("分页参数无效"));

            assertThrows(WmsApiException.class, () -> wmsApiClient.queryOrder(request));
        }
    }

    // ==================== 辅助方法 ====================

    private <T> WmsApiResponse<T> createSuccessResponse(T data) {
        WmsApiResponse<T> response = new WmsApiResponse<>();
        response.setSuccess("success");
        response.setCode(0);
        response.setMessage("成功");
        response.setData(data);
        return response;
    }

    private <T> WmsApiResponse<T> createFailureResponse() {
        WmsApiResponse<T> response = new WmsApiResponse<>();
        response.setSuccess("false");
        response.setCode(1);
        response.setMessage("API调用失败");
        return response;
    }

    private WmsApiResponse<Void> createSuccessVoidResponse() {
        WmsApiResponse<Void> response = new WmsApiResponse<>();
        response.setSuccess("success");
        response.setCode(0);
        response.setMessage("成功");
        return response;
    }

    private WmsPurchaseOrderReq createMockPurchaseOrderReq(String purchaseNo) {
        return WmsPurchaseOrderReq.builder()
            .purchaseNo(purchaseNo)
            .shopOrderId("SHOP" + purchaseNo.substring(2))
            .orderId(Long.valueOf(1688000 + Integer.parseInt(purchaseNo.substring(2))))
            .sellerOpenId("SELLER001")
            .storeId("STORE001")
            .total(BigDecimal.valueOf(100.0))
            .status(WmsOrderStatusEnum.PAID_PENDING_REVIEW)
            .payType(WmsPayTypeEnum.BALANCE)
            .createUser("testUser")
            .createTime(LocalDateTime.now())
            .build();
    }

    private WmsCreatePurchaseOrderRes createMockPurchaseOrderRes(String purchaseNo, String shopOrderId) {
        WmsCreatePurchaseOrderRes response = new WmsCreatePurchaseOrderRes();
        response.setPurchaseNo(purchaseNo);
        response.setShopOrderId(shopOrderId);
        return response;
    }

    private WmsPurchaseOrderInfoRes createMockPurchaseOrderInfoRes(String purchaseNo) {
        WmsPurchaseOrderInfoRes order = new WmsPurchaseOrderInfoRes();
        order.setPurchaseNo(purchaseNo);
        order.setShopOrderId("SHOP" + purchaseNo.substring(2));
        order.setOrderId(Long.valueOf(1688000 + Integer.parseInt(purchaseNo.substring(2))));
        order.setTotal(BigDecimal.valueOf(100.0));
        order.setStatus(WmsOrderStatusEnum.PAID_PENDING_REVIEW);
        order.setCreateTime(LocalDateTime.now());
        return order;
    }

    private WmsPurchaseOrderDetailsRes createMockPurchaseOrderDetailsRes(String purchaseNo) {
        WmsPurchaseOrderDetailsRes details = new WmsPurchaseOrderDetailsRes();
        details.setPurchaseNo(purchaseNo);
        details.setShopOrderId("SHOP" + purchaseNo.substring(2));
//        details.setSkuId("SKU" + purchaseNo.substring(2));
//        details.setCnName("测试产品" + purchaseNo.substring(2));
//        details.setEnName("Test Product " + purchaseNo.substring(2));
//        details.setQuantity(10);
//        details.setUnitPrice(BigDecimal.valueOf(10.0));
//        details.setSubTotal(BigDecimal.valueOf(100.0));
        return details;
    }

    private WmsProductRes createMockProduct(String sku, String cnName) {
        return WmsProductRes.builder()
            .sku(sku)
            .cnName(cnName)
            .enName("Test Product")
            .price(BigDecimal.valueOf(99.99))
            .weight(BigDecimal.valueOf(1.5))
            .barcode("********9" + sku.substring(3))
            .status(1)
            .createTime(LocalDateTime.now().toString())
            .build();
    }

    // ==================== 入库管理相关API测试 ====================

    @Test
    @DisplayName("创建入库单 - 成功场景")
    void testInboundCreateByPurchase_Success() {
        // 准备测试数据
        WmsCreateInboundOrderReq request = WmsCreateInboundOrderReq.builder()
            .cusCode("TEST001")
            .purchaseNo("PO20250131001")
            .asnType("PURCHASE")
            .warehouseAddress("深圳仓库")
            .transportWay("EXPRESS")
            .expressCompany("顺丰速运")
            .expressNumber("SF********90")
            .remark("单元测试创建入库单")
            .createUser("testUser")
            .build();

        WmsCreateInboundOrderRes expectedData = new WmsCreateInboundOrderRes();
        expectedData.setAsnId(12345L);
        expectedData.setAsnNumber("ASN20250131001");
        expectedData.setCreateTime(LocalDateTime.now());
        expectedData.setStatus("CREATED");

        when(wmsFulfillmenAPI.inboundCreateByPurchase(request))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            // 执行测试
            WmsCreateInboundOrderRes result = wmsApiClient.inboundCreateByPurchase(request);

            // 验证结果
            assertNotNull(result);
            assertEquals(Long.valueOf(12345L), result.getAsnId());
            assertEquals("ASN20250131001", result.getAsnNumber());
            assertEquals("CREATED", result.getStatus());
            assertNotNull(result.getCreateTime());

            verify(wmsFulfillmenAPI).inboundCreateByPurchase(request);
            mockedHandler.verify(() -> WmsResponseHandler.handleWmsResponse(any(), eq("WMS创建入库单API"), eq(request)));
        }
    }

    @Test
    @DisplayName("创建入库单 - 参数验证失败")
    void testInboundCreateByPurchase_InvalidParameters() {
        // 测试空的客户代码
        WmsCreateInboundOrderReq requestWithNullCusCode = WmsCreateInboundOrderReq.builder()
            .cusCode(null)
            .purchaseNo("PO20250131001")
            .build();

        IllegalArgumentException exception1 = assertThrows(IllegalArgumentException.class,
            () -> wmsApiClient.inboundCreateByPurchase(requestWithNullCusCode));
        assertTrue(exception1.getMessage().contains("客户代码不能为空"));

        // 测试空的采购单号
        WmsCreateInboundOrderReq requestWithNullPurchaseNo = WmsCreateInboundOrderReq.builder()
            .cusCode("TEST001")
            .purchaseNo(null)
            .build();

        IllegalArgumentException exception2 = assertThrows(IllegalArgumentException.class,
            () -> wmsApiClient.inboundCreateByPurchase(requestWithNullPurchaseNo));
        assertTrue(exception2.getMessage().contains("采购单号不能为空"));

        // 验证没有调用API
        verify(wmsFulfillmenAPI, never()).inboundCreateByPurchase(any());
    }

    @Test
    @DisplayName("创建入库单 - WMS API异常")
    void testInboundCreateByPurchase_WmsApiException() {
        WmsCreateInboundOrderReq request = WmsCreateInboundOrderReq.builder()
            .cusCode("TEST001")
            .purchaseNo("PO20250131001")
            .build();

        WmsApiException expectedException = new WmsApiException("WMS创建入库单API调用失败");

        when(wmsFulfillmenAPI.inboundCreateByPurchase(request))
            .thenReturn(Mono.just(createFailureResponse()));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenThrow(expectedException);

            // 执行测试并验证异常
            WmsApiException exception = assertThrows(WmsApiException.class,
                () -> wmsApiClient.inboundCreateByPurchase(request));

            assertEquals(expectedException.getMessage(), exception.getMessage());
            verify(wmsFulfillmenAPI).inboundCreateByPurchase(request);
        }
    }

    @Test
    @DisplayName("更新入库单 - 成功场景")
    void testInboundUpdateByPurchase_Success() {
        // 准备测试数据
        WmsInboundOrderUpdateReq request = WmsInboundOrderUpdateReq.builder()
            .asnId(12345L)
            .asnNumber("ASN20250131001")
            .transportWay("TRUCK")
            .expressCompany("德邦物流")
            .expressNumber("DP********90")
            .remark("更新入库单信息")
            .modifyUser("testUser")
            .build();

        WmsInboundUpdateRes expectedData = new WmsInboundUpdateRes();
        expectedData.setAsnId(12345L);
        expectedData.setAsnNumber("ASN20250131001");
        expectedData.setUpdateTime(LocalDateTime.now());
        expectedData.setStatus("UPDATED");

        when(wmsFulfillmenAPI.inboundUpdateByPurchase(request))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            // 执行测试
            WmsInboundUpdateRes result = wmsApiClient.inboundUpdateByPurchase(request);

            // 验证结果
            assertNotNull(result);
            assertEquals(Long.valueOf(12345L), result.getAsnId());
            assertEquals("ASN20250131001", result.getAsnNumber());
            assertEquals("UPDATED", result.getStatus());
            assertNotNull(result.getUpdateTime());

            verify(wmsFulfillmenAPI).inboundUpdateByPurchase(request);
            mockedHandler.verify(() -> WmsResponseHandler.handleWmsResponse(any(), eq("WMS更新入库单API"), eq(request)));
        }
    }

    @Test
    @DisplayName("分页查询入库单 - 成功场景")
    void testInboundQueryPage_Success() {
        // 准备测试数据
        WmsInboundQueryOrderPageReq request = WmsInboundQueryOrderPageReq.builder()
            .cusCode("TEST001")
            .page(1L)
            .pageSize(10L)
            .queryConditions(WmsInboundQueryOrderPageReq.ASNListQueryConditions.builder()
                .asnNumber("ASN20250131001")
                .purchaseNo("PO20250131001")
                .build())
            .build();

        WmsPageDTO<WmsInboundQueryPageRes> expectedData = WmsPageDTO.<WmsInboundQueryPageRes>builder()
            .total(2)
            .pageIndex(1)
            .pageSize(10)
            .totalPages(1)
            .records(Arrays.asList(
                createMockInboundQueryPageRes("ASN20250131001", "PO20250131001"),
                createMockInboundQueryPageRes("ASN20250131002", "PO20250131002")
            ))
            .build();

        when(wmsFulfillmenAPI.inboundQueryPage(request))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            // 执行测试
            WmsPageDTO<WmsInboundQueryPageRes> result = wmsApiClient.inboundQueryPage(request);

            // 验证结果
            assertNotNull(result);
            assertEquals(Integer.valueOf(2), result.getTotal());
            assertEquals(Integer.valueOf(1), result.getPageIndex());
            assertEquals(Integer.valueOf(10), result.getPageSize());
            assertEquals(Integer.valueOf(1), result.getTotalPages());
            assertEquals(2, result.getRecords().size());
            assertEquals("ASN20250131001", result.getRecords().get(0).getAsnNumber());

            verify(wmsFulfillmenAPI).inboundQueryPage(request);
            mockedHandler.verify(() -> WmsResponseHandler.handleWmsResponse(any(), eq("WMS分页查询入库单API"), eq(request)));
        }
    }

    @Test
    @DisplayName("分页查询入库单 - 空结果")
    void testInboundQueryPage_EmptyResult() {
        WmsInboundQueryOrderPageReq request = WmsInboundQueryOrderPageReq.builder()
            .cusCode("TEST001")
            .page(1L)
            .pageSize(10L)
            .queryConditions(WmsInboundQueryOrderPageReq.ASNListQueryConditions.builder()
                .asnNumber("NONEXISTENT")
                .build())
            .build();

        WmsPageDTO<WmsInboundQueryPageRes> expectedData = WmsPageDTO.<WmsInboundQueryPageRes>builder()
            .total(0)
            .pageIndex(1)
            .pageSize(10)
            .totalPages(0)
            .records(Collections.emptyList())
            .build();

        when(wmsFulfillmenAPI.inboundQueryPage(request))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            WmsPageDTO<WmsInboundQueryPageRes> result = wmsApiClient.inboundQueryPage(request);

            assertNotNull(result);
            assertEquals(Integer.valueOf(0), result.getTotal());
            assertTrue(result.getRecords().isEmpty());

            verify(wmsFulfillmenAPI).inboundQueryPage(request);
        }
    }

    @Test
    @DisplayName("查询入库单详情 - 成功场景")
    void testInboundQuery_Success() {
        // 准备测试数据
        WmsInboundInfoReq request = WmsInboundInfoReq.builder()
            .asnId(12345L)
            .asnNumber("ASN20250131001")
            .cusCode("TEST001")
            .build();

        WmsInboundInfoRes expectedData = createMockInboundInfoRes("ASN20250131001", "PO20250131001");

        when(wmsFulfillmenAPI.inboundQuery(request))
            .thenReturn(Mono.just(createSuccessResponse(expectedData)));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenReturn(expectedData);

            // 执行测试
            WmsInboundInfoRes result = wmsApiClient.inboundQuery(request);

            // 验证结果
            assertNotNull(result);
            assertEquals(Long.valueOf(12345L), result.getAsnId());
            assertEquals("ASN20250131001", result.getAsnNumber());
            assertEquals("PO20250131001", result.getPurchaseNo());
            assertEquals("TEST001", result.getChargePerson());
            assertNotNull(result.getAsnDetails());
            assertEquals(2, result.getAsnDetails().size());

            verify(wmsFulfillmenAPI).inboundQuery(request);
            mockedHandler.verify(() -> WmsResponseHandler.handleWmsResponse(any(), eq("WMS查询入库单详情API"), eq(request)));
        }
    }

    @Test
    @DisplayName("查询入库单详情 - 入库单不存在")
    void testInboundQuery_NotFound() {
        WmsInboundInfoReq request = WmsInboundInfoReq.builder()
            .asnId(99999L)
            .asnNumber("NONEXISTENT")
            .cusCode("TEST001")
            .build();

        WmsApiException expectedException = new WmsApiException("入库单不存在");

        when(wmsFulfillmenAPI.inboundQuery(request))
            .thenReturn(Mono.just(createFailureResponse()));

        try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
            mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
                .thenThrow(expectedException);

            // 执行测试并验证异常
            WmsApiException exception = assertThrows(WmsApiException.class,
                () -> wmsApiClient.inboundQuery(request));

            assertEquals(expectedException.getMessage(), exception.getMessage());
            verify(wmsFulfillmenAPI).inboundQuery(request);
        }
    }

    // ==================== 入库管理辅助方法 ====================

    private WmsInboundQueryPageRes createMockInboundQueryPageRes(String asnNumber, String purchaseNo) {
        WmsInboundQueryPageRes res = new WmsInboundQueryPageRes();
        res.setAsnId(Long.valueOf(Math.abs(asnNumber.hashCode())));
        res.setAsnNumber(asnNumber);
        res.setPurchaseNo(purchaseNo);
        res.setCreateTime(LocalDateTime.now().toString());
        res.setCreateUser("testUser");
        return res;
    }

    private WmsInboundInfoRes createMockInboundInfoRes(String asnNumber, String purchaseNo) {
        WmsInboundInfoRes inboundInfo = new WmsInboundInfoRes();
        inboundInfo.setAsnId(12345L);
        inboundInfo.setAsnNumber(asnNumber);
        inboundInfo.setPurchaseNo(purchaseNo);
        inboundInfo.setAsnType("PURCHASE");
        inboundInfo.setChargePerson("TEST001");
        inboundInfo.setCreateTime(OffsetDateTime.now());
        inboundInfo.setCreateUser("testUser");
        inboundInfo.setWarehouseAddress("深圳仓库");
        inboundInfo.setTransportWay("EXPRESS");
        inboundInfo.setExpressCompany("顺丰速运");
        inboundInfo.setExpressNumber("SF********90");
        inboundInfo.setRemark("测试入库单");
        inboundInfo.setVoidFlag(false);

        // 创建入库单明细
        List<WmsInboundInfoRes.ASNDetail> details = Arrays.asList(
            createMockASNDetail(1L, "商品1", "Product1", 10L, 8L),
            createMockASNDetail(2L, "商品2", "Product2", 20L, 18L)
        );
        inboundInfo.setAsnDetails(details);

        return inboundInfo;
    }

    private WmsInboundInfoRes.ASNDetail createMockASNDetail(Long goodsId, String cnName, String enName, Long shouldQuantity, Long factQuantity) {
        WmsInboundInfoRes.ASNDetail detail = new WmsInboundInfoRes.ASNDetail();
        detail.setAsnDetailId(goodsId * 100);
        detail.setAsnMainId(12345L);
        detail.setGoodsId(goodsId);
        detail.setCnName(cnName);
        detail.setEnName(enName);
        detail.setShouldQuantity(shouldQuantity);
        detail.setFactQuantity(factQuantity);
        detail.setOkQuantity(factQuantity);
        detail.setPrice(99.99);
        detail.setRefWeight(1.5);
        detail.setFactWeight(1.4);
        detail.setGoodsStatus("NORMAL");
        detail.setCreateTime(OffsetDateTime.now());
        detail.setVoidFlag(false);
        return detail;
    }
}
