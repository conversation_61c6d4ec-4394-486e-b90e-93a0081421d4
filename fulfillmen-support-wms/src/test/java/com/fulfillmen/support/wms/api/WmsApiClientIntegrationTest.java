/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.api;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fulfillmen.support.wms.BaseIntegrationTest;
import com.fulfillmen.support.wms.autoconfigure.WmsAutoConfiguration;
import com.fulfillmen.support.wms.autoconfigure.WmsProperties;
import com.fulfillmen.support.wms.dto.common.WmsApiResponse;
import com.fulfillmen.support.wms.dto.common.WmsPageDTO;
import com.fulfillmen.support.wms.dto.enums.WmsOperationTypeEnum;
import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.enums.WmsPayTypeEnum;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsAccountChargeV2Req;
import com.fulfillmen.support.wms.dto.request.WmsCreateGoodsReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateInboundOrderReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateOrderReq;
import com.fulfillmen.support.wms.dto.request.WmsInboundInfoReq;
import com.fulfillmen.support.wms.dto.request.WmsInboundOrderUpdateReq;
import com.fulfillmen.support.wms.dto.request.WmsInboundQueryOrderPageReq;
import com.fulfillmen.support.wms.dto.request.WmsOrderQueryReq;
import com.fulfillmen.support.wms.dto.request.WmsProductInfoReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderReq;
import com.fulfillmen.support.wms.dto.response.WmsAccountChargeInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsAccountInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsCreateInboundOrderRes;
import com.fulfillmen.support.wms.dto.response.WmsCreatePurchaseOrderRes;
import com.fulfillmen.support.wms.dto.response.WmsInboundInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsInboundQueryPageRes;
import com.fulfillmen.support.wms.dto.response.WmsInboundUpdateRes;
import com.fulfillmen.support.wms.dto.response.WmsProductRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderInfoRes;
import com.fulfillmen.support.wms.exception.WmsApiException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import okhttp3.mockwebserver.RecordedRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

/**
 * WmsApiClient 集成测试
 *
 * <p>使用MockWebServer进行真实HTTP请求的集成测试，验证WMS API客户端的功能，
 * 包括请求头注入、API调用、响应处理、新的WmsResponseHandler等。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@DisplayName("WmsApiClient集成测试")
class WmsApiClientIntegrationTest extends BaseIntegrationTest {

    private WmsApiClient wmsApiClient;
    private WmsFulfillmenAPI wmsFulfillmenAPI;
    private WmsProperties wmsProperties;

    @BeforeEach
    void setUp() {
        // 创建WMS配置
        wmsProperties = new WmsProperties();
        wmsProperties.setBaseUrl(baseUrl);
        wmsProperties.setApiKey("e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855");

        // 创建WMS自动配置实例
        WmsAutoConfiguration wmsAutoConfiguration = new WmsAutoConfiguration(wmsProperties);

        // 创建WebClient
        WebClient webClient = wmsAutoConfiguration.wmsWebClient();

        // 创建声明式HTTP接口
        WebClientAdapter adapter = WebClientAdapter.create(webClient);
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(adapter).build();
        wmsFulfillmenAPI = factory.createClient(WmsFulfillmenAPI.class);

        // 创建API客户端
        wmsApiClient = new WmsApiClient(wmsFulfillmenAPI);
    }

    private WmsApiResponse<Void> createSuccessVoidResponse() {
        WmsApiResponse<Void> response = new WmsApiResponse<>();
        response.setSuccess("success");
        response.setCode(0);
        response.setMessage("成功");
        return response;
    }

    private WmsAccountInfoRes createMockAccountInfo(String customerCode) {
        return WmsAccountInfoRes.builder()
            .customerId(10002)
            .customerCode(customerCode)
            .customerName("测试客户")
            .accountMoney(BigDecimal.valueOf(1000.0))
            .email("<EMAIL>")
            .build();
    }

    private WmsPurchaseOrderReq createMockPurchaseOrderReq(String purchaseNo) {
        return WmsPurchaseOrderReq.builder()
            .purchaseNo(purchaseNo)
            .shopOrderId("SHOP" + purchaseNo.substring(12))
            .orderId(Long.valueOf(1688000 + Integer.parseInt(purchaseNo.substring(12))))
            .sellerOpenId("SELLER001")
            .storeId("STORE001")
            .total(BigDecimal.valueOf(100.0))
            .status(WmsOrderStatusEnum.PAID_PENDING_REVIEW)
            .payType(WmsPayTypeEnum.BALANCE)
            .createUser("testUser")
            .createTime(LocalDateTime.now())
            .remark("集成测试订单")
            .build();
    }

    private WmsCreatePurchaseOrderRes createMockPurchaseOrderRes(String purchaseNo, String shopOrderId) {
        WmsCreatePurchaseOrderRes response = new WmsCreatePurchaseOrderRes();
        response.setPurchaseNo(purchaseNo);
        response.setShopOrderId(shopOrderId);
        return response;
    }

    private WmsPurchaseOrderInfoRes createMockPurchaseOrderInfoRes(String purchaseNo) {
        WmsPurchaseOrderInfoRes order = new WmsPurchaseOrderInfoRes();
        order.setPurchaseNo(purchaseNo);
        order.setShopOrderId("SHOP" + purchaseNo.substring(12));
        order.setOrderId(Long.valueOf(1688000 + Integer.parseInt(purchaseNo.substring(12))));
        order.setTotal(BigDecimal.valueOf(100.0));
        order.setStatus(WmsOrderStatusEnum.PAID_PENDING_REVIEW);
        order.setCreateTime(LocalDateTime.now());
        order.setCusCode("TEST001");
        order.setCreateUser("testUser");
        return order;
    }

    // ==================== 辅助方法 ====================

    private WmsPurchaseOrderDetailsRes createMockPurchaseOrderDetailsRes(String purchaseNo) {
        WmsPurchaseOrderDetailsRes details = new WmsPurchaseOrderDetailsRes();
        details.setPurchaseNo(purchaseNo);
        details.setShopOrderId("SHOP" + purchaseNo.substring(12));
//        details.setSkuId("SKU" + purchaseNo.substring(12));
//        details.setCnName("测试产品" + purchaseNo.substring(12));
//        details.setEnName("Test Product " + purchaseNo.substring(12));
//        details.setQuantity(10);
//        details.setUnitPrice(BigDecimal.valueOf(10.0));
//        details.setSubTotal(BigDecimal.valueOf(100.0));
//        details.setWeight(BigDecimal.valueOf(1.5));
//        details.setImageUrl("https://example.com/image.jpg");
//        details.setProductUrl("https://example.com/product");
        return details;
    }

    private WmsProductRes createMockProduct(String sku, String cnName) {
        return WmsProductRes.builder()
            .customerId("TEST001")
            .sku(sku)
            .cnName(cnName)
            .enName("Test Product")
            .price(BigDecimal.valueOf(99.99))
            .weight(BigDecimal.valueOf(1.5))
            .length(BigDecimal.valueOf(10.0))
            .width(BigDecimal.valueOf(8.0))
            .height(BigDecimal.valueOf(5.0))
            .barcode("*********" + sku.substring(sku.length() - 3))
            .status(1)
            .createTime(LocalDateTime.now().toString())
            .updateTime(LocalDateTime.now().toString())
            .imageUrl("https://example.com/image.jpg")
            .productUrl("https://example.com/product")
            .category("测试分类")
            .brand("测试品牌")
            .build();
    }

    @Nested
    @DisplayName("账户管理API测试")
    class AccountManagementTests {

        @Test
        @DisplayName("通过客户码获取账户信息 - 成功场景")
        void testGetAccountInfoByCustomerCode_Success() throws Exception {
            // 准备测试数据
            String customerCode = "10002";
            WmsAccountInfoRes expectedData = WmsAccountInfoRes.builder()
                .customerId(10002)
                .customerCode(customerCode)
                .customerName("测试客户")
                .accountMoney(BigDecimal.valueOf(1000.0))
                .email("<EMAIL>")
                .country("中国")
                .province("广东省")
                .city("深圳市")
                .address("测试地址123号")
                .mobile("***********")
                .phone("0755-********")
                .status("1")
                .serviceFeeRate(BigDecimal.valueOf(0.05))
                .creditLimit(BigDecimal.valueOf(5000.0))
                .createTime(LocalDateTime.now())
                .build();

            WmsApiResponse<WmsAccountInfoRes> mockResponse = createSuccessResponse(expectedData);
            mockWebServer.enqueue(createMockResponse(mockResponse));

            // 执行测试
            WmsAccountInfoRes result = wmsApiClient.getAccountInfoByCustomerCode(customerCode);

            // 验证结果
            assertNotNull(result);
            assertEquals(customerCode, result.getCustomerCode());
            assertEquals("测试客户", result.getCustomerName());
            assertEquals(BigDecimal.valueOf(1000.0), result.getAccountMoney());
            assertEquals("<EMAIL>", result.getEmail());

            // 验证请求
            RecordedRequest request = takeRequest();
            assertNotNull(request);
            assertEquals("GET", request.getMethod());
            assertTrue(request.getPath().contains("/Alibaba/OpenApi/GetAccountInfo.ashx"));
            assertTrue(request.getPath().contains("cusCode=" + customerCode));

            // 验证WMS特定的请求头
            assertWmsRequestHeaders(request);
        }

        @Test
        @DisplayName("通过授权码获取账户信息 - 成功场景")
        void testGetAccountInfoByAuthCode_Success() throws Exception {
            // 准备测试数据
            String authCode = "dGVzdC1hdXRoLWNvZGU=";
            WmsAccountInfoRes expectedData = WmsAccountInfoRes.builder()
                .customerId(10002)
                .customerCode("TEST001")
                .customerName("测试客户")
                .accountMoney(BigDecimal.valueOf(1500.0))
                .build();

            WmsApiResponse<WmsAccountInfoRes> mockResponse = createSuccessResponse(expectedData);
            mockWebServer.enqueue(createMockResponse(mockResponse));

            // 执行测试
            WmsAccountInfoRes result = wmsApiClient.getAccountInfoByAuthCode(authCode);

            // 验证结果
            assertNotNull(result);
            assertEquals("TEST001", result.getCustomerCode());
            assertEquals("测试客户", result.getCustomerName());
            assertEquals(BigDecimal.valueOf(1500.0), result.getAccountMoney());

            // 验证请求
            RecordedRequest request = takeRequest();
            assertNotNull(request);
            assertEquals("GET", request.getMethod());
            assertTrue(request.getPath().contains("/Alibaba/OpenApi/AuthLogin.ashx"));
            assertTrue(request.getPath().contains("Code=" + authCode));

            assertWmsRequestHeaders(request);
        }

        @Test
        @DisplayName("账户余额扣减 - 成功场景")
        void testDeductAccountInfoByPurchase_Success() throws Exception {
            // 准备测试数据
//            DeductAccountReq requestData = DeductAccountReq.builder()
//                .cusCode("TEST001")
//                .orderNo("ORDER20250121001")
//                .totalAmount(150.00)
//                .productAmount(120.00)
//                .serviceFee(30.00)
//                .payType(WmsPayTypeEnum.BALANCE)
//                .createUser("testUser")
//                .remark("集成测试订单")
//                .build();
            WmsAccountChargeV2Req requestData = WmsAccountChargeV2Req.builder()
                .cusCode("10002")
                .orderNo("ORDER20250121001")
                .operationType(WmsOperationTypeEnum.CHARGE)
                .productAmount(120.00)
                .serviceFee(30.00)
                .shippingFee(15.00)
                .storeId("536")
                .originalTotalPrice(155.00)
                .remark("nayasource 测试")
//              .payType(WmsPayTypeEnum.BALANCE)
                .createUser("testUser")
                .build();

            WmsAccountChargeInfoRes expectedData = new WmsAccountChargeInfoRes();
            expectedData.setCusCode("10002");
            expectedData.setOrderNo("ORDER20250121001");
            expectedData.setPaymentNumber("PAY20250121001");
            expectedData.setBalanceAfterCharge(BigDecimal.valueOf(850.00));
//            expectedData.setChargedAmount(150.00);
            expectedData.setPaymentDetailId(12345);

            WmsApiResponse<WmsAccountChargeInfoRes> mockResponse = createSuccessResponse(expectedData);
            mockWebServer.enqueue(createMockResponse(mockResponse));

            // 执行测试
            WmsAccountChargeInfoRes result = wmsApiClient.deductAccountInfoByPurchase(requestData);

            // 验证结果
            assertNotNull(result);
            assertEquals("TEST001", result.getCusCode());
            assertEquals("ORDER20250121001", result.getOrderNo());
            assertEquals("PAY20250121001", result.getPaymentNumber());
            assertEquals(850.00, result.getBalanceAfterCharge());
            assertEquals(150.00, result.getBalanceAfterCharge());
            assertEquals(Integer.valueOf(12345), result.getPaymentDetailId());

            // 验证请求
            RecordedRequest request = takeRequest();
            assertNotNull(request);
            assertEquals("POST", request.getMethod());
            assertTrue(request.getPath().contains("/Alibaba/OpenApi/DeductAccountInfoByPurchase.ashx"));

            assertWmsRequestHeaders(request);
        }
    }

    @Nested
    @DisplayName("采购订单API测试")
    class PurchaseOrderTests {

        @Test
        @DisplayName("创建采购订单（新版本）- 成功场景")
        void testCreatePurchaseOrderNew_Success() throws Exception {
            // 准备测试数据
            WmsCreateOrderReq request = WmsCreateOrderReq.builder()
                .customerCode("TEST001")
                .orders(Arrays.asList(
                    createMockPurchaseOrderReq("PO20250121001"),
                    createMockPurchaseOrderReq("PO20250121002")
                ))
                .build();

            List<WmsCreatePurchaseOrderRes> expectedData = Arrays.asList(
                createMockPurchaseOrderRes("PO20250121001", "SHOP001"),
                createMockPurchaseOrderRes("PO20250121002", "SHOP002")
            );

            WmsApiResponse<List<WmsCreatePurchaseOrderRes>> mockResponse = createSuccessResponse(expectedData);
            mockWebServer.enqueue(createMockResponse(mockResponse));

            // 执行测试
            List<WmsCreatePurchaseOrderRes> result = wmsApiClient.createPurchaseOrderNew(request);

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals("PO20250121001", result.get(0).getPurchaseNo());
            assertEquals("SHOP001", result.get(0).getShopOrderId());
            assertEquals("PO20250121002", result.get(1).getPurchaseNo());
            assertEquals("SHOP002", result.get(1).getShopOrderId());

            // 验证请求
            RecordedRequest request1 = takeRequest();
            assertNotNull(request1);
            assertEquals("POST", request1.getMethod());
            assertTrue(request1.getPath().contains("/Alibaba/OpenApi/PurchaseOrderCreate.ashx"));

            assertWmsRequestHeaders(request1);
        }

        @Test
        @DisplayName("更新采购订单 - 成功场景")
        void testUpdatePurchaseOrder_Success() throws Exception {
            // 准备测试数据
            List<WmsPurchaseOrderDetailReq> request = Arrays.asList(
                WmsPurchaseOrderDetailReq.builder()
                    .purchaseNo("PO20250121001")
                    .status(WmsOrderStatusEnum.PURCHASED_PENDING_SHIPMENT)
                    .remark("已采购，等待发货")
                    .build(),
                WmsPurchaseOrderDetailReq.builder()
                    .purchaseNo("PO20250121002")
                    .status(WmsOrderStatusEnum.SHIPPED_PENDING_RECEIPT)
                    .trackingNo("SF*********0")
                    .remark("已发货，等待收货")
                    .build()
            );

            WmsApiResponse<Void> mockResponse = createSuccessVoidResponse();
            mockWebServer.enqueue(createMockResponse(mockResponse));

            // 执行测试
            assertDoesNotThrow(() -> wmsApiClient.updatePurchaseOrder(request));

            // 验证请求
            RecordedRequest recordedRequest = takeRequest();
            assertNotNull(recordedRequest);
            assertEquals("POST", recordedRequest.getMethod());
            assertTrue(recordedRequest.getPath().contains("/Alibaba/OpenApi/PurchaseOrdersUpdate.ashx"));

            assertWmsRequestHeaders(recordedRequest);
        }

        @Test
        @DisplayName("查询采购订单 - 成功场景")
        void testQueryOrder_Success() throws Exception {
            // 准备测试数据
            WmsOrderQueryReq request = WmsOrderQueryReq.builder()
                .page(1)
                .pageSize(20)
                .includeDetails(true)
                .build();

            List<WmsPurchaseOrderInfoRes> orderList = Arrays.asList(
                createMockPurchaseOrderInfoRes("PO20250121001"),
                createMockPurchaseOrderInfoRes("PO20250121002")
            );

            WmsPageDTO<WmsPurchaseOrderInfoRes> expectedData = WmsPageDTO.<WmsPurchaseOrderInfoRes>builder()
                .total(2)
                .pageSize(20)
                .pageIndex(1)
                .totalPages(1)
                .records(orderList)
                .build();

            WmsApiResponse<WmsPageDTO<WmsPurchaseOrderInfoRes>> mockResponse = createSuccessResponse(expectedData);
            mockWebServer.enqueue(createMockResponse(mockResponse));

            // 执行测试
            WmsPageDTO<WmsPurchaseOrderInfoRes> result = wmsApiClient.queryOrder(request);

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.getTotal());
            assertEquals(20, result.getPageSize());
            assertEquals(1, result.getPageIndex());
            assertEquals(2, result.getRecords().size());

            // 验证订单详情
            WmsPurchaseOrderInfoRes firstOrder = result.getRecords().get(0);
            assertEquals("PO20250121001", firstOrder.getPurchaseNo());
            assertEquals("SHOP001", firstOrder.getShopOrderId());

            // 验证请求
            RecordedRequest recordedRequest = takeRequest();
            assertNotNull(recordedRequest);
            assertEquals("POST", recordedRequest.getMethod());
            assertTrue(recordedRequest.getPath().contains("/Alibaba/OpenApi/PurchaseOrderQuery.ashx"));

            assertWmsRequestHeaders(recordedRequest);
        }

        @Test
        @DisplayName("查询采购订单详情 - 成功场景")
        void testQueryOrderDetail_Success() throws Exception {
            // 准备测试数据
            PurchaseOrderDetailReq request = PurchaseOrderDetailReq.builder()
                .purchaseNo("PO20250121001,PO20250121002")
                .orderId("1688001,1688002")
                .nayaPurchaseNo("WMS001,WMS002")
                .build();

            List<WmsPurchaseOrderDetailsRes> expectedData = Arrays.asList(
                createMockPurchaseOrderDetailsRes("PO20250121001"),
                createMockPurchaseOrderDetailsRes("PO20250121002")
            );

            WmsApiResponse<List<WmsPurchaseOrderDetailsRes>> mockResponse = createSuccessResponse(expectedData);
            mockWebServer.enqueue(createMockResponse(mockResponse));

            // 执行测试
            List<WmsPurchaseOrderDetailsRes> result = wmsApiClient.queryOrderDetail(request);

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.size());

            WmsPurchaseOrderDetailsRes firstDetail = result.get(0);
            assertEquals("PO20250121001", firstDetail.getPurchaseNo());
            assertEquals("SHOP001", firstDetail.getShopOrderId());
//            assertEquals("SKU001", firstDetail.getSkuId());
//            assertEquals("测试产品001", firstDetail.getCnName());

            // 验证请求
            RecordedRequest recordedRequest = takeRequest();
            assertNotNull(recordedRequest);
            assertEquals("GET", recordedRequest.getMethod());
            assertTrue(recordedRequest.getPath().contains("/Alibaba/OpenApi/PurchaseOrderQuery.ashx"));
            assertTrue(recordedRequest.getPath().contains("purchaseNo=PO20250121001%2CPO20250121002"));

            assertWmsRequestHeaders(recordedRequest);
        }
    }

    @Nested
    @DisplayName("产品管理API测试")
    class ProductManagementTests {

        @Test
        @DisplayName("创建产品 - 成功场景")
        void testCreateProduct_Success() throws Exception {
            // 准备测试数据
            WmsCreateGoodsReq request = WmsCreateGoodsReq.builder()
                .cusCode("TEST001")
                .sku("TEST-SKU-001")
                .platformSku("TEST-SKU-001")
                .cnName("测试产品")
                .enName("Test Product")
                .price(99.99)
                .weight(1.5)
                .length(10.0)
                .width(8.0)
                .height(5.0)
                .barcode("*********0123")
                .build();

            WmsApiResponse<Void> mockResponse = createSuccessVoidResponse();
            mockWebServer.enqueue(createMockResponse(mockResponse));

            // 执行测试
            assertDoesNotThrow(() -> wmsApiClient.createProduct(request));

            // 验证请求
            RecordedRequest recordedRequest = takeRequest();
            assertNotNull(recordedRequest);
            assertEquals("POST", recordedRequest.getMethod());
            assertTrue(recordedRequest.getPath().contains("/Alibaba/OpenApi/CreateGoods.ashx"));

            assertWmsRequestHeaders(recordedRequest);
        }

        @Test
        @DisplayName("获取产品列表 - 成功场景")
        void testGetProductList_Success() throws Exception {
            // 准备测试数据
            Integer page = 1;
            String barcode = "*********0123";

            WmsPageDTO<WmsProductRes> expectedData = WmsPageDTO.<WmsProductRes>builder()
                .total(2)
                .pageSize(20)
                .pageIndex(1)
                .totalPages(1)
                .records(
                    Arrays.asList(
                        createMockProduct("TEST-SKU-001", "测试产品1"),
                        createMockProduct("TEST-SKU-002", "测试产品2")
                    )).build();

            WmsApiResponse<WmsPageDTO<WmsProductRes>> mockResponse = createSuccessResponse(expectedData);
            mockWebServer.enqueue(createMockResponse(mockResponse));

            // 执行测试
            WmsPageDTO<WmsProductRes> result = wmsApiClient.getProductList(page, barcode);

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.getRecords().size());
            assertEquals("TEST-SKU-001", result.getRecords().get(0).getSku());
            assertEquals("测试产品1", result.getRecords().get(0).getCnName());
            assertEquals("TEST-SKU-002", result.getRecords().get(1).getSku());
            assertEquals("测试产品2", result.getRecords().get(1).getCnName());

            // 验证请求
            RecordedRequest recordedRequest = takeRequest();
            assertNotNull(recordedRequest);
            assertEquals("POST", recordedRequest.getMethod());
            assertTrue(recordedRequest.getPath().contains("/Alibaba/OpenApi/GetGoodsListProxy.ashx"));

            assertWmsRequestHeaders(recordedRequest);
        }

        @Test
        @DisplayName("获取产品信息 - 成功场景")
        void testGetProductInfo_Success() throws Exception {
            // 准备测试数据
            String cusCode = "TEST001";
            String offerId = "OFFER001";
            String sku = "TEST-SKU-001";

            List<WmsProductRes> expectedData = Collections.singletonList(createMockProduct(sku, "测试产品详情"));

            WmsApiResponse<List<WmsProductRes>> mockResponse = createSuccessResponse(expectedData);
            mockWebServer.enqueue(createMockResponse(mockResponse));

            // 执行测试
            List<WmsProductRes> result = wmsApiClient.getProductInfoList(WmsProductInfoReq.builder()
                .cusCode(cusCode)
                .offerId(offerId)
                .sku(sku)
                .build());

            // 验证结果
            assertNotNull(result);
            assertEquals(sku, result.get(0).getSku());
            assertEquals("测试产品详情", result.get(0).getCnName());
            assertEquals(BigDecimal.valueOf(99.99), result.get(0).getPrice());

            // 验证请求
            RecordedRequest recordedRequest = takeRequest();
            assertNotNull(recordedRequest);
            assertEquals("GET", recordedRequest.getMethod());
            assertTrue(recordedRequest.getPath().contains("/Alibaba/OpenApi/GetGoodsQuery.ashx"));
            assertTrue(recordedRequest.getPath().contains("cusCode=" + cusCode));
            assertTrue(recordedRequest.getPath().contains("offerId=" + offerId));
            assertTrue(recordedRequest.getPath().contains("sku=" + sku));

            assertWmsRequestHeaders(recordedRequest);
        }
    }

    @Nested
    @DisplayName("异常情况测试")
    class ExceptionTests {

        @Test
        @DisplayName("API调用失败 - 返回错误响应")
        void testApiCallFailure_ErrorResponse() throws Exception {
            String customerCode = "INVALID001";

            WmsApiResponse<WmsAccountInfoRes> failureResponse = new WmsApiResponse<>();
            failureResponse.setSuccess("false");
            failureResponse.setCode(1001);
            failureResponse.setMessage("客户不存在");

            mockWebServer.enqueue(createMockResponse(failureResponse));

            // 执行测试并验证异常
            WmsApiException exception = assertThrows(WmsApiException.class,
                () -> wmsApiClient.getAccountInfoByCustomerCode(customerCode));

            assertNotNull(exception.getMessage());
            assertTrue(exception.getMessage().contains("WMS"));

            // 验证请求仍然发送
            RecordedRequest request = takeRequest();
            assertNotNull(request);
        }

        @Test
        @DisplayName("网络异常 - 服务器不可达")
        void testNetworkException_ServerUnavailable() throws Exception {
            // 停止MockWebServer来模拟网络异常
            mockWebServer.shutdown();

            String customerCode = "TEST001";

            // 执行测试并验证异常
            Exception exception = assertThrows(Exception.class,
                () -> wmsApiClient.getAccountInfoByCustomerCode(customerCode));

            assertNotNull(exception);
            // 网络异常应该被包装成WmsApiException
            assertTrue(exception instanceof WmsApiException ||
                exception.getCause() instanceof java.net.ConnectException);
        }
    }

    @Nested
    @DisplayName("请求头验证测试")
    class RequestHeaderTests {

        @Test
        @DisplayName("动态请求头注入验证")
        void testDynamicHeaderInjection() throws Exception {
            String customerCode = "TEST001";
            WmsAccountInfoRes expectedData = createMockAccountInfo(customerCode);

            WmsApiResponse<WmsAccountInfoRes> mockResponse = createSuccessResponse(expectedData);
            mockWebServer.enqueue(createMockResponse(mockResponse));

            // 执行API调用
            wmsApiClient.getAccountInfoByCustomerCode(customerCode);

            // 验证请求头
            RecordedRequest request = takeRequest();
            assertNotNull(request);

            // 验证动态请求头
            String nonce = request.getHeader(WmsFulfillmenAPI.X_NONCE);
            String timestamp = request.getHeader(WmsFulfillmenAPI.X_TIMESTAMP);

            assertNotNull(nonce, "X-Nonce请求头不应为空");
            assertNotNull(timestamp, "X-Timestamp请求头不应为空");

            // 验证nonce格式
            assertEquals(12, nonce.length(), "X-Nonce应该是12位字符");
            assertTrue(nonce.matches("[a-zA-Z0-9]+"), "X-Nonce应该只包含字母和数字");

            // 验证timestamp格式
            long timestampValue = Long.parseLong(timestamp);
            long currentTime = System.currentTimeMillis();
            long timeDiff = Math.abs(currentTime - timestampValue);
            assertTrue(timeDiff < 5000, "时间戳应该在5秒内: " + timeDiff + "ms");
        }

        @Test
        @DisplayName("请求头随机性验证")
        void testRequestHeaderRandomness() throws Exception {
            String customerCode = "TEST001";
            WmsAccountInfoRes expectedData = createMockAccountInfo(customerCode);
            WmsApiResponse<WmsAccountInfoRes> mockResponse = createSuccessResponse(expectedData);

            // 发送两个相同的请求
            mockWebServer.enqueue(createMockResponse(mockResponse));
            mockWebServer.enqueue(createMockResponse(mockResponse));

            wmsApiClient.getAccountInfoByCustomerCode(customerCode);
            wmsApiClient.getAccountInfoByCustomerCode(customerCode);

            // 验证两个请求的nonce不同
            RecordedRequest request1 = takeRequest();
            RecordedRequest request2 = takeRequest();

            String nonce1 = request1.getHeader(WmsFulfillmenAPI.X_NONCE);
            String nonce2 = request2.getHeader(WmsFulfillmenAPI.X_NONCE);

            assertNotNull(nonce1);
            assertNotNull(nonce2);
            assertNotEquals(nonce1, nonce2, "两次请求的nonce应该不同");
        }
    }

    // ==================== 账户费用退款集成测试 ====================

    @Nested
    @DisplayName("账户费用退款集成测试")
    class AccountRefundIntegrationTests {

        @Test
        @DisplayName("账户费用退款 - 端到端测试")
        void testRefundAccountInfoByPurchase_EndToEnd() throws Exception {
            // 准备测试数据
            WmsAccountChargeV2Req request = WmsAccountChargeV2Req.builder()
                .cusCode("TEST001")
                .orderNo("ORDER20250131001")
                .operationType(WmsOperationTypeEnum.REFUND)
                .refundAmount(165.00)
                .refundReason("集成测试退款")
                .createUser("integrationTestUser")
                .remark("集成测试账户退款")
                .build();

            WmsAccountChargeInfoRes expectedData = new WmsAccountChargeInfoRes();
            expectedData.setCusCode("TEST001");
            expectedData.setOrderNo("ORDER20250131001");
            expectedData.setPaymentNumber("REF20250131001");
            expectedData.setBalanceAfterCharge(BigDecimal.valueOf(1165.00));
            expectedData.setPaymentDetailId(12346);

            WmsApiResponse<WmsAccountChargeInfoRes> mockResponse = createSuccessResponse(expectedData);
            mockWebServer.enqueue(createMockResponse(mockResponse));

            // 执行测试
            WmsAccountChargeInfoRes result = wmsApiClient.refundAccountInfoByPurchase(request);

            // 验证结果
            assertNotNull(result);
            assertEquals("TEST001", result.getCusCode());
            assertEquals("ORDER20250131001", result.getOrderNo());
            assertEquals("REF20250131001", result.getPaymentNumber());
            assertEquals(BigDecimal.valueOf(1165.00), result.getBalanceAfterCharge());
            assertEquals(Integer.valueOf(12346), result.getPaymentDetailId());

            // 验证HTTP请求
            RecordedRequest recordedRequest = takeRequest();
            assertEquals("POST", recordedRequest.getMethod());
            assertTrue(recordedRequest.getPath().contains("/account/charge"));

            // 验证请求头
            assertNotNull(recordedRequest.getHeader(WmsFulfillmenAPI.X_TIMESTAMP));
            assertNotNull(recordedRequest.getHeader(WmsFulfillmenAPI.X_NONCE));
            assertNotNull(recordedRequest.getHeader(WmsFulfillmenAPI.X_SIGNATURE));
            assertEquals("application/json", recordedRequest.getHeader("Content-Type"));
        }

        @Test
        @DisplayName("账户费用扣减 - 端到端测试")
        void testDeductAccountInfoByPurchase_EndToEnd() throws Exception {
            // 准备测试数据
            WmsAccountChargeV2Req request = WmsAccountChargeV2Req.builder()
                .cusCode("TEST001")
                .orderNo("ORDER20250131002")
                .operationType(WmsOperationTypeEnum.CHARGE)
                .productAmount(120.00)
                .serviceFee(30.00)
                .shippingFee(15.00)
                .originalTotalPrice(165.00)
                .createUser("integrationTestUser")
                .remark("集成测试账户扣减")
                .build();

            WmsAccountChargeInfoRes expectedData = new WmsAccountChargeInfoRes();
            expectedData.setCusCode("TEST001");
            expectedData.setOrderNo("ORDER20250131002");
            expectedData.setPaymentNumber("PAY20250131002");
            expectedData.setBalanceAfterCharge(BigDecimal.valueOf(835.00));
            expectedData.setPaymentDetailId(12347);

            WmsApiResponse<WmsAccountChargeInfoRes> mockResponse = createSuccessResponse(expectedData);
            mockWebServer.enqueue(createMockResponse(mockResponse));

            // 执行测试
            WmsAccountChargeInfoRes result = wmsApiClient.deductAccountInfoByPurchase(request);

            // 验证结果
            assertNotNull(result);
            assertEquals("TEST001", result.getCusCode());
            assertEquals("ORDER20250131002", result.getOrderNo());
            assertEquals("PAY20250131002", result.getPaymentNumber());
            assertEquals(BigDecimal.valueOf(835.00), result.getBalanceAfterCharge());
            assertEquals(Integer.valueOf(12347), result.getPaymentDetailId());

            // 验证HTTP请求
            RecordedRequest recordedRequest = takeRequest();
            assertEquals("POST", recordedRequest.getMethod());
            assertTrue(recordedRequest.getPath().contains("/account/charge"));
        }

        @Test
        @DisplayName("账户费用操作 - 网络异常处理")
        void testAccountChargeOperations_NetworkError() {
            WmsAccountChargeV2Req request = WmsAccountChargeV2Req.builder()
                .cusCode("TEST001")
                .orderNo("ORDER20250131003")
                .operationType(WmsOperationTypeEnum.REFUND)
                .refundAmount(100.00)
                .build();

            // 模拟网络错误 - 不添加任何响应到队列
            mockWebServer.enqueue(createErrorResponse(500, "Internal Server Error"));

            // 执行测试并验证异常
            assertThrows(WmsApiException.class, () -> {
                wmsApiClient.refundAccountInfoByPurchase(request);
            });
        }
    }

    // ==================== 入库管理集成测试 ====================

    @Nested
    @DisplayName("入库管理集成测试")
    class InboundManagementIntegrationTests {

        @Test
        @DisplayName("创建入库单 - 端到端测试")
        void testInboundCreateByPurchase_EndToEnd() throws Exception {
            // 准备测试数据
            WmsCreateInboundOrderReq request = WmsCreateInboundOrderReq.builder()
                .cusCode("TEST001")
                .purchaseNo("PO20250131001")
                .asnType("PURCHASE")
                .warehouseAddress("深圳仓库")
                .transportWay("EXPRESS")
                .expressCompany("顺丰速运")
                .expressNumber("SF*********0")
                .remark("集成测试创建入库单")
                .createUser("integrationTestUser")
                .build();

            WmsCreateInboundOrderRes expectedData = new WmsCreateInboundOrderRes();
            expectedData.setAsnId(12345L);
            expectedData.setAsnNumber("ASN20250131001");
            expectedData.setCreateTime(LocalDateTime.now());
            expectedData.setStatus("CREATED");

            WmsApiResponse<WmsCreateInboundOrderRes> mockResponse = createSuccessResponse(expectedData);
            mockWebServer.enqueue(createMockResponse(mockResponse));

            // 执行测试
            WmsCreateInboundOrderRes result = wmsApiClient.inboundCreateByPurchase(request);

            // 验证结果
            assertNotNull(result);
            assertEquals(Long.valueOf(12345L), result.getAsnId());
            assertEquals("ASN20250131001", result.getAsnNumber());
            assertEquals("CREATED", result.getStatus());
            assertNotNull(result.getCreateTime());

            // 验证HTTP请求
            RecordedRequest recordedRequest = takeRequest();
            assertEquals("POST", recordedRequest.getMethod());
            assertTrue(recordedRequest.getPath().contains("/inbound/create"));

            // 验证请求头
            assertNotNull(recordedRequest.getHeader(WmsFulfillmenAPI.X_TIMESTAMP));
            assertNotNull(recordedRequest.getHeader(WmsFulfillmenAPI.X_NONCE));
            assertNotNull(recordedRequest.getHeader(WmsFulfillmenAPI.X_SIGNATURE));
        }

        @Test
        @DisplayName("更新入库单 - 端到端测试")
        void testInboundUpdateByPurchase_EndToEnd() throws Exception {
            // 准备测试数据
            WmsInboundOrderUpdateReq request = WmsInboundOrderUpdateReq.builder()
                .asnId(12345L)
                .asnNumber("ASN20250131001")
                .transportWay("TRUCK")
                .expressCompany("德邦物流")
                .expressNumber("DP*********0")
                .remark("集成测试更新入库单")
                .modifyUser("integrationTestUser")
                .build();

            WmsInboundUpdateRes expectedData = new WmsInboundUpdateRes();
            expectedData.setAsnId(12345L);
            expectedData.setAsnNumber("ASN20250131001");
            expectedData.setUpdateTime(LocalDateTime.now());
            expectedData.setStatus("UPDATED");

            WmsApiResponse<WmsInboundUpdateRes> mockResponse = createSuccessResponse(expectedData);
            mockWebServer.enqueue(createMockResponse(mockResponse));

            // 执行测试
            WmsInboundUpdateRes result = wmsApiClient.inboundUpdateByPurchase(request);

            // 验证结果
            assertNotNull(result);
            assertEquals(Long.valueOf(12345L), result.getAsnId());
            assertEquals("ASN20250131001", result.getAsnNumber());
            assertEquals("UPDATED", result.getStatus());
            assertNotNull(result.getUpdateTime());

            // 验证HTTP请求
            RecordedRequest recordedRequest = takeRequest();
            assertEquals("POST", recordedRequest.getMethod());
            assertTrue(recordedRequest.getPath().contains("/inbound/update"));
        }

        @Test
        @DisplayName("分页查询入库单 - 端到端测试")
        void testInboundQueryPage_EndToEnd() throws Exception {
            // 准备测试数据
            WmsInboundQueryOrderPageReq request = WmsInboundQueryOrderPageReq.builder()
                .cusCode("TEST001")
                .page(1L)
                .pageSize(10L)
                .queryConditions(WmsInboundQueryOrderPageReq.ASNListQueryConditions.builder()
                    .asnNumber("ASN20250131001")
                    .purchaseNo("PO20250131001")
                    .build())
                .build();

            WmsPageDTO<WmsInboundQueryPageRes> expectedData = WmsPageDTO.<WmsInboundQueryPageRes>builder()
                .total(2)
                .pageIndex(1)
                .pageSize(10)
                .totalPages(1)
                .records(Arrays.asList(
                    createMockInboundQueryPageRes("ASN20250131001", "PO20250131001"),
                    createMockInboundQueryPageRes("ASN20250131002", "PO20250131002")
                ))
                .build();

            WmsApiResponse<WmsPageDTO<WmsInboundQueryPageRes>> mockResponse = createSuccessResponse(expectedData);
            mockWebServer.enqueue(createMockResponse(mockResponse));

            // 执行测试
            WmsPageDTO<WmsInboundQueryPageRes> result = wmsApiClient.inboundQueryPage(request);

            // 验证结果
            assertNotNull(result);
            assertEquals(Integer.valueOf(2), result.getTotal());
            assertEquals(Integer.valueOf(1), result.getPageIndex());
            assertEquals(Integer.valueOf(10), result.getPageSize());
            assertEquals(2, result.getRecords().size());
            assertEquals("ASN20250131001", result.getRecords().get(0).getAsnNumber());

            // 验证HTTP请求
            RecordedRequest recordedRequest = takeRequest();
            assertEquals("POST", recordedRequest.getMethod());
            assertTrue(recordedRequest.getPath().contains("/inbound/queryPage"));
        }

        @Test
        @DisplayName("查询入库单详情 - 端到端测试")
        void testInboundQuery_EndToEnd() throws Exception {
            // 准备测试数据
            WmsInboundInfoReq request = WmsInboundInfoReq.builder()
                .asnId(12345L)
                .asnNumber("ASN20250131001")
                .cusCode("TEST001")
                .build();

            WmsInboundInfoRes expectedData = createMockInboundInfoRes("ASN20250131001", "PO20250131001");

            WmsApiResponse<WmsInboundInfoRes> mockResponse = createSuccessResponse(expectedData);
            mockWebServer.enqueue(createMockResponse(mockResponse));

            // 执行测试
            WmsInboundInfoRes result = wmsApiClient.inboundQuery(request);

            // 验证结果
            assertNotNull(result);
            assertEquals(Long.valueOf(12345L), result.getAsnId());
            assertEquals("ASN20250131001", result.getAsnNumber());
            assertEquals("PO20250131001", result.getPurchaseNo());
            assertEquals("TEST001", result.getChargePerson());
            assertNotNull(result.getAsnDetails());
            assertEquals(2, result.getAsnDetails().size());

            // 验证HTTP请求
            RecordedRequest recordedRequest = takeRequest();
            assertEquals("POST", recordedRequest.getMethod());
            assertTrue(recordedRequest.getPath().contains("/inbound/query"));
        }

        @Test
        @DisplayName("入库管理 - 网络异常处理")
        void testInboundManagement_NetworkError() {
            WmsCreateInboundOrderReq request = WmsCreateInboundOrderReq.builder()
                .cusCode("TEST001")
                .purchaseNo("PO20250131001")
                .build();

            // 模拟网络错误
            mockWebServer.enqueue(createErrorResponse(500, "Internal Server Error"));

            // 执行测试并验证异常
            assertThrows(WmsApiException.class, () -> {
                wmsApiClient.inboundCreateByPurchase(request);
            });
        }

        @Test
        @DisplayName("入库管理 - 业务异常处理")
        void testInboundManagement_BusinessError() throws Exception {
            WmsCreateInboundOrderReq request = WmsCreateInboundOrderReq.builder()
                .cusCode("INVALID001")
                .purchaseNo("INVALID_PO")
                .build();

            // 模拟业务异常响应
            WmsApiResponse<WmsCreateInboundOrderRes> errorResponse = new WmsApiResponse<>();
            errorResponse.setSuccess("false");
            errorResponse.setCode(1001);
            errorResponse.setMessage("客户代码不存在");

            mockWebServer.enqueue(createMockResponse(errorResponse));

            // 执行测试并验证异常
            assertThrows(WmsApiException.class, () -> {
                wmsApiClient.inboundCreateByPurchase(request);
            });

            // 验证请求仍然发送
            RecordedRequest recordedRequest = takeRequest();
            assertEquals("POST", recordedRequest.getMethod());
        }

        // ==================== 入库管理辅助方法 ====================

        private WmsInboundQueryPageRes createMockInboundQueryPageRes(String asnNumber, String purchaseNo) {
            WmsInboundQueryPageRes res = new WmsInboundQueryPageRes();
            res.setAsnId(Long.valueOf(Math.abs(asnNumber.hashCode())));
            res.setAsnNumber(asnNumber);
            res.setPurchaseNo(purchaseNo);
            res.setCreateTime(LocalDateTime.now().toString());
            res.setCreateUser("integrationTestUser");
            return res;
        }

        private WmsInboundInfoRes createMockInboundInfoRes(String asnNumber, String purchaseNo) {
            WmsInboundInfoRes inboundInfo = new WmsInboundInfoRes();
            inboundInfo.setAsnId(12345L);
            inboundInfo.setAsnNumber(asnNumber);
            inboundInfo.setPurchaseNo(purchaseNo);
            inboundInfo.setAsnType("PURCHASE");
            inboundInfo.setChargePerson("TEST001");
            inboundInfo.setCreateTime(java.time.OffsetDateTime.now());
            inboundInfo.setCreateUser("integrationTestUser");
            inboundInfo.setWarehouseAddress("深圳仓库");
            inboundInfo.setTransportWay("EXPRESS");
            inboundInfo.setExpressCompany("顺丰速运");
            inboundInfo.setExpressNumber("SF*********0");
            inboundInfo.setRemark("集成测试入库单");
            inboundInfo.setVoidFlag(false);

            // 创建入库单明细
            List<WmsInboundInfoRes.ASNDetail> details = Arrays.asList(
                createMockASNDetail(1L, "商品1", "Product1", 10L, 8L),
                createMockASNDetail(2L, "商品2", "Product2", 20L, 18L)
            );
            inboundInfo.setAsnDetails(details);

            return inboundInfo;
        }

        private WmsInboundInfoRes.ASNDetail createMockASNDetail(Long goodsId, String cnName, String enName, Long shouldQuantity, Long factQuantity) {
            WmsInboundInfoRes.ASNDetail detail = new WmsInboundInfoRes.ASNDetail();
            detail.setAsnDetailId(goodsId * 100);
            detail.setAsnMainId(12345L);
            detail.setGoodsId(goodsId);
            detail.setCnName(cnName);
            detail.setEnName(enName);
            detail.setShouldQuantity(shouldQuantity);
            detail.setFactQuantity(factQuantity);
            detail.setOkQuantity(factQuantity);
            detail.setPrice(99.99);
            detail.setRefWeight(1.5);
            detail.setFactWeight(1.4);
            detail.setGoodsStatus("NORMAL");
            detail.setCreateTime(java.time.OffsetDateTime.now());
            detail.setVoidFlag(false);
            return detail;
        }
    }
}
