# WMS API Client 测试配置文件
# 用于单元测试和集成测试的配置

# Spring 配置
spring:
  profiles:
    active: test
  
  # 日志配置
  logging:
    level:
      com.fulfillmen.support.wms: DEBUG
      org.springframework.web.reactive: DEBUG
      reactor.netty: INFO
    pattern:
      console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# WMS API 配置
fulfillmen:
  wms:
    # 测试环境配置
    base-url: "http://localhost:8080"
    app-key: "test-app-key"
    app-secret: "test-app-secret"
    
    # 连接配置
    connection:
      timeout: 30s
      read-timeout: 60s
      write-timeout: 60s
      max-connections: 100
      max-idle-time: 30s
    
    # 重试配置
    retry:
      max-attempts: 3
      backoff-delay: 1s
      max-backoff-delay: 10s
      multiplier: 2.0
    
    # 熔断器配置
    circuit-breaker:
      enabled: true
      failure-rate-threshold: 50
      wait-duration-in-open-state: 30s
      sliding-window-size: 10
      minimum-number-of-calls: 5
    
    # 监控配置
    metrics:
      enabled: true
      export-interval: 30s
    
    # 测试专用配置
    test:
      mock-server:
        enabled: true
        port: 8080
        response-delay: 100ms
      
      # 测试数据配置
      data:
        default-customer-code: "TEST001"
        default-order-prefix: "ORDER"
        default-purchase-prefix: "PO"
        default-asn-prefix: "ASN"
      
      # 验证配置
      validation:
        strict-mode: true
        check-required-fields: true
        validate-formats: true

# 测试框架配置
junit:
  jupiter:
    execution:
      parallel:
        enabled: true
        mode:
          default: concurrent
        config:
          strategy: dynamic
          factor: 1.0
    
    testinstance:
      lifecycle:
        default: per_class

# MockWebServer 配置
mock:
  web-server:
    port: 0  # 随机端口
    response-delay: 50ms
    max-connections: 50
    
    # 默认响应配置
    default-responses:
      success:
        code: 0
        message: "成功"
        success: "success"
      
      failure:
        code: 1
        message: "API调用失败"
        success: "false"
      
      error:
        code: 500
        message: "服务器内部错误"
        success: "false"

# 测试数据配置
test:
  data:
    # 账户费用测试数据
    account:
      default-balance: 1000.00
      refund-amount: 165.00
      charge-amount: 200.00
      payment-detail-id-start: 10000
    
    # 入库管理测试数据
    inbound:
      default-warehouse: "深圳仓库"
      default-transport-way: "EXPRESS"
      default-express-company: "顺丰速运"
      asn-id-start: 10000
      goods-id-start: 1000
    
    # 分页测试数据
    pagination:
      default-page-size: 10
      max-page-size: 100
      default-page-index: 1
    
    # 时间配置
    time:
      default-start-days-ago: 30
      default-end-days-ago: 0
      timestamp-tolerance-ms: 5000

# 性能测试配置
performance:
  test:
    enabled: false
    concurrent-users: 10
    test-duration: 60s
    ramp-up-time: 10s
    
    # 响应时间阈值
    thresholds:
      average-response-time: 500ms
      max-response-time: 2000ms
      error-rate: 5%

# 集成测试配置
integration:
  test:
    # 外部服务配置
    external-services:
      wms-api:
        enabled: true
        base-url: "http://localhost:8080"
        health-check-path: "/health"
        timeout: 30s
    
    # 数据库配置（如果需要）
    database:
      enabled: false
      cleanup-after-test: true
      use-test-containers: true
    
    # 消息队列配置（如果需要）
    messaging:
      enabled: false
      use-embedded-broker: true

# 安全测试配置
security:
  test:
    # 签名测试
    signature:
      test-app-key: "test-app-key"
      test-app-secret: "test-app-secret"
      algorithm: "HmacSHA256"
      
    # 认证测试
    authentication:
      valid-tokens:
        - "valid-token-1"
        - "valid-token-2"
      invalid-tokens:
        - "invalid-token"
        - "expired-token"

# 监控和指标配置
monitoring:
  test:
    enabled: true
    
    # 指标收集
    metrics:
      request-count: true
      response-time: true
      error-rate: true
      circuit-breaker-state: true
    
    # 健康检查
    health:
      enabled: true
      interval: 10s
      timeout: 5s

# 调试配置
debug:
  test:
    # HTTP 请求/响应日志
    http-logging:
      enabled: true
      log-headers: true
      log-body: true
      max-body-size: 1024
    
    # 详细错误信息
    verbose-errors: true
    
    # 测试执行跟踪
    execution-trace: true
