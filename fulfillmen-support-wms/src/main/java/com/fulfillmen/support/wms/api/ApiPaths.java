/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.api;

/**
 * WMS API 接口路径常量定义
 *
 * <p>统一管理所有WMS API的接口路径，避免硬编码，提高代码可维护性。
 * 路径按功能模块分组：账户管理、采购订单、产品管理等。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
public interface ApiPaths {

    // ==================== 账户管理相关API ====================

    /**
     * 通过授权码获取账户信息
     */
    String GET_ACCOUNT_INFO_BY_AUTH_CODE = "/Alibaba/OpenApi/AuthLogin.ashx";

    /**
     * 通过客户码获取账户信息
     */
    String GET_ACCOUNT_INFO_BY_CUS_CODE = "/Alibaba/OpenApi/GetAccountInfo.ashx";

    /**
     * 采购商品扣减账户余额
     */
    String DEDUCT_ACCOUNT_INFO_BY_PURCHASE = "/Alibaba/OpenApi/DeductAccountInfoByPurchase.ashx";

    /**
     * 更新账户余额
     * <p>
     * 处理账户费用或退款
     * </p>
     */
    String UPDATE_ACCOUNT_BALANCE = "/Alibaba/OpenApi/CustomerCharge.ashx";

    // ==================== 采购订单相关API ====================

    /**
     * 创建采购订单
     */
    String PURCHASE_ORDER_CREATE = "/Alibaba/OpenApi/PurchaseOrderCreate.ashx";

    /**
     * 查询采购订单（POST方式） 分页
     */
    String PURCHASE_ORDER_QUERY_POST = "/Alibaba/OpenApi/PurchaseOrderQuery.ashx";

    /**
     * 查询采购订单详情（GET方式）
     */
    String PURCHASE_ORDER_QUERY_GET = "/Alibaba/OpenApi/PurchaseOrderQuery.ashx";

    /**
     * 更新采购订单
     */
    String PURCHASE_ORDER_UPDATE = "/Alibaba/OpenApi/PurchaseOrdersUpdate.ashx";

    // ==================== 采购入库相关API ====================

    /**
     * 入库单创建
     */
    String INBOUND_ORDER_CREATE = "/Alibaba/OpenApi/CreateASN.ashx";

    /**
     * 入库单更新
     */
    String INBOUND_ORDER_UPDATE = "/Alibaba/OpenApi/UpdateASN.ashx";

    /**
     * 入库单查询
     */
    String INBOUND_ORDER_QUERY_PAGE = "/Alibaba/OpenApi/GetASNList.ashx";

    /**
     * 入库单查询
     */
    String INBOUND_ORDER_INFO = "/Alibaba/OpenApi/GetASNQuery.ashx";

    // ==================== 产品管理相关API ====================

    /**
     * 创建产品
     */
    String PRODUCT_CREATE = "/Alibaba/OpenApi/CreateGoods.ashx";

    /**
     * 分页获取产品信息
     */
    String PRODUCT_INFO_PAGE = "/Alibaba/OpenApi/GetGoodsQuery.ashx";

    /**
     * 获取产品信息列表
     */
    String PRODUCT_INFO_LIST = "/Alibaba/OpenApi/GetGoodsList.ashx";

    /**
     * 批量更新产品信息
     */
    String PRODUCT_BATCH_UPDATE = "/Alibaba/OpenApi/GoodsUpdate.ashx";
}
