package com.fulfillmen.support.wms.dto.response;

import com.fulfillmen.support.wms.dto.enums.WmsAnsStatusEnums;
import java.time.OffsetDateTime;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/31 16:16
 * @description: todo
 * @since 1.0.0
 */
@Data
public class WmsInbondQueryPageRes {

    /**
     * 审核时间
     */
    private String approveTime;
    /**
     * 审核人
     */
    private String approveUser;
    /**
     * 入库单ID
     */
    private Long asnId;
    /**
     * 入库单号
     */
    private String asnNumber;
    /**
     * 入库单状态
     */
    private WmsAnsStatusEnums asnStatus;
    /**
     * 状态中文描述
     */
    private String asnStatusText;
    /**
     * 入库类型
     */
    private String asnType;
    /**
     * 经手人
     */
    private String chargePerson;
    /**
     * 收货时间
     */
    private String chargeTime;
    /**
     * 完成时间
     */
    private OffsetDateTime completionTime;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 收货人
     */
    private String deliveryer;
    /**
     * 送货方式
     */
    private String deliveryStyle;
    /**
     * 收货时间
     */
    private String deliveryTime;
    /**
     * 箱数
     */
    private Long enchaseNum;
    /**
     * 入库类型
     */
    private String enchaseType;
    /**
     * 快递公司
     */
    private String expressCompany;
    /**
     * 头程服务单号
     */
    private String expressNumber;
    /**
     * 扣费状态
     */
    private Long feeStatus;
    /**
     * 标记
     */
    private String field5;
    /**
     * 是否审批
     */
    private Long isApproval;
    /**
     * 是否完整（1表示完整，2表示不完整）
     */
    private String isComplete;
    /**
     * 最后修改时间
     */
    private String modifyTime;
    /**
     * 最后修改人
     */
    private String modifyUser;
    /**
     * 所属公司
     */
    private String office;
    /**
     * 上架费
     */
    private Double onFrameFee;
    /**
     * 采购单号
     */
    private String purchaseNo;
    /**
     * 预计到达时间
     */
    private String reachStartTime;
    /**
     * 参考号
     */
    private String refNo;
    /**
     * 备注
     */
    private String remark;
    /**
     * 退货状态
     */
    private Long returnsStatus;
    /**
     * 增值服务单号
     */
    private String serviceNo;
    /**
     * 所属类型
     */
    private Long style;
    /**
     * 运单号
     */
    private String trackingNo;
    /**
     * 运输方式
     */
    private String transportWay;
    /**
     * 作废标志
     */
    private Boolean voidFlag;
    /**
     * 仓库地址
     */
    private String warehouseAddress;

}
