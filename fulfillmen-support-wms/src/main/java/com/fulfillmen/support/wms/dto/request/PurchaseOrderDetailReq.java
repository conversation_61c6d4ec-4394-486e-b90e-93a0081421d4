/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 采购订单详情查询请求
 * 
 * <p>定义采购订单详情查询的请求参数，支持通过采购单号、订单ID或WMS采购单号查询。
 * 三个参数至少需要提供一个。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrderDetailReq {

    /**
     * WMS采购单号，支持多个值（逗号分隔）
     */
    private String purchaseNo;

    /**
     * 1688订单号，支持多个值（逗号分隔）
     */
    private String orderId;

    /**
     * naya 采购单号，支持多个值（逗号分隔）
     */
    private String nayaPurchaseNo;
}
