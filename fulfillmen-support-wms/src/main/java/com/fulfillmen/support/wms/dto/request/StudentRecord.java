/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

/**
 * student record
 *
 * @param id     学生ID
 * @param name   学生姓名
 * @param age    学生年龄
 * @param gender 学生性别
 * @param email  学生邮箱
 * <AUTHOR>
 * @date 2025/7/30 11:22
 * @description: todo
 * @since 1.0.0
 */
@Builder
public record StudentRecord(
    @JsonProperty("id") Long id,
    @JsonProperty("stuName") String name,
    @JsonProperty("stuAge") int age,
    @JsonProperty("stuGender") String gender,
    String email
) {

    public static void main(String[] args) {
        StudentRecord studentRecord = StudentRecord.builder().id(1L).name("张三").age(18).gender("男").email("<EMAIL>").build();
        System.out.println(studentRecord);
    }
}
