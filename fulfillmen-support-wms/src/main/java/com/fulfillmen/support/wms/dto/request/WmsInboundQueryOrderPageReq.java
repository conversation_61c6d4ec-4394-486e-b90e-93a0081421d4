/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import com.fulfillmen.support.wms.dto.enums.WmsAnsStatusEnums;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WMS 入库单查询请求
 *
 * <AUTHOR>
 * @date 2025/7/31 16:16
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WmsInboundQueryOrderPageReq {

    /**
     * 客户编码（必填）
     */
    private String cusCode;
    /**
     * 页码（从1开始）
     */
    private Long page;
    /**
     * 每页记录数（最大100）
     */
    private Long pageSize;
    /**
     * 查询条件
     */
    private ASNListQueryConditions queryConditions;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ASNListQueryConditions {

        /**
         * 入库单号（支持模糊查询）
         */
        private String asnNumber;
        /**
         * 入库单状态
         */
        private WmsAnsStatusEnums asnStatus;
        /**
         * 采购单号
         */
        private String purchaseNo;
        /**
         * 运单号（支持模糊查询）
         */
        private String trackingNo;
    }
}
