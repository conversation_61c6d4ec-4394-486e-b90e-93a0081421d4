/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.api;

import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.wms.dto.common.WmsApiResponse;
import com.fulfillmen.support.wms.exception.WmsApiException;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * WMS API响应处理工具类
 *
 * <AUTHOR>
 * @created 2025-07-29
 */
@Slf4j
public final class WmsResponseHandler {

    private static final String SERVICE_NAME = "WMS";

    /**
     * 处理WMS API响应
     *
     * @param mono          响应Mono
     * @param operationName 操作名称
     * @param request       请求
     * @param <T>           响应类型
     * @return 响应
     */
    public static <T> T handleWmsResponse(Mono<WmsApiResponse<T>> mono, String operationName, Object request) {
        return processWmsApiResponseHandler(mono, operationName, request).block();
    }

    /**
     * 处理WMS API响应 非阻塞方式
     *
     * @param mono          响应Mono
     * @param operationName 操作名称
     * @param request       请求
     * @param <T>           响应类型
     * @return 响应Mono
     */
    public static <T> Mono<T> handleWmsResponseMono(Mono<WmsApiResponse<T>> mono, String operationName,
        Object request) {
        return processWmsApiResponseHandler(mono, operationName, request);
    }

    /**
     * 处理无数据返回的WMS API响应
     *
     * @param mono          响应Mono
     * @param operationName 操作名称
     * @param request       请求
     */
    public static void handleVoidWmsResponse(Mono<WmsApiResponse<Void>> mono, String operationName, Object request) {
        handleWmsResponse(mono.map(response -> {
            // 将 Void 类型转换为 Object，便于统一处理
            return response;
        }), operationName, request);
    }

    /**
     * 处理WMS API响应
     *
     * @param mono          响应Mono
     * @param operationName 操作名称
     * @param request       请求
     * @param <T>           响应类型
     * @return 响应Mono
     */
    private static <T> Mono<T> processWmsApiResponseHandler(Mono<WmsApiResponse<T>> mono, String operationName, Object request) {
        return mono
            .doOnSubscribe(sub -> log.debug("[{}] {} 请求开始: request=[{}]", SERVICE_NAME, operationName, JacksonUtil.toJsonString(request)))
            .<T>handle((response, sink) -> {
                if (response == null || !response.isSuccess()) {
                    String errorMsg = String.format("[%s] %s API调用失败，响应为空或失败", SERVICE_NAME, operationName);
                    log.error("{}: request=[{}], response=[{}]", errorMsg, request, response);
                    sink.error(new WmsApiException(errorMsg));
                    return;
                }
                log.debug("[{}] {} API调用成功: request=[{}]", SERVICE_NAME, operationName, request);
                
                // 处理data为null的情况，避免Reactor内部的requireNonNull检查导致NPE
                T data = response.getData();
                if (data != null) {
                    sink.next(data);
                } else {
                    // 对于void操作或其他允许返回null的场景，调用complete()而不是next(null)
                    log.debug("[{}] {} API返回数据为null，操作成功完成", SERVICE_NAME, operationName);
                    sink.complete();
                }
            })
            .doOnError(error -> {
                if (!(error instanceof WmsApiException)) {
                    log.error("[{}] {} API调用异常: request=[{}], error=[{}]",
                        SERVICE_NAME, operationName, request, error.getMessage(), error);
                }
            })
            .onErrorMap(e -> e instanceof WmsApiException ? e
                : new WmsApiException(
                    String.format("[%s] %s API调用失败", SERVICE_NAME, operationName), e));
    }
}
