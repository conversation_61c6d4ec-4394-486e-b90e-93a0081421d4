/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * asn 入库单状态枚举
 *
 * <AUTHOR>
 * @date 2025/7/31 16:21
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum WmsAnsStatusEnums {

    /// 1,10,11 未收货 12 已扫描入库 2 仓库已收货 3 已质检 4 已上架 8 已完成 6 作废
    NOT_RECEIVED(1, "未收货"),
    RECEIVED(2, "仓库已收货"),
    QUALITY_CHECKED(3, "已质检"),
    STOCKED(4, "已上架"),
    CANCELED(6, "已作废"),
    COMPLETED(8, "已完成"),
    PENDING_RECEIPT(10, "待收货"),
    PARTIALLY_RECEIVED(11, "部分收货"),
    SCANNED(12, "已扫描入库"),

    ;

    @JsonValue
    private final int code;
    private final String desc;

    WmsAnsStatusEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
