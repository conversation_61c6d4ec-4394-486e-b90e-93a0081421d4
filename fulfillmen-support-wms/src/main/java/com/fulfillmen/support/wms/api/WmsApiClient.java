/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.api;

import com.fulfillmen.support.wms.dto.common.WmsPageDTO;
import com.fulfillmen.support.wms.dto.response.WmsProductRes;
import com.fulfillmen.support.wms.dto.request.WmsAccountChargeV2Req;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateGoodsReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateOrderReq;
import com.fulfillmen.support.wms.dto.request.WmsOrderQueryReq;
import com.fulfillmen.support.wms.dto.request.WmsProductInfoReq;
import com.fulfillmen.support.wms.dto.request.WmsProductQueryReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.response.WmsAccountChargeInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsAccountInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsCreatePurchaseOrderRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderInfoRes;
import com.fulfillmen.support.wms.exception.WmsApiException;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * WMS API客户端
 *
 * <p>
 * 封装WMS API的调用逻辑，使用声明式HTTP接口进行API调用。 提供账户管理、订单管理、产品管理等功能的API调用方法。
 * </p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WmsApiClient {

    private final WmsFulfillmenAPI wmsFulfillmenAPI;

    // ==================== 账户管理相关API ====================

    /**
     * 通过授权码获取账户信息
     *
     * @param authCode 授权码
     * @return 解密后的账户信息
     * @throws WmsApiException 当API调用失败时抛出
     */
    public WmsAccountInfoRes getAccountInfoByAuthCode(String authCode) {
        try {
            log.debug("调用WMS通过授权码获取账户信息API: authCode=[{}]", authCode);

            return WmsResponseHandler.handleWmsResponse(wmsFulfillmenAPI.getAccountInfoByAuthCode(authCode),
                "WMS获取账户信息API", authCode);
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS获取账户信息API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS获取账户信息API调用失败", e);
        }
    }

    /**
     * 通过客户码获取账户信息
     *
     * @param customerCode 客户码
     * @return 账户信息
     * @throws WmsApiException 当API调用失败时抛出
     */
    public WmsAccountInfoRes getAccountInfoByCustomerCode(String customerCode) {
        try {
            log.debug("调用WMS通过客户码获取账户信息API: customerCode=[{}]", customerCode);

            return WmsResponseHandler.handleWmsResponse(wmsFulfillmenAPI.getAccountInfoByCustomerCode(customerCode),
                "WMS获取账户信息API", customerCode);
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS获取账户信息API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS获取账户信息API调用失败", e);
        }
    }

    /**
     * 采购商品扣减账户余额
     *
     * @param request 扣减请求
     * @return API响应结果
     * @throws WmsApiException 当API调用失败时抛出
     */
    public WmsAccountChargeInfoRes deductAccountInfoByPurchase(WmsAccountChargeV2Req request) {
        try {
            log.debug("调用WMS扣减账户余额API: {}", request);
            return WmsResponseHandler.handleWmsResponse(wmsFulfillmenAPI.accountChargeByPurchase(request),
                "WMS扣减账户余额API", request);
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS扣减账户余额API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS扣减账户余额API调用失败", e);
        }
    }

    /**
     * 采购商品费用退款
     *
     * @param request 扣减请求
     * @return API响应结果
     * @throws WmsApiException 当API调用失败时抛出
     */
    public WmsAccountChargeInfoRes refundAccountInfoByPurchase(WmsAccountChargeV2Req request) {
        try {
            log.debug("调用WMS退还采购费用API: {}", request);
            return WmsResponseHandler.handleWmsResponse(wmsFulfillmenAPI.accountChargeByPurchase(request),
                "WMS返还账户余额API", request);
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS返还账户余额API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS返还账户余额API调用失败", e);
        }
    }

    // ==================== 采购订单相关API ====================

    /**
     * 创建采购订单
     *
     * @param request 创建订单请求
     * @return 采购数据响应
     * @throws WmsApiException 当API调用失败时抛出
     */
    public List<WmsCreatePurchaseOrderRes> createPurchaseOrderNew(WmsCreateOrderReq request) {
        try {
            log.debug("调用WMS创建采购订单API: customerCode=[{}], orderCount=[{}]",
                request.getCustomerCode(), request.getOrders().size());

            return WmsResponseHandler.handleWmsResponse(wmsFulfillmenAPI.createPurchaseOrderNew(request),
                "WMS创建采购订单API", request);
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS创建采购订单API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS创建采购订单API调用失败", e);
        }
    }

    /**
     * 更新 采购订单。
     *
     * @param request 创建订单请求
     * @return 采购数据响应
     * @throws WmsApiException 当API调用失败时抛出
     */
    public void updatePurchaseOrder(List<WmsPurchaseOrderDetailReq> request) {
        try {
            // 拼接多个 purchaseNo .逗号分割
            String purchaseNos = request.stream().map(WmsPurchaseOrderDetailReq::getPurchaseNo)
                .collect(Collectors.joining(","));
            log.debug("调用 WMS 更新采购订单API: orderCount=[{}] , 更新wms 的 purchaseNos : [{}]", request.size(), purchaseNos);
            WmsResponseHandler.handleVoidWmsResponse(wmsFulfillmenAPI.updatePurchaseOrderDetails(request),
                "WMS更新采购订单API", request);
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新 WMS 采购订单API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("更新 WMS 采购订单API调用失败", e);
        }
    }

    /**
     * 查询采购订单
     *
     * @param request 订单查询请求
     * @return 分页订单信息
     * @throws WmsApiException 当API调用失败时抛出
     */
    public WmsPageDTO<WmsPurchaseOrderInfoRes> queryOrder(WmsOrderQueryReq request) {
        try {
            log.debug("调用WMS查询采购订单API: page=[{}], pageSize=[{}]", request.getPage(), request.getPageSize());

            return WmsResponseHandler.handleWmsResponse(wmsFulfillmenAPI.queryOrder(request), "WMS查询采购订单API", request);
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS查询采购订单API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS查询采购订单API调用失败", e);
        }
    }

    /**
     * 查询采购订单详情
     *
     * @param request 订单详情查询请求
     * @return 订单详情列表
     * @throws WmsApiException 当API调用失败时抛出
     */
    public List<WmsPurchaseOrderDetailsRes> queryOrderDetail(PurchaseOrderDetailReq request) {
        try {
            log.debug("调用WMS查询采购订单详情API: purchaseNo=[{}], orderId=[{}], wmsPurchaseNo=[{}]",
                request.getPurchaseNo(), request.getOrderId(), request.getNayaPurchaseNo());

            return WmsResponseHandler.handleWmsResponse(wmsFulfillmenAPI.queryOrderDetails(
                request.getPurchaseNo(),
                request.getOrderId(),
                request.getNayaPurchaseNo()), "WMS查询采购订单详情API", request);
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS查询采购订单详情API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS查询采购订单详情API调用失败", e);
        }
    }

    // ==================== 产品管理相关API ====================

    /**
     * 创建产品
     *
     * @param request 产品创建请求
     * @return API响应结果
     * @throws WmsApiException 当API调用失败时抛出
     */
    public void createProduct(WmsCreateGoodsReq request) {
        try {
            log.debug("调用WMS创建产品API: productName=[{}]", request.getCnName());

            WmsResponseHandler.handleVoidWmsResponse(wmsFulfillmenAPI.createProduct(request), "WMS创建产品API", request);
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS创建产品API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS创建产品API调用失败", e);
        }
    }

    /**
     * 获取产品列表
     *
     * @param page    页码
     * @param barcode 条码
     * @return 产品列表响应
     * @throws WmsApiException 当API调用失败时抛出
     */
    public WmsPageDTO<WmsProductRes> getProductList(Integer page, String barcode) {
        try {
            log.debug("调用WMS获取产品列表API: page=[{}], barcode=[{}]", page, barcode);

            return WmsResponseHandler.handleWmsResponse(
                wmsFulfillmenAPI.getProductByPage(WmsProductQueryReq.builder().page(page).barcode(barcode).build()),
                "WMS获取产品列表API", null);
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS获取产品列表API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS获取产品列表API调用失败", e);
        }
    }

    /**
     * 获取产品列表
     *
     * @param request 产品信息请求
     * @return 产品列表响应
     * @throws WmsApiException 当API调用失败时抛出
     */
    public List<WmsProductRes> getProductInfoList(WmsProductInfoReq request) {
        try {
            log.debug("调用WMS获取产品列表API: request=[{}]", request);

            return WmsResponseHandler.handleWmsResponse(
                wmsFulfillmenAPI.getProductInfoList(request.getCusCode(), request.getOfferId(), request.getSku()),
                "WMS获取产品列表API", null);
        } catch (WmsApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("WMS获取产品列表API调用失败: error=[{}]", e.getMessage(), e);
            throw new WmsApiException("WMS获取产品列表API调用失败", e);
        }
    }

}
