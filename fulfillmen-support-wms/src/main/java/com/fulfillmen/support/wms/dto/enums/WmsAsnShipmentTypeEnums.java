package com.fulfillmen.support.wms.dto.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/7/31 16:38
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum WmsAsnShipmentType {
//    0 一件代发 2 集货运输
    ONE_PIECE_SALE(0, "一件代发"),
    COLLECTION(2, "集货运输")
    ;
    @JsonValue
    private final int code;
    private final String desc;

    WmsAsnShipmentType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
