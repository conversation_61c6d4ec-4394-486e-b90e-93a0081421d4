/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.response;

import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.enums.WmsPayTypeEnum;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WMS采购订单信息响应
 *
 * <p>定义WMS采购订单的基本信息响应结构，包含订单号、状态、金额等信息。
 * 使用JsonAlias注解支持多种字段名格式的兼容性。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Data
@NoArgsConstructor
public class WmsPurchaseOrderInfoRes {

    /**
     * naya 采购单号
     */
    private String nayaPurchaseNo;

    /**
     * wms 采购单号
     */
    private String purchaseNo;

    /**
     * naya订单唯一值，创建成功返回wms采购单号与之对应
     * <p>
     * 对应的供应商订单 {@link TzOrderSupplier#supplierOrderNo}
     * </p>
     */
    private String shopOrderId;

    /**
     * 1688 卖家id
     */
    private String sellerOpenId;

    /**
     * buyer 买家，对应 wms 客户码
     */
    private String cusCode;

    /**
     * 仓库id
     */
    private String storeId;

    /**
     * 1688 交易流水号
     */
    private String outTradeNo;

    /**
     * 1688 订单id
     */
    private Long orderId;

    /**
     * wms 客户id
     */
    private Integer customerId;

    /**
     * 创建订单用户
     */
    private String createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 平台标识
     */
    private String platform;

    /**
     * 支付类型
     */
    private WmsPayTypeEnum payType;

    /**
     * 支付链接
     */
    private String payUrl;

    /**
     * 支付表单数据
     */
    private String payBody;

    /**
     * 平台支付类型
     */
    private String platformPayType;

    /**
     * 客服支付的运费
     */
    private BigDecimal shippingFee;

    /**
     * 1688 应付运费
     */
    private BigDecimal originalShippingFee;

    /**
     * 1688 应付商品总价 初始产品总价
     */
    private BigDecimal productOriginalTotalAmount;

    /**
     * 产品+运费
     */
    private BigDecimal total;

    /**
     * 客户支付的服务费
     */
    private BigDecimal serviceFee;

    /**
     * 商品销售总金额 - 客户支付的商品总价
     */
    private BigDecimal productSalesTotalAmount;

    /**
     * 商品最终总金额 1688 实付商品价格
     */
    private BigDecimal productFinalTotalAmount;

    /**
     * 最终运费 1688 实付运费
     */
    private BigDecimal finalShoppingFee;

    /**
     * 1688 订单应付价格
     */
    private BigDecimal originalTotalPrice;

    /**
     * 1688 订单折扣
     */
    private BigDecimal discount;
    /**
     * 1688 Plus折扣
     */
    private BigDecimal plusDiscount;
    /**
     * 1688 红包/优惠券 折扣
     */
    private BigDecimal couponDiscount;
    /**
     * 最终订单销售的总金额 客户支付的总价
     * <p>
     * 包含 total + serviceFee 服务费
     * </p>
     */
    private BigDecimal totalAmount;
    /**
     * 订单状态
     */
    private WmsOrderStatusEnum status;
    /**
     * 是否询价
     */
    private Boolean isRequestQuote;
    /**
     * 支付时间
     */
    private LocalDateTime paymentTime;
    /**
     * 发货时间
     */
    private LocalDateTime shippingTime;
    /**
     * 完成时间
     */
    private LocalDateTime completeTime;
    /**
     * 商品链接
     */
    private String link;
    /**
     * 物流单号
     */
    private String trackingNo;
    /**
     * 平台状态
     */
    private String platformStatus;
    /**
     * 平台备注
     */
    private String platformRemark;

    // ==================== 商品详情字段 ====================
//    @JsonProperty("orderDetails")
//    private List<WmsPurchaseOrderDetailsItemRes> orderDetailsItemRes;
}
