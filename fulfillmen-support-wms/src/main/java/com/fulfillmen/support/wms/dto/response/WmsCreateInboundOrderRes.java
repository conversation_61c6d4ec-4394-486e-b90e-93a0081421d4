/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建入库单成功 响应实体
 *
 * <AUTHOR>
 * @date 2025/7/31 15:46
 * @description: todo
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
public class WmsCreateInboundOrderRes {

    /**
     * asnNumber
     */
    @JsonProperty("asnNumber")
    private String asnNumber;
    /**
     * purchaseNo
     */
    @JsonProperty("purchaseNo")
    private String purchaseNo;
    /**
     * cusCode
     */
    @JsonProperty("cusCode")
    private String cusCode;
    /**
     * trackingNo
     */
    @JsonProperty("trackingNo")
    private String trackingNo;
}
