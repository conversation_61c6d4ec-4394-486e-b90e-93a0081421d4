/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.response;

import com.fulfillmen.support.wms.dto.enums.WmsAnsStatusEnums;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.List;
import lombok.Data;

/**
 * 入库单信息查询请求
 *
 * <AUTHOR>
 * @date 2025/7/31 16:48
 * @description: todo
 * @since 1.0.0
 */
@Data
public class WmsInboundInfoRes {

    /**
     * 审批时间
     */
    private OffsetDateTime approveTime;
    /**
     * 审批用户
     */
    private String approveUser;
    /**
     * 入库单明细列表
     */
    private List<ASNDetail> asnDetails;
    /**
     * 入库单主表ID
     */
    private Long asnId;
    /**
     * 入库单号
     */
    private String asnNumber;
    /**
     * 入库单状态代码
     */
    private WmsAnsStatusEnums asnStatus;
    /**
     * 入库单状态中文描述
     */
    private String asnStatusText;
    /**
     * 入库单类型
     */
    private String asnType;
    /**
     * 负责人
     */
    private String chargePerson;
    /**
     * 负责时间
     */
    private OffsetDateTime chargeTime;
    /**
     * 完成时间
     */
    private OffsetDateTime completionTime;
    /**
     * 创建时间
     */
    private OffsetDateTime createTime;
    /**
     * 创建用户
     */
    private String createUser;
    /**
     * 配送员
     */
    private String deliveryer;
    /**
     * 配送方式
     */
    private String deliveryStyle;
    /**
     * 配送时间
     */
    private OffsetDateTime deliveryTime;
    /**
     * 镶嵌数量
     */
    private Long enchaseNum;
    /**
     * 镶嵌类型
     */
    private String enchaseType;
    /**
     * 快递公司
     */
    private String expressCompany;
    /**
     * 快递单号
     */
    private String expressNumber;
    /**
     * 费用状态
     */
    private Long feeStatus;
    /**
     * 扩展字段5
     */
    private String field5;
    /**
     * 是否已审批
     */
    private Boolean isApproval;
    /**
     * 是否完成
     */
    private String isComplete;
    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;
    /**
     * 修改用户
     */
    private String modifyUser;
    /**
     * 办事处
     */
    private String office;
    /**
     * 上架费用
     */
    private Double onFrameFee;
    /**
     * wms采购单号
     */
    private String purchaseNo;
    /**
     * 到达开始时间
     */
    private LocalDateTime reachStartTime;
    /**
     * 参考号
     */
    private String refNo;
    /**
     * 备注
     */
    private String remark;
    /**
     * 退货状态
     */
    private Boolean returnsStatus;
    /**
     * 服务编号
     */
    private String serviceNo;
    /**
     * 样式类型
     */
    private Long style;
    /**
     * 运单号
     */
    private String trackingNo;
    /**
     * 运输方式
     */
    private String transportWay;
    /**
     * 是否作废
     */
    private Boolean voidFlag;
    /**
     * 仓库地址
     */
    private String warehouseAddress;

    /**
     * 入库单明细
     */
    @Data
    public static class ASNDetail {

        /**
         * 明细ID
         */
        private Long asnDetailId;
        /**
         * 主表ID
         */
        private Long asnMainId;
        /**
         * 中文名称
         */
        private String cnName;
        /**
         * 创建时间
         */
        private OffsetDateTime createTime;
        /**
         * 英文名称
         */
        private String enName;
        /**
         * 过期日期
         */
        private LocalDate expiredDate;
        /**
         * 实收数量
         */
        private Long factQuantity;
        /**
         * 实际重量
         */
        private Double factWeight;
        /**
         * 商品ID
         */
        private Long goodsId;
        /**
         * 商品状态
         */
        private String goodsStatus;
        /**
         * 标签费用
         */
        private Double labelFee;
        /**
         * 最后取数量
         */
        private Long lastTakedQuantity;
        /**
         * 修改时间
         */
        private OffsetDateTime modifyTime;
        /**
         * 不合格原因
         */
        private String notOkReason;
        /**
         * 合格数量
         */
        private Long okQuantity;
        /**
         * 包装费用
         */
        private Double packetFee;
        /**
         * 单价
         */
        private Double price;
        /**
         * 生产日期
         */
        private LocalDate produceDate;
        /**
         * 收货时间
         */
        private OffsetDateTime receiveTime;
        /**
         * 收货人
         */
        private String receiveUser;
        /**
         * 推荐库位
         */
        private String refereeStock;
        /**
         * 参考重量
         */
        private Double refWeight;
        /**
         * 备注
         */
        private String remark;
        /**
         * 应收数量
         */
        private Long shouldQuantity;
        /**
         * 已取数量
         */
        private Long takedQuantity;
        /**
         * 是否作废
         */
        private Boolean voidFlag;
    }
}
