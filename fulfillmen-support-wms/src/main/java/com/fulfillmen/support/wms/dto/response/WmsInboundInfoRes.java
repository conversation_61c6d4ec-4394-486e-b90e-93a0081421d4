package com.fulfillmen.support.wms.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 入库单信息查询请求
 *
 * <AUTHOR>
 * @date 2025/7/31 16:48
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WmsInboundInfoReq {

    /**
     * 入库单主表ID（最高优先级） 如果提供此参数，将直接使用该ID查询，忽略其他查询条件
     */
    private String asnMainId;
    /**
     * 入库单号 当未提供asnMainId时，优先使用此条件查询主单
     */
    private String asnNumber;
    /**
     * 客户编码，用于标识客户身份
     */
    private String cusCode;
    /**
     * 采购单号 当未提供其他查询条件时，使用此条件查询主单
     */
    private String purchaseNo;
    /**
     * 运单号 当未提供asnMainId和asnNumber时，使用此条件查询主单
     */
    private String trackingNo;
}
