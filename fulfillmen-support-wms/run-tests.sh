#!/bin/bash

# WMS API Client 测试运行脚本
# 用于运行新增接口的单元测试和集成测试

set -e

echo "🚀 开始运行 WMS API Client 测试..."
echo "=================================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Maven是否可用
if ! command -v mvn &> /dev/null; then
    echo -e "${RED}❌ Maven 未安装或不在PATH中${NC}"
    exit 1
fi

echo -e "${BLUE}📋 测试配置信息:${NC}"
echo "- 项目路径: $(pwd)"
echo "- Maven版本: $(mvn -version | head -1)"
echo "- Java版本: $(java -version 2>&1 | head -1)"
echo ""

# 1. 编译项目
echo -e "${BLUE}🔨 编译项目...${NC}"
if mvn clean compile -q; then
    echo -e "${GREEN}✅ 项目编译成功${NC}"
else
    echo -e "${RED}❌ 项目编译失败${NC}"
    exit 1
fi

echo ""

# 2. 运行单元测试
echo -e "${BLUE}🧪 运行单元测试...${NC}"
echo "测试类: WmsApiClientTest"

# 运行特定的新增接口测试方法
TEST_METHODS=(
    "testRefundAccountInfoByPurchase_Success"
    "testRefundAccountInfoByPurchase_WmsApiException"
    "testDeductAccountInfoByPurchase_Success"
    "testInboundCreateByPurchase_Success"
    "testInboundCreateByPurchase_InvalidParameters"
    "testInboundCreateByPurchase_WmsApiException"
    "testInboundUpdateByPurchase_Success"
    "testInboundQueryPage_Success"
    "testInboundQueryPage_EmptyResult"
    "testInboundQuery_Success"
    "testInboundQuery_NotFound"
)

UNIT_TEST_PASSED=0
UNIT_TEST_FAILED=0

for method in "${TEST_METHODS[@]}"; do
    echo -n "  - 运行 $method ... "
    if mvn test -Dtest=WmsApiClientTest#$method -q > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 通过${NC}"
        ((UNIT_TEST_PASSED++))
    else
        echo -e "${RED}❌ 失败${NC}"
        ((UNIT_TEST_FAILED++))
    fi
done

echo ""
echo -e "${BLUE}📊 单元测试结果:${NC}"
echo "- 通过: ${UNIT_TEST_PASSED}"
echo "- 失败: ${UNIT_TEST_FAILED}"
echo "- 总计: $((UNIT_TEST_PASSED + UNIT_TEST_FAILED))"

if [ $UNIT_TEST_FAILED -gt 0 ]; then
    echo -e "${YELLOW}⚠️  部分单元测试失败，继续运行集成测试...${NC}"
fi

echo ""

# 3. 运行集成测试
echo -e "${BLUE}🔗 运行集成测试...${NC}"
echo "测试类: WmsApiClientIntegrationTest"

# 运行特定的新增接口集成测试方法
INTEGRATION_TEST_METHODS=(
    "testRefundAccountInfoByPurchase_EndToEnd"
    "testDeductAccountInfoByPurchase_EndToEnd"
    "testAccountChargeOperations_NetworkError"
    "testInboundCreateByPurchase_EndToEnd"
    "testInboundUpdateByPurchase_EndToEnd"
    "testInboundQueryPage_EndToEnd"
    "testInboundQuery_EndToEnd"
    "testInboundManagement_NetworkError"
    "testInboundManagement_BusinessError"
)

INTEGRATION_TEST_PASSED=0
INTEGRATION_TEST_FAILED=0

for method in "${INTEGRATION_TEST_METHODS[@]}"; do
    echo -n "  - 运行 $method ... "
    if mvn test -Dtest=WmsApiClientIntegrationTest#$method -q > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 通过${NC}"
        ((INTEGRATION_TEST_PASSED++))
    else
        echo -e "${RED}❌ 失败${NC}"
        ((INTEGRATION_TEST_FAILED++))
    fi
done

echo ""
echo -e "${BLUE}📊 集成测试结果:${NC}"
echo "- 通过: ${INTEGRATION_TEST_PASSED}"
echo "- 失败: ${INTEGRATION_TEST_FAILED}"
echo "- 总计: $((INTEGRATION_TEST_PASSED + INTEGRATION_TEST_FAILED))"

echo ""

# 4. 运行完整测试套件（可选）
echo -e "${BLUE}🎯 运行完整测试套件...${NC}"
if mvn test -Dtest=WmsApiClientTest,WmsApiClientIntegrationTest -q; then
    echo -e "${GREEN}✅ 完整测试套件运行成功${NC}"
    FULL_SUITE_SUCCESS=true
else
    echo -e "${RED}❌ 完整测试套件运行失败${NC}"
    FULL_SUITE_SUCCESS=false
fi

echo ""

# 5. 生成测试报告
echo -e "${BLUE}📋 生成测试报告...${NC}"

TOTAL_PASSED=$((UNIT_TEST_PASSED + INTEGRATION_TEST_PASSED))
TOTAL_FAILED=$((UNIT_TEST_FAILED + INTEGRATION_TEST_FAILED))
TOTAL_TESTS=$((TOTAL_PASSED + TOTAL_FAILED))

cat > test-report.md << EOF
# WMS API Client 新增接口测试报告

## 测试概览

- **测试时间**: $(date)
- **测试范围**: 新增API接口（账户费用退款、入库管理）
- **总测试数**: ${TOTAL_TESTS}
- **通过数**: ${TOTAL_PASSED}
- **失败数**: ${TOTAL_FAILED}
- **成功率**: $(( TOTAL_PASSED * 100 / TOTAL_TESTS ))%

## 单元测试结果

- **通过**: ${UNIT_TEST_PASSED}
- **失败**: ${UNIT_TEST_FAILED}
- **覆盖接口**:
  - refundAccountInfoByPurchase (账户费用退款)
  - deductAccountInfoByPurchase (账户费用扣减)
  - inboundCreateByPurchase (创建入库单)
  - inboundUpdateByPurchase (更新入库单)
  - inboundQueryPage (分页查询入库单)
  - inboundQuery (查询入库单详情)

## 集成测试结果

- **通过**: ${INTEGRATION_TEST_PASSED}
- **失败**: ${INTEGRATION_TEST_FAILED}
- **测试场景**:
  - 端到端功能测试
  - 网络异常处理
  - 业务异常处理
  - HTTP请求验证
  - 响应数据验证

## 测试覆盖范围

### 账户费用管理
- ✅ 正常退款流程
- ✅ 正常扣减流程
- ✅ 参数验证
- ✅ 异常处理
- ✅ 网络错误处理

### 入库管理
- ✅ 创建入库单
- ✅ 更新入库单
- ✅ 分页查询
- ✅ 详情查询
- ✅ 参数验证
- ✅ 异常处理

## 建议

1. 如有测试失败，请检查：
   - 网络连接
   - 测试数据准备
   - Mock服务配置
   - 依赖版本兼容性

2. 持续集成建议：
   - 将测试集成到CI/CD流程
   - 定期运行回归测试
   - 监控测试覆盖率

EOF

echo -e "${GREEN}✅ 测试报告已生成: test-report.md${NC}"

echo ""
echo "=================================================="

# 6. 总结
if [ $TOTAL_FAILED -eq 0 ] && [ "$FULL_SUITE_SUCCESS" = true ]; then
    echo -e "${GREEN}🎉 所有测试通过！新增接口测试完成。${NC}"
    exit 0
elif [ $TOTAL_FAILED -eq 0 ]; then
    echo -e "${YELLOW}⚠️  新增接口测试通过，但完整测试套件有问题。${NC}"
    exit 1
else
    echo -e "${RED}❌ 有 ${TOTAL_FAILED} 个测试失败，请检查并修复。${NC}"
    echo ""
    echo "详细错误信息请查看Maven测试输出。"
    echo "建议运行: mvn test -Dtest=WmsApiClientTest,WmsApiClientIntegrationTest"
    exit 1
fi
