# WMS API Client 新增接口测试指南

本文档详细说明了为 WmsApiClient 新增接口编写的测试用例，包括单元测试和集成测试的运行方法、测试覆盖范围以及最佳实践。

## 📋 新增接口概览

### 1. 账户费用管理接口
- **refundAccountInfoByPurchase** - 账户费用退款
- **deductAccountInfoByPurchase** - 账户费用扣减

### 2. 入库管理接口
- **inboundCreateByPurchase** - 创建入库单
- **inboundUpdateByPurchase** - 更新入库单
- **inboundQueryPage** - 分页查询入库单
- **inboundQuery** - 查询入库单详情

## 🧪 测试结构

```
fulfillmen-support-wms/
├── src/test/java/com/fulfillmen/support/wms/
│   ├── api/
│   │   ├── WmsApiClientTest.java                    # 单元测试
│   │   └── WmsApiClientIntegrationTest.java         # 集成测试
│   └── testutil/
│       └── WmsTestDataFactory.java                  # 测试数据工厂
├── src/test/resources/
│   └── application-test.yml                         # 测试配置
├── run-tests.sh                                     # 测试运行脚本
└── TEST_README.md                                   # 本文档
```

## 🚀 快速开始

### 1. 运行所有新增接口测试

```bash
# 使用测试脚本（推荐）
chmod +x run-tests.sh
./run-tests.sh

# 或使用Maven直接运行
mvn test -Dtest=WmsApiClientTest,WmsApiClientIntegrationTest
```

### 2. 运行特定测试类

```bash
# 只运行单元测试
mvn test -Dtest=WmsApiClientTest

# 只运行集成测试
mvn test -Dtest=WmsApiClientIntegrationTest
```

### 3. 运行特定测试方法

```bash
# 运行账户退款测试
mvn test -Dtest=WmsApiClientTest#testRefundAccountInfoByPurchase_Success

# 运行入库单创建测试
mvn test -Dtest=WmsApiClientTest#testInboundCreateByPurchase_Success
```

## 📊 测试覆盖范围

### 单元测试覆盖

#### 账户费用管理
- ✅ **正常流程测试**
  - 成功退款场景
  - 成功扣减场景
  - 响应数据验证
  - API调用验证

- ✅ **异常流程测试**
  - WMS API异常处理
  - 网络异常处理
  - 业务异常处理

- ✅ **参数验证测试**
  - 必填参数验证
  - 参数格式验证
  - 边界值测试

#### 入库管理
- ✅ **CRUD操作测试**
  - 创建入库单
  - 更新入库单
  - 分页查询
  - 详情查询

- ✅ **数据验证测试**
  - 请求参数验证
  - 响应数据完整性
  - 分页数据正确性

- ✅ **异常处理测试**
  - 参数验证失败
  - 业务逻辑异常
  - 系统异常处理

### 集成测试覆盖

#### 端到端测试
- ✅ **完整业务流程**
  - HTTP请求构建
  - 请求头验证
  - 响应解析
  - 错误处理

- ✅ **网络层测试**
  - 连接超时处理
  - 读取超时处理
  - 网络异常恢复

- ✅ **安全性测试**
  - 签名验证
  - 时间戳验证
  - 随机数验证

## 🛠️ 测试工具和框架

### 核心依赖
- **JUnit 5** - 测试框架
- **Mockito** - Mock框架
- **MockWebServer** - HTTP Mock服务器
- **Spring Boot Test** - Spring测试支持

### 测试工具类
- **WmsTestDataFactory** - 测试数据生成
- **WmsResponseHandler** - 响应处理Mock
- **BaseIntegrationTest** - 集成测试基类

## 📝 测试最佳实践

### 1. 测试命名规范

```java
// 格式：test{方法名}_{场景}
@Test
void testRefundAccountInfoByPurchase_Success() { }

@Test
void testInboundCreateByPurchase_InvalidParameters() { }

@Test
void testInboundQuery_NotFound() { }
```

### 2. 测试数据管理

```java
// 使用测试数据工厂
WmsAccountChargeV2Req request = WmsTestDataFactory.createRefundRequest(
    "TEST001", "ORDER001", 100.0);

// 使用Builder模式
WmsCreateInboundOrderReq request = WmsCreateInboundOrderReq.builder()
    .cusCode("TEST001")
    .purchaseNo("PO001")
    .build();
```

### 3. 断言策略

```java
// 验证核心业务数据
assertNotNull(result);
assertEquals("TEST001", result.getCusCode());
assertEquals("ORDER001", result.getOrderNo());

// 验证API调用
verify(wmsFulfillmenAPI).refundAccountInfoByPurchase(request);

// 验证异常处理
assertThrows(WmsApiException.class, () -> {
    wmsApiClient.refundAccountInfoByPurchase(invalidRequest);
});
```

### 4. Mock策略

```java
// Mock外部API调用
when(wmsFulfillmenAPI.refundAccountInfoByPurchase(request))
    .thenReturn(Mono.just(createSuccessResponse(expectedData)));

// Mock静态方法
try (MockedStatic<WmsResponseHandler> mockedHandler = mockStatic(WmsResponseHandler.class)) {
    mockedHandler.when(() -> WmsResponseHandler.handleWmsResponse(any(), anyString(), any()))
        .thenReturn(expectedData);
}
```

## 🔍 测试执行和调试

### 1. 查看测试报告

```bash
# 运行测试后查看报告
cat test-report.md

# 查看详细的Maven测试报告
open target/surefire-reports/index.html
```

### 2. 调试测试

```bash
# 启用调试模式
mvn test -Dtest=WmsApiClientTest -Dmaven.surefire.debug

# 查看详细日志
mvn test -Dtest=WmsApiClientTest -Dlogging.level.com.fulfillmen.support.wms=DEBUG
```

### 3. 性能测试

```bash
# 运行性能测试（如果启用）
mvn test -Dtest=WmsApiClientTest -Dperformance.test.enabled=true
```

## 📈 持续集成

### 1. CI/CD集成

```yaml
# GitHub Actions 示例
- name: Run WMS API Tests
  run: |
    chmod +x run-tests.sh
    ./run-tests.sh

- name: Upload Test Results
  uses: actions/upload-artifact@v2
  with:
    name: test-results
    path: test-report.md
```

### 2. 测试覆盖率

```bash
# 生成覆盖率报告
mvn test jacoco:report

# 查看覆盖率
open target/site/jacoco/index.html
```

## 🐛 常见问题和解决方案

### 1. 测试失败排查

**问题**: 网络连接超时
```bash
# 解决方案：检查Mock服务器配置
# 确保 application-test.yml 中的端口配置正确
```

**问题**: Mock数据不匹配
```bash
# 解决方案：检查测试数据工厂
# 确保 WmsTestDataFactory 生成的数据格式正确
```

### 2. 依赖问题

**问题**: 找不到测试依赖
```bash
# 解决方案：更新Maven依赖
mvn clean install -U
```

### 3. 配置问题

**问题**: 测试配置不生效
```bash
# 解决方案：检查配置文件
# 确保 application-test.yml 在正确的位置
```

## 📚 参考资料

- [JUnit 5 用户指南](https://junit.org/junit5/docs/current/user-guide/)
- [Mockito 文档](https://javadoc.io/doc/org.mockito/mockito-core/latest/org/mockito/Mockito.html)
- [Spring Boot 测试指南](https://spring.io/guides/gs/testing-web/)
- [MockWebServer 使用指南](https://github.com/square/okhttp/tree/master/mockwebserver)

## 🤝 贡献指南

1. **添加新测试**
   - 遵循现有的命名规范
   - 使用测试数据工厂
   - 添加适当的注释和文档

2. **修改现有测试**
   - 确保向后兼容
   - 更新相关文档
   - 运行完整测试套件

3. **报告问题**
   - 提供详细的错误信息
   - 包含重现步骤
   - 建议解决方案

---

**注意**: 请确保在提交代码前运行完整的测试套件，确保所有测试通过。
