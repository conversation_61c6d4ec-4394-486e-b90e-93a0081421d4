package com.fulfillmen.shop.dao.mapper;

import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.starter.data.mp.base.BaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2025/7/30 18:57
 * @description: todo
 * @since 1.0.0
 */
@Mapper
public interface TzOrderSupplierMapper extends BaseMapper<TzOrderSupplier> {

    /**
     * 根据外部订单ID查询供应商订单
     * <pre>
     * 此方法将不使用 租户 Id 过滤
     * </pre>
     *
     * @param platformOrderId 外部订单ID 例如 1688 orderId
     * @return 供应商订单
     */
    TzOrderSupplier findByPlatformOrderIdAndIgnoreTenantId(String platformOrderId);

    /**
     * 根据外部订单ID查询供应商订单
     * <pre>
     * 此方法将不使用 租户 Id 过滤
     * </pre>
     *
     * @param purchaseOrderId 采购订单id
     * @return 供应商订单
     */
    List<TzOrderSupplier> listByPurchaseOrderIdAndIgnoreTenantId(Long purchaseOrderId);
}