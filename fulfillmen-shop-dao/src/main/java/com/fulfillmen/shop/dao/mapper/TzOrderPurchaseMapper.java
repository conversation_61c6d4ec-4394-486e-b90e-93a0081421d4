/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.dao.mapper;

import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.starter.data.mp.base.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2025/7/30 11:39
 * @description: todo
 * @since 1.0.0
 */
@Mapper
public interface TzOrderPurchaseMapper extends BaseMapper<TzOrderPurchase> {

    /**
     * 根据采购订单ID查询采购订单
     * <pre>
     * 忽略租户ID
     * </pre>
     *
     * @param id 采购订单ID
     * @return 采购订单
     */
    TzOrderPurchase findByIdAndIgnoreTenantId(Long id);
}