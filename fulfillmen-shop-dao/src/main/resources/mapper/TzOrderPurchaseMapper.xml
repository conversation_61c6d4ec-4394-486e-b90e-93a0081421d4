<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper">
  <resultMap id="BaseResultMap" type="com.fulfillmen.shop.domain.entity.TzOrderPurchase">
    <!--@mbg.generated-->
    <!--@Table tz_order_purchase-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="buyer_id" jdbcType="BIGINT" property="buyerId" />
    <result column="buyer_type" jdbcType="TINYINT" property="buyerType" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
    <result column="paid_date" jdbcType="TIMESTAMP" property="paidDate" />
    <result column="paid_transaction_no" jdbcType="VARCHAR" property="paidTransactionNo" />
    <result column="order_completed_date" jdbcType="TIMESTAMP" property="orderCompletedDate" />
    <result column="service_fee" jdbcType="DECIMAL" property="serviceFee" />
    <result column="exchange_rate_snapshot" jdbcType="DECIMAL" property="exchangeRateSnapshot" />
    <result column="customer_goods_amount" jdbcType="DECIMAL" property="customerGoodsAmount" />
    <result column="customer_total_freight" jdbcType="DECIMAL" property="customerTotalFreight" />
    <result column="customer_total_amount" jdbcType="DECIMAL" property="customerTotalAmount" />
    <result column="payable_coupon_amount" jdbcType="DECIMAL" property="payableCouponAmount" />
    <result column="payable_discount_amount" jdbcType="DECIMAL" property="payableDiscountAmount" />
    <result column="payable_plus_discount_amount" jdbcType="DECIMAL" property="payablePlusDiscountAmount" />
    <result column="payable_goods_amount" jdbcType="DECIMAL" property="payableGoodsAmount" />
    <result column="payable_freight_total" jdbcType="DECIMAL" property="payableFreightTotal" />
    <result column="payable_amount_total" jdbcType="DECIMAL" property="payableAmountTotal" />
    <result column="actual_payment_amount" jdbcType="DECIMAL" property="actualPaymentAmount" />
    <result column="actual_payment_goods_amount" jdbcType="DECIMAL" property="actualPaymentGoodsAmount" />
    <result column="actual_payment_freight_amount" jdbcType="DECIMAL" property="actualPaymentFreightAmount" />
    <result column="actual_payment_discount_amount" jdbcType="DECIMAL" property="actualPaymentDiscountAmount" />
    <result column="actual_payment_coupon_amount" jdbcType="DECIMAL" property="actualPaymentCouponAmount" />
    <result column="actual_payment_plus_amount" jdbcType="DECIMAL" property="actualPaymentPlusAmount" />
    <result column="recipient_warehouse_id" jdbcType="BIGINT" property="recipientWarehouseId" />
    <result column="recipient_warehouse_name" jdbcType="VARCHAR" property="recipientWarehouseName" />
    <result column="delivery_address" jdbcType="LONGVARCHAR" property="deliveryAddress" />
    <result column="postal_code" jdbcType="VARCHAR" property="postalCode" />
    <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="consignee_name" jdbcType="VARCHAR" property="consigneeName" />
    <result column="consignee_phone" jdbcType="VARCHAR" property="consigneePhone" />
    <result column="supplier_count" jdbcType="INTEGER" property="supplierCount" />
    <result column="line_item_count" jdbcType="INTEGER" property="lineItemCount" />
    <result column="completed_supplier_count" jdbcType="INTEGER" property="completedSupplierCount" />
    <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity" />
    <result column="purchase_notes" jdbcType="LONGVARCHAR" property="purchaseNotes" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, purchase_order_no, buyer_id, buyer_type, order_status, order_date, paid_date, 
    paid_transaction_no, order_completed_date, service_fee, exchange_rate_snapshot, customer_goods_amount, 
    customer_total_freight, customer_total_amount, payable_coupon_amount, payable_discount_amount, 
    payable_plus_discount_amount, payable_goods_amount, payable_freight_total, payable_amount_total, 
    actual_payment_amount, actual_payment_goods_amount, actual_payment_freight_amount, 
    actual_payment_discount_amount, actual_payment_coupon_amount, actual_payment_plus_amount, 
    recipient_warehouse_id, recipient_warehouse_name, delivery_address, postal_code, 
    country_code, province, city, district, consignee_name, consignee_phone, supplier_count, 
    line_item_count, completed_supplier_count, total_quantity, purchase_notes, tenant_id, 
    is_deleted, revision, gmt_created, gmt_modified
  </sql>

  <select id="findByIdAndIgnoreTenantId" resultMap="BaseResultMap">
    select *
    from tz_order_purchase
    where id = #{id}
    and is_deleted = 0
  </select>
</mapper>