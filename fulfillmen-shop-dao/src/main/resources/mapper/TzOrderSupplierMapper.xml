<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper">
  <resultMap id="BaseResultMap" type="com.fulfillmen.shop.domain.entity.TzOrderSupplier">
    <!--@mbg.generated-->
    <!--@Table tz_order_supplier-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="purchase_order_id" jdbcType="BIGINT" property="purchaseOrderId" />
    <result column="supplier_order_no" jdbcType="VARCHAR" property="supplierOrderNo" />
    <result column="platform_code" jdbcType="VARCHAR" property="platformCode" />
    <result column="supplier_id" jdbcType="VARCHAR" property="supplierId" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="supplier_shop_name" jdbcType="VARCHAR" property="supplierShopName" />
    <result column="metadata_json" jdbcType="VARCHAR" property="metadataJson" />
    <result column="is_multiple_orders" jdbcType="TINYINT" property="isMultipleOrders" />
    <result column="external_sync_status" jdbcType="TINYINT" property="externalSyncStatus" />
    <result column="external_sync_failed_message" jdbcType="VARCHAR" property="externalSyncFailedMessage" />
    <result column="platform_order_id" jdbcType="VARCHAR" property="platformOrderId" />
    <result column="platform_order_no" jdbcType="VARCHAR" property="platformOrderNo" />
    <result column="platform_trade_no" jdbcType="VARCHAR" property="platformTradeNo" />
    <result column="platform_pay_url" jdbcType="VARCHAR" property="platformPayUrl" />
    <result column="platform_trade_type" jdbcType="VARCHAR" property="platformTradeType" />
    <result column="platform_trade_type_desc" jdbcType="VARCHAR" property="platformTradeTypeDesc" />
    <result column="platform_tracking_no" jdbcType="VARCHAR" property="platformTrackingNo" />
    <result column="wms_purchase_order_no" jdbcType="VARCHAR" property="wmsPurchaseOrderNo" />
    <result column="wms_sync_status" jdbcType="TINYINT" property="wmsSyncStatus" />
    <result column="wms_failed_message" jdbcType="VARCHAR" property="wmsFailedMessage" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
    <result column="payment_date" jdbcType="TIMESTAMP" property="paymentDate" />
    <result column="procurement_date" jdbcType="TIMESTAMP" property="procurementDate" />
    <result column="shipped_date" jdbcType="TIMESTAMP" property="shippedDate" />
    <result column="delivered_date" jdbcType="TIMESTAMP" property="deliveredDate" />
    <result column="completed_date" jdbcType="TIMESTAMP" property="completedDate" />
    <result column="cancelled_date" jdbcType="TIMESTAMP" property="cancelledDate" />
    <result column="customer_goods_amount" jdbcType="DECIMAL" property="customerGoodsAmount" />
    <result column="customer_freight_amount" jdbcType="DECIMAL" property="customerFreightAmount" />
    <result column="customer_total_amount" jdbcType="DECIMAL" property="customerTotalAmount" />
    <result column="service_fee" jdbcType="DECIMAL" property="serviceFee" />
    <result column="payable_goods_amount" jdbcType="DECIMAL" property="payableGoodsAmount" />
    <result column="payable_freight_amount" jdbcType="DECIMAL" property="payableFreightAmount" />
    <result column="payable_amount_total" jdbcType="DECIMAL" property="payableAmountTotal" />
    <result column="payable_discount_amount" jdbcType="DECIMAL" property="payableDiscountAmount" />
    <result column="payable_coupon_amount" jdbcType="DECIMAL" property="payableCouponAmount" />
    <result column="payable_plus_discount_amount" jdbcType="DECIMAL" property="payablePlusDiscountAmount" />
    <result column="actual_payment_amount" jdbcType="DECIMAL" property="actualPaymentAmount" />
    <result column="actual_payment_goods_amount" jdbcType="DECIMAL" property="actualPaymentGoodsAmount" />
    <result column="actual_payment_freight_amount" jdbcType="DECIMAL" property="actualPaymentFreightAmount" />
    <result column="actual_payment_discount_amount" jdbcType="DECIMAL" property="actualPaymentDiscountAmount" />
    <result column="actual_payment_coupon_amount" jdbcType="DECIMAL" property="actualPaymentCouponAmount" />
    <result column="actual_payment_plus_amount" jdbcType="DECIMAL" property="actualPaymentPlusAmount" />
    <result column="line_item_count" jdbcType="INTEGER" property="lineItemCount" />
    <result column="completed_item_count" jdbcType="INTEGER" property="completedItemCount" />
    <result column="supplier_notes" jdbcType="VARCHAR" property="supplierNotes" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, purchase_order_id, supplier_order_no, platform_code, supplier_id, supplier_name, 
    supplier_shop_name, metadata_json, is_multiple_orders, external_sync_status, external_sync_failed_message, 
    platform_order_id, platform_order_no, platform_trade_no, platform_pay_url, platform_trade_type, 
    platform_trade_type_desc, platform_tracking_no, wms_purchase_order_no, wms_sync_status, 
    wms_failed_message, `status`, order_date, payment_date, procurement_date, shipped_date, 
    delivered_date, completed_date, cancelled_date, customer_goods_amount, customer_freight_amount, 
    customer_total_amount, service_fee, payable_goods_amount, payable_freight_amount, 
    payable_amount_total, payable_discount_amount, payable_coupon_amount, payable_plus_discount_amount, 
    actual_payment_amount, actual_payment_goods_amount, actual_payment_freight_amount, 
    actual_payment_discount_amount, actual_payment_coupon_amount, actual_payment_plus_amount, 
    line_item_count, completed_item_count, supplier_notes, tenant_id, is_deleted, revision, 
    created_by, gmt_created, gmt_modified
  </sql>

  <select id="findByPlatformOrderIdAndIgnoreTenantId" resultMap="BaseResultMap">
    select *
    from tz_order_supplier t
    where platform_order_id = #{platformOrderId}
    and is_deleted = 0
  </select>

  <select id="listByPurchaseOrderIdAndIgnoreTenantId" resultMap="BaseResultMap">
    select *
    from tz_order_supplier t
    where purchase_order_id = #{purchaseOrderId}
      and is_deleted = 0
  </select>
</mapper>