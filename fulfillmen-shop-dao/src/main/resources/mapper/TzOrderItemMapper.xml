<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fulfillmen.shop.dao.mapper.TzOrderItemMapper">
  <resultMap id="BaseResultMap" type="com.fulfillmen.shop.domain.entity.TzOrderItem">
    <!--@mbg.generated-->
    <!--@Table tz_order_item-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="purchase_order_id" jdbcType="BIGINT" property="purchaseOrderId" />
    <result column="supplier_order_id" jdbcType="BIGINT" property="supplierOrderId" />
    <result column="platform_order_id" jdbcType="VARCHAR" property="platformOrderId" />
    <result column="line_number" jdbcType="INTEGER" property="lineNumber" />
    <result column="product_spu_id" jdbcType="BIGINT" property="productSpuId" />
    <result column="product_sku_id" jdbcType="BIGINT" property="productSkuId" />
    <result column="platform_product_id" jdbcType="VARCHAR" property="platformProductId" />
    <result column="platform_sku_id" jdbcType="VARCHAR" property="platformSkuId" />
    <result column="platform_spec_id" jdbcType="VARCHAR" property="platformSpecId" />
    <result column="platform_item_id" jdbcType="VARCHAR" property="platformItemId" />
    <result column="platform_snapshot_url" jdbcType="VARCHAR" property="platformSnapshotUrl" />
    <result column="platform_metadata" jdbcType="VARCHAR" property="platformMetadata" />
    <result column="product_title" jdbcType="VARCHAR" property="productTitle" />
    <result column="product_title_en" jdbcType="VARCHAR" property="productTitleEn" />
    <result column="product_link" jdbcType="VARCHAR" property="productLink" />
    <result column="sku_specs" jdbcType="VARCHAR" property="skuSpecs" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    <result column="product_image_url" jdbcType="VARCHAR" property="productImageUrl" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="actual_payment_amount" jdbcType="DECIMAL" property="actualPaymentAmount" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="unit_en" jdbcType="VARCHAR" property="unitEn" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="logistics_status" jdbcType="TINYINT" property="logisticsStatus" />
    <result column="error_code" jdbcType="INTEGER" property="errorCode" />
    <result column="error_message" jdbcType="VARCHAR" property="errorMessage" />
    <result column="external_item_id" jdbcType="VARCHAR" property="externalItemId" />
    <result column="weight" jdbcType="VARCHAR" property="weight" />
    <result column="weight_unit" jdbcType="VARCHAR" property="weightUnit" />
    <result column="completed_datetime" jdbcType="TIMESTAMP" property="completedDatetime" />
    <result column="is_single_item" jdbcType="TINYINT" property="isSingleItem" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, purchase_order_id, supplier_order_id, platform_order_id, line_number, product_spu_id, 
    product_sku_id, platform_product_id, platform_sku_id, platform_spec_id, platform_item_id, 
    platform_snapshot_url, platform_metadata, product_title, product_title_en, product_link, 
    sku_specs, product_image_url, price, quantity, total_amount, actual_payment_amount, 
    unit, unit_en, `status`, logistics_status, error_code, error_message, external_item_id, 
    weight, weight_unit, completed_datetime, is_single_item, tenant_id, is_deleted, revision, 
    gmt_created, gmt_modified
  </sql>
</mapper>